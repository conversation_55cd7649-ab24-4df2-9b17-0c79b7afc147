/**
 * @license lucide-react v0.535.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M17 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 1-1.732", key: "1vyzll" }],
  ["path", { d: "m22 5.5-6.419 4.179a2 2 0 0 1-2.162 0L7 5.5", key: "k7ramc" }],
  ["rect", { x: "7", y: "3", width: "15", height: "12", rx: "2", key: "17196g" }]
];
const Mails = createLucideIcon("mails", __iconNode);

export { __iconNode, Mails as default };
//# sourceMappingURL=mails.js.map
