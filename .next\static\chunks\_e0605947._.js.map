{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/SplitText/SplitText.tsx"], "sourcesContent": ["import React, { useRef, useEffect } from \"react\";\nimport { gsap } from \"gsap\";\nimport { ScrollTrigger } from \"gsap/ScrollTrigger\";\nimport { SplitText as GSAPSplitText } from \"gsap/SplitText\";\n\ngsap.registerPlugin(ScrollTrigger, GSAPSplitText);\n\nexport interface SplitTextProps {\n  text: string;\n  className?: string;\n  delay?: number;\n  duration?: number;\n  ease?: string | ((t: number) => number);\n  splitType?: \"chars\" | \"words\" | \"lines\" | \"words, chars\";\n  from?: gsap.TweenVars;\n  to?: gsap.TweenVars;\n  threshold?: number;\n  rootMargin?: string;\n  textAlign?: React.CSSProperties[\"textAlign\"];\n  onLetterAnimationComplete?: () => void;\n}\n\nconst SplitText: React.FC<SplitTextProps> = ({\n  text,\n  className = \"\",\n  delay = 100,\n  duration = 0.6,\n  ease = \"power3.out\",\n  splitType = \"chars\",\n  from = { opacity: 0, y: 40 },\n  to = { opacity: 1, y: 0 },\n  threshold = 0.1,\n  rootMargin = \"-100px\",\n  textAlign = \"center\",\n  onLetterAnimationComplete,\n}) => {\n  const ref = useRef<HTMLParagraphElement>(null);\n  const animationCompletedRef = useRef(false);\n  const scrollTriggerRef = useRef<ScrollTrigger | null>(null);\n\n  useEffect(() => {\n    if (typeof window === \"undefined\" || !ref.current || !text) return;\n\n    const el = ref.current;\n    \n    animationCompletedRef.current = false;\n\n    const absoluteLines = splitType === \"lines\";\n    if (absoluteLines) el.style.position = \"relative\";\n\n    let splitter: GSAPSplitText;\n    try {\n      splitter = new GSAPSplitText(el, {\n        type: splitType,\n        absolute: absoluteLines,\n        linesClass: \"split-line\",\n      });\n    } catch (error) {\n      console.error(\"Failed to create SplitText:\", error);\n      return;\n    }\n\n    let targets: Element[];\n    switch (splitType) {\n      case \"lines\":\n        targets = splitter.lines;\n        break;\n      case \"words\":\n        targets = splitter.words;\n        break;\n      case \"chars\":\n        targets = splitter.chars;\n        break;\n      default:\n        targets = splitter.chars;\n    }\n\n    if (!targets || targets.length === 0) {\n      console.warn(\"No targets found for SplitText animation\");\n      splitter.revert();\n      return;\n    }\n\n    targets.forEach((t) => {\n      (t as HTMLElement).style.willChange = \"transform, opacity\";\n    });\n\n    const startPct = (1 - threshold) * 100;\n    const marginMatch = /^(-?\\d+(?:\\.\\d+)?)(px|em|rem|%)?$/.exec(rootMargin);\n    const marginValue = marginMatch ? parseFloat(marginMatch[1]) : 0;\n    const marginUnit = marginMatch ? (marginMatch[2] || \"px\") : \"px\";\n    const sign = marginValue < 0 ? `-=${Math.abs(marginValue)}${marginUnit}` : `+=${marginValue}${marginUnit}`;\n    const start = `top ${startPct}%${sign}`;\n\n    const tl = gsap.timeline({\n      scrollTrigger: {\n        trigger: el,\n        start,\n        toggleActions: \"play none none none\",\n        once: true,\n        onToggle: (self) => {\n          scrollTriggerRef.current = self;\n        },\n      },\n      smoothChildTiming: true,\n      onComplete: () => {\n        animationCompletedRef.current = true;\n        gsap.set(targets, {\n          ...to,\n          clearProps: \"willChange\",\n          immediateRender: true,\n        });\n        onLetterAnimationComplete?.();\n      },\n    });\n\n    tl.set(targets, { ...from, immediateRender: false, force3D: true });\n    tl.to(targets, {\n      ...to,\n      duration,\n      ease,\n      stagger: delay / 1000,\n      force3D: true,\n    });\n\n    return () => {\n      tl.kill();\n      if (scrollTriggerRef.current) {\n        scrollTriggerRef.current.kill();\n        scrollTriggerRef.current = null;\n      }\n      gsap.killTweensOf(targets);\n      if (splitter) {\n        splitter.revert();\n      }\n    };\n  }, [\n    text,\n    delay,\n    duration,\n    ease,\n    splitType,\n    from,\n    to,\n    threshold,\n    rootMargin,\n    onLetterAnimationComplete,\n  ]);\n\n  return (\n    <p\n      ref={ref}\n      className={`split-parent overflow-hidden inline-block whitespace-normal ${className}`}\n      style={{\n        textAlign,\n        wordWrap: \"break-word\",\n      }}\n    >\n      {text}\n    </p>\n  );\n};\n\nexport default SplitText;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;;AAEA,gJAAA,CAAA,OAAI,CAAC,cAAc,CAAC,wIAAA,CAAA,gBAAa,EAAE,oIAAA,CAAA,YAAa;AAiBhD,MAAM,YAAsC;QAAC,EAC3C,IAAI,EACJ,YAAY,EAAE,EACd,QAAQ,GAAG,EACX,WAAW,GAAG,EACd,OAAO,YAAY,EACnB,YAAY,OAAO,EACnB,OAAO;QAAE,SAAS;QAAG,GAAG;IAAG,CAAC,EAC5B,KAAK;QAAE,SAAS;QAAG,GAAG;IAAE,CAAC,EACzB,YAAY,GAAG,EACf,aAAa,QAAQ,EACrB,YAAY,QAAQ,EACpB,yBAAyB,EAC1B;;IACC,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAwB;IACzC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACrC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAwB;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,aAAkB,eAAe,CAAC,IAAI,OAAO,IAAI,CAAC,MAAM;YAE5D,MAAM,KAAK,IAAI,OAAO;YAEtB,sBAAsB,OAAO,GAAG;YAEhC,MAAM,gBAAgB,cAAc;YACpC,IAAI,eAAe,GAAG,KAAK,CAAC,QAAQ,GAAG;YAEvC,IAAI;YACJ,IAAI;gBACF,WAAW,IAAI,oIAAA,CAAA,YAAa,CAAC,IAAI;oBAC/B,MAAM;oBACN,UAAU;oBACV,YAAY;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C;YACF;YAEA,IAAI;YACJ,OAAQ;gBACN,KAAK;oBACH,UAAU,SAAS,KAAK;oBACxB;gBACF,KAAK;oBACH,UAAU,SAAS,KAAK;oBACxB;gBACF,KAAK;oBACH,UAAU,SAAS,KAAK;oBACxB;gBACF;oBACE,UAAU,SAAS,KAAK;YAC5B;YAEA,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;gBACpC,QAAQ,IAAI,CAAC;gBACb,SAAS,MAAM;gBACf;YACF;YAEA,QAAQ,OAAO;uCAAC,CAAC;oBACd,EAAkB,KAAK,CAAC,UAAU,GAAG;gBACxC;;YAEA,MAAM,WAAW,CAAC,IAAI,SAAS,IAAI;YACnC,MAAM,cAAc,oCAAoC,IAAI,CAAC;YAC7D,MAAM,cAAc,cAAc,WAAW,WAAW,CAAC,EAAE,IAAI;YAC/D,MAAM,aAAa,cAAe,WAAW,CAAC,EAAE,IAAI,OAAQ;YAC5D,MAAM,OAAO,cAAc,IAAI,AAAC,KAA4B,OAAxB,KAAK,GAAG,CAAC,cAA0B,OAAX,cAAe,AAAC,KAAkB,OAAd,aAAyB,OAAX;YAC9F,MAAM,QAAQ,AAAC,OAAkB,OAAZ,UAAS,KAAQ,OAAL;YAEjC,MAAM,KAAK,gJAAA,CAAA,OAAI,CAAC,QAAQ,CAAC;gBACvB,eAAe;oBACb,SAAS;oBACT;oBACA,eAAe;oBACf,MAAM;oBACN,QAAQ;kDAAE,CAAC;4BACT,iBAAiB,OAAO,GAAG;wBAC7B;;gBACF;gBACA,mBAAmB;gBACnB,UAAU;8CAAE;wBACV,sBAAsB,OAAO,GAAG;wBAChC,gJAAA,CAAA,OAAI,CAAC,GAAG,CAAC,SAAS;4BAChB,GAAG,EAAE;4BACL,YAAY;4BACZ,iBAAiB;wBACnB;wBACA,sCAAA,gDAAA;oBACF;;YACF;YAEA,GAAG,GAAG,CAAC,SAAS;gBAAE,GAAG,IAAI;gBAAE,iBAAiB;gBAAO,SAAS;YAAK;YACjE,GAAG,EAAE,CAAC,SAAS;gBACb,GAAG,EAAE;gBACL;gBACA;gBACA,SAAS,QAAQ;gBACjB,SAAS;YACX;YAEA;uCAAO;oBACL,GAAG,IAAI;oBACP,IAAI,iBAAiB,OAAO,EAAE;wBAC5B,iBAAiB,OAAO,CAAC,IAAI;wBAC7B,iBAAiB,OAAO,GAAG;oBAC7B;oBACA,gJAAA,CAAA,OAAI,CAAC,YAAY,CAAC;oBAClB,IAAI,UAAU;wBACZ,SAAS,MAAM;oBACjB;gBACF;;QACF;8BAAG;QACD;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,AAAC,+DAAwE,OAAV;QAC1E,OAAO;YACL;YACA,UAAU;QACZ;kBAEC;;;;;;AAGP;GA3IM;KAAA;uCA6IS", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/ScrollVelocity/ScrollVelocity.tsx"], "sourcesContent": ["import React, { useRef, useLayoutEffect, useState } from \"react\";\nimport {\n  motion,\n  useScroll,\n  useSpring,\n  useTransform,\n  useMotionValue,\n  useVelocity,\n  useAnimationFrame,\n} from \"framer-motion\";\n\ninterface VelocityMapping {\n  input: [number, number];\n  output: [number, number];\n}\n\ninterface VelocityTextProps {\n  children: React.ReactNode;\n  baseVelocity: number;\n  scrollContainerRef?: React.RefObject<HTMLElement>;\n  className?: string;\n  damping?: number;\n  stiffness?: number;\n  numCopies?: number;\n  velocityMapping?: VelocityMapping;\n  parallaxClassName?: string;\n  scrollerClassName?: string;\n  parallaxStyle?: React.CSSProperties;\n  scrollerStyle?: React.CSSProperties;\n}\n\ninterface ScrollVelocityProps {\n  scrollContainerRef?: React.RefObject<HTMLElement>;\n  texts: string[];\n  velocity?: number;\n  className?: string;\n  damping?: number;\n  stiffness?: number;\n  numCopies?: number;\n  velocityMapping?: VelocityMapping;\n  parallaxClassName?: string;\n  scrollerClassName?: string;\n  parallaxStyle?: React.CSSProperties;\n  scrollerStyle?: React.CSSProperties;\n}\n\nfunction useElementWidth<T extends HTMLElement>(ref: React.RefObject<T | null>): number {\n  const [width, setWidth] = useState(0);\n\n  useLayoutEffect(() => {\n    function updateWidth() {\n      if (ref.current) {\n        setWidth(ref.current.offsetWidth);\n      }\n    }\n    updateWidth();\n    window.addEventListener(\"resize\", updateWidth);\n    return () => window.removeEventListener(\"resize\", updateWidth);\n  }, [ref]);\n\n  return width;\n}\n\nexport const ScrollVelocity: React.FC<ScrollVelocityProps> = ({\n  scrollContainerRef,\n  texts = [],\n  velocity = 100,\n  className = \"\",\n  damping = 50,\n  stiffness = 400,\n  numCopies = 6,\n  velocityMapping = { input: [0, 1000], output: [0, 5] },\n  parallaxClassName,\n  scrollerClassName,\n  parallaxStyle,\n  scrollerStyle,\n}) => {\n  function VelocityText({\n    children,\n    baseVelocity = velocity,\n    scrollContainerRef,\n    className = \"\",\n    damping,\n    stiffness,\n    numCopies,\n    velocityMapping,\n    parallaxClassName,\n    scrollerClassName,\n    parallaxStyle,\n    scrollerStyle,\n  }: VelocityTextProps) {\n    const baseX = useMotionValue(0);\n    const scrollOptions = scrollContainerRef\n      ? { container: scrollContainerRef }\n      : {};\n    const { scrollY } = useScroll(scrollOptions);\n    const scrollVelocity = useVelocity(scrollY);\n    const smoothVelocity = useSpring(scrollVelocity, {\n      damping: damping ?? 50,\n      stiffness: stiffness ?? 400,\n    });\n    const velocityFactor = useTransform(\n      smoothVelocity,\n      velocityMapping?.input || [0, 1000],\n      velocityMapping?.output || [0, 5],\n      { clamp: false }\n    );\n\n    const copyRef = useRef<HTMLSpanElement>(null);\n    const copyWidth = useElementWidth(copyRef);\n\n    function wrap(min: number, max: number, v: number): number {\n      const range = max - min;\n      const mod = (((v - min) % range) + range) % range;\n      return mod + min;\n    }\n\n    const x = useTransform(baseX, (v) => {\n      if (copyWidth === 0) return \"0px\";\n      return `${wrap(-copyWidth, 0, v)}px`;\n    });\n\n    const directionFactor = useRef<number>(1);\n    useAnimationFrame((t, delta) => {\n      let moveBy = directionFactor.current * baseVelocity * (delta / 1000);\n\n      if (velocityFactor.get() < 0) {\n        directionFactor.current = -1;\n      } else if (velocityFactor.get() > 0) {\n        directionFactor.current = 1;\n      }\n\n      moveBy += directionFactor.current * moveBy * velocityFactor.get();\n      baseX.set(baseX.get() + moveBy);\n    });\n\n    const spans = [];\n    for (let i = 0; i < numCopies!; i++) {\n      spans.push(\n        <span\n          className={`flex-shrink-0 ${className}`}\n          key={i}\n          ref={i === 0 ? copyRef : null}\n        >\n          {children}\n        </span>\n      );\n    }\n\n    return (\n      <div\n        className={`${parallaxClassName} relative overflow-hidden`}\n        style={parallaxStyle}\n      >\n        <motion.div\n          className={`${scrollerClassName} flex whitespace-nowrap text-center font-sans text-4xl font-bold tracking-[-0.02em] drop-shadow md:text-[5rem] md:leading-[5rem]`}\n          style={{ x, ...scrollerStyle }}\n        >\n          {spans}\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <section>\n      {texts.map((text: string, index: number) => (\n        <VelocityText\n          key={index}\n          className={className}\n          baseVelocity={index % 2 !== 0 ? -velocity : velocity}\n          scrollContainerRef={scrollContainerRef}\n          damping={damping}\n          stiffness={stiffness}\n          numCopies={numCopies}\n          velocityMapping={velocityMapping}\n          parallaxClassName={parallaxClassName}\n          scrollerClassName={scrollerClassName}\n          parallaxStyle={parallaxStyle}\n          scrollerStyle={scrollerStyle}\n        >\n          {text}&nbsp;\n        </VelocityText>\n      ))}\n    </section>\n  );\n};\n\nexport default ScrollVelocity;\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;AA6CA,SAAS,gBAAuC,GAA8B;;IAC5E,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD;2CAAE;YACd,SAAS;gBACP,IAAI,IAAI,OAAO,EAAE;oBACf,SAAS,IAAI,OAAO,CAAC,WAAW;gBAClC;YACF;YACA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;mDAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;0CAAG;QAAC;KAAI;IAER,OAAO;AACT;GAfS;AAiBF,MAAM,iBAAgD;QAAC,EAC5D,kBAAkB,EAClB,QAAQ,EAAE,EACV,WAAW,GAAG,EACd,YAAY,EAAE,EACd,UAAU,EAAE,EACZ,YAAY,GAAG,EACf,YAAY,CAAC,EACb,kBAAkB;QAAE,OAAO;YAAC;YAAG;SAAK;QAAE,QAAQ;YAAC;YAAG;SAAE;IAAC,CAAC,EACtD,iBAAiB,EACjB,iBAAiB,EACjB,aAAa,EACb,aAAa,EACd;;IACC,SAAS,aAAa,KAaF;YAbE,EACpB,QAAQ,EACR,eAAe,QAAQ,EACvB,kBAAkB,EAClB,YAAY,EAAE,EACd,OAAO,EACP,SAAS,EACT,SAAS,EACT,eAAe,EACf,iBAAiB,EACjB,iBAAiB,EACjB,aAAa,EACb,aAAa,EACK,GAbE;;QAcpB,MAAM,QAAQ,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;QAC7B,MAAM,gBAAgB,qBAClB;YAAE,WAAW;QAAmB,IAChC,CAAC;QACL,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE;QAC9B,MAAM,iBAAiB,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QACnC,MAAM,iBAAiB,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;YAC/C,SAAS,oBAAA,qBAAA,UAAW;YACpB,WAAW,sBAAA,uBAAA,YAAa;QAC1B;QACA,MAAM,iBAAiB,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAChC,gBACA,CAAA,4BAAA,sCAAA,gBAAiB,KAAK,KAAI;YAAC;YAAG;SAAK,EACnC,CAAA,4BAAA,sCAAA,gBAAiB,MAAM,KAAI;YAAC;YAAG;SAAE,EACjC;YAAE,OAAO;QAAM;QAGjB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmB;QACxC,MAAM,YAAY,gBAAgB;QAElC,SAAS,KAAK,GAAW,EAAE,GAAW,EAAE,CAAS;YAC/C,MAAM,QAAQ,MAAM;YACpB,MAAM,MAAM,CAAC,AAAC,CAAC,IAAI,GAAG,IAAI,QAAS,KAAK,IAAI;YAC5C,OAAO,MAAM;QACf;QAEA,MAAM,IAAI,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE;2DAAO,CAAC;gBAC7B,IAAI,cAAc,GAAG,OAAO;gBAC5B,OAAO,AAAC,GAAyB,OAAvB,KAAK,CAAC,WAAW,GAAG,IAAG;YACnC;;QAEA,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;QACvC,CAAA,GAAA,wLAAA,CAAA,oBAAiB,AAAD;6DAAE,CAAC,GAAG;gBACpB,IAAI,SAAS,gBAAgB,OAAO,GAAG,eAAe,CAAC,QAAQ,IAAI;gBAEnE,IAAI,eAAe,GAAG,KAAK,GAAG;oBAC5B,gBAAgB,OAAO,GAAG,CAAC;gBAC7B,OAAO,IAAI,eAAe,GAAG,KAAK,GAAG;oBACnC,gBAAgB,OAAO,GAAG;gBAC5B;gBAEA,UAAU,gBAAgB,OAAO,GAAG,SAAS,eAAe,GAAG;gBAC/D,MAAM,GAAG,CAAC,MAAM,GAAG,KAAK;YAC1B;;QAEA,MAAM,QAAQ,EAAE;QAChB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAY,IAAK;YACnC,MAAM,IAAI,eACR,6LAAC;gBACC,WAAW,AAAC,iBAA0B,OAAV;gBAE5B,KAAK,MAAM,IAAI,UAAU;0BAExB;eAHI;;;;;QAMX;QAEA,qBACE,6LAAC;YACC,WAAW,AAAC,GAAoB,OAAlB,mBAAkB;YAChC,OAAO;sBAEP,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,AAAC,GAAoB,OAAlB,mBAAkB;gBAChC,OAAO;oBAAE;oBAAG,GAAG,aAAa;gBAAC;0BAE5B;;;;;;;;;;;IAIT;OArFS;;YAcO,qLAAA,CAAA,iBAAc;YAIR,4KAAA,CAAA,YAAS;YACN,8KAAA,CAAA,cAAW;YACX,4KAAA,CAAA,YAAS;YAIT,+KAAA,CAAA,eAAY;YAQjB;YAQR,+KAAA,CAAA,eAAY;YAMtB,wLAAA,CAAA,oBAAiB;;;IAyCnB,qBACE,6LAAC;kBACE,MAAM,GAAG,CAAC,CAAC,MAAc,sBACxB,6LAAC;gBAEC,WAAW;gBACX,cAAc,QAAQ,MAAM,IAAI,CAAC,WAAW;gBAC5C,oBAAoB;gBACpB,SAAS;gBACT,WAAW;gBACX,WAAW;gBACX,iBAAiB;gBACjB,mBAAmB;gBACnB,mBAAmB;gBACnB,eAAe;gBACf,eAAe;;oBAEd;oBAAK;;eAbD;;;;;;;;;;AAkBf;KA3Ha;uCA6HE", "debugId": null}}, {"offset": {"line": 358, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/BlurText/BlurText.tsx"], "sourcesContent": ["import { motion, Transition } from \"framer-motion\";\nimport { useEffect, useRef, useState, useMemo } from \"react\";\n\ntype BlurTextProps = {\n  text?: string;\n  delay?: number;\n  className?: string;\n  animateBy?: \"words\" | \"letters\";\n  direction?: \"top\" | \"bottom\";\n  threshold?: number;\n  rootMargin?: string;\n  animationFrom?: Record<string, string | number>;\n  animationTo?: Array<Record<string, string | number>>;\n  easing?: (t: number) => number;\n  onAnimationComplete?: () => void;\n  stepDuration?: number;\n};\n\nconst buildKeyframes = (\n  from: Record<string, string | number>,\n  steps: Array<Record<string, string | number>>\n): Record<string, Array<string | number>> => {\n  const keys = new Set<string>([\n    ...Object.keys(from),\n    ...steps.flatMap((s) => Object.keys(s)),\n  ]);\n\n  const keyframes: Record<string, Array<string | number>> = {};\n  keys.forEach((k) => {\n    keyframes[k] = [from[k], ...steps.map((s) => s[k])];\n  });\n  return keyframes;\n};\n\nconst BlurText: React.FC<BlurTextProps> = ({\n  text = \"\",\n  delay = 200,\n  className = \"\",\n  animateBy = \"words\",\n  direction = \"top\",\n  threshold = 0.1,\n  rootMargin = \"0px\",\n  animationFrom,\n  animationTo,\n  easing = (t) => t,\n  onAnimationComplete,\n  stepDuration = 0.35,\n}) => {\n  const elements = animateBy === \"words\" ? text.split(\" \") : text.split(\"\");\n  const [inView, setInView] = useState(false);\n  const ref = useRef<HTMLParagraphElement>(null);\n\n  useEffect(() => {\n    if (!ref.current) return;\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setInView(true);\n          observer.unobserve(ref.current as Element);\n        }\n      },\n      { threshold, rootMargin }\n    );\n    observer.observe(ref.current);\n    return () => observer.disconnect();\n  }, [threshold, rootMargin]);\n\n  const defaultFrom = useMemo(\n    () =>\n      direction === \"top\"\n        ? { filter: \"blur(10px)\", opacity: 0, y: -50 }\n        : { filter: \"blur(10px)\", opacity: 0, y: 50 },\n    [direction]\n  );\n\n  const defaultTo = useMemo(\n    () => [\n      {\n        filter: \"blur(5px)\",\n        opacity: 0.5,\n        y: direction === \"top\" ? 5 : -5,\n      },\n      { filter: \"blur(0px)\", opacity: 1, y: 0 },\n    ],\n    [direction]\n  );\n\n  const fromSnapshot = animationFrom ?? defaultFrom;\n  const toSnapshots = animationTo ?? defaultTo;\n\n  const stepCount = toSnapshots.length + 1;\n  const totalDuration = stepDuration * (stepCount - 1);\n  const times = Array.from({ length: stepCount }, (_, i) =>\n    stepCount === 1 ? 0 : i / (stepCount - 1)\n  );\n\n  return (\n    <p ref={ref} className={`blur-text ${className} flex flex-wrap`}>\n      {elements.map((segment, index) => {\n        const animateKeyframes = buildKeyframes(fromSnapshot, toSnapshots);\n\n        const spanTransition: Transition = {\n          duration: totalDuration,\n          times,\n          delay: (index * delay) / 1000,\n        };\n        (spanTransition as any).ease = easing;\n\n        return (\n          <motion.span\n            key={index}\n            initial={fromSnapshot}\n            animate={inView ? animateKeyframes : fromSnapshot}\n            transition={spanTransition}\n            onAnimationComplete={\n              index === elements.length - 1 ? onAnimationComplete : undefined\n            }\n            style={{\n              display: \"inline-block\",\n              willChange: \"transform, filter, opacity\",\n            }}\n          >\n            {segment === \" \" ? \"\\u00A0\" : segment}\n            {animateBy === \"words\" && index < elements.length - 1 && \"\\u00A0\"}\n          </motion.span>\n        );\n      })}\n    </p>\n  );\n};\n\nexport default BlurText;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAiBA,MAAM,iBAAiB,CACrB,MACA;IAEA,MAAM,OAAO,IAAI,IAAY;WACxB,OAAO,IAAI,CAAC;WACZ,MAAM,OAAO,CAAC,CAAC,IAAM,OAAO,IAAI,CAAC;KACrC;IAED,MAAM,YAAoD,CAAC;IAC3D,KAAK,OAAO,CAAC,CAAC;QACZ,SAAS,CAAC,EAAE,GAAG;YAAC,IAAI,CAAC,EAAE;eAAK,MAAM,GAAG,CAAC,CAAC,IAAM,CAAC,CAAC,EAAE;SAAE;IACrD;IACA,OAAO;AACT;AAEA,MAAM,WAAoC;QAAC,EACzC,OAAO,EAAE,EACT,QAAQ,GAAG,EACX,YAAY,EAAE,EACd,YAAY,OAAO,EACnB,YAAY,KAAK,EACjB,YAAY,GAAG,EACf,aAAa,KAAK,EAClB,aAAa,EACb,WAAW,EACX,SAAS,CAAC,IAAM,CAAC,EACjB,mBAAmB,EACnB,eAAe,IAAI,EACpB;;IACC,MAAM,WAAW,cAAc,UAAU,KAAK,KAAK,CAAC,OAAO,KAAK,KAAK,CAAC;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAwB;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,CAAC,IAAI,OAAO,EAAE;YAClB,MAAM,WAAW,IAAI;sCACnB;wBAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,UAAU;wBACV,SAAS,SAAS,CAAC,IAAI,OAAO;oBAChC;gBACF;qCACA;gBAAE;gBAAW;YAAW;YAE1B,SAAS,OAAO,CAAC,IAAI,OAAO;YAC5B;sCAAO,IAAM,SAAS,UAAU;;QAClC;6BAAG;QAAC;QAAW;KAAW;IAE1B,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yCACxB,IACE,cAAc,QACV;gBAAE,QAAQ;gBAAc,SAAS;gBAAG,GAAG,CAAC;YAAG,IAC3C;gBAAE,QAAQ;gBAAc,SAAS;gBAAG,GAAG;YAAG;wCAChD;QAAC;KAAU;IAGb,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;uCACtB,IAAM;gBACJ;oBACE,QAAQ;oBACR,SAAS;oBACT,GAAG,cAAc,QAAQ,IAAI,CAAC;gBAChC;gBACA;oBAAE,QAAQ;oBAAa,SAAS;oBAAG,GAAG;gBAAE;aACzC;sCACD;QAAC;KAAU;IAGb,MAAM,eAAe,0BAAA,2BAAA,gBAAiB;IACtC,MAAM,cAAc,wBAAA,yBAAA,cAAe;IAEnC,MAAM,YAAY,YAAY,MAAM,GAAG;IACvC,MAAM,gBAAgB,eAAe,CAAC,YAAY,CAAC;IACnD,MAAM,QAAQ,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAU,GAAG,CAAC,GAAG,IAClD,cAAc,IAAI,IAAI,IAAI,CAAC,YAAY,CAAC;IAG1C,qBACE,6LAAC;QAAE,KAAK;QAAK,WAAW,AAAC,aAAsB,OAAV,WAAU;kBAC5C,SAAS,GAAG,CAAC,CAAC,SAAS;YACtB,MAAM,mBAAmB,eAAe,cAAc;YAEtD,MAAM,iBAA6B;gBACjC,UAAU;gBACV;gBACA,OAAO,AAAC,QAAQ,QAAS;YAC3B;YACC,eAAuB,IAAI,GAAG;YAE/B,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBAEV,SAAS;gBACT,SAAS,SAAS,mBAAmB;gBACrC,YAAY;gBACZ,qBACE,UAAU,SAAS,MAAM,GAAG,IAAI,sBAAsB;gBAExD,OAAO;oBACL,SAAS;oBACT,YAAY;gBACd;;oBAEC,YAAY,MAAM,WAAW;oBAC7B,cAAc,WAAW,QAAQ,SAAS,MAAM,GAAG,KAAK;;eAbpD;;;;;QAgBX;;;;;;AAGN;GA/FM;KAAA;uCAiGS", "debugId": null}}, {"offset": {"line": 498, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/TiltedCard/TiltedCard.tsx"], "sourcesContent": ["import type { SpringOptions } from \"framer-motion\";\nimport { useRef, useState } from \"react\";\nimport { motion, useMotionValue, useSpring } from \"framer-motion\";\n\ninterface TiltedCardProps {\n  imageSrc: React.ComponentProps<\"img\">[\"src\"];\n  altText?: string;\n  captionText?: string;\n  containerHeight?: React.CSSProperties['height'];\n  containerWidth?: React.CSSProperties['width'];\n  imageHeight?: React.CSSProperties['height'];\n  imageWidth?: React.CSSProperties['width'];\n  scaleOnHover?: number;\n  rotateAmplitude?: number;\n  showMobileWarning?: boolean;\n  showTooltip?: boolean;\n  overlayContent?: React.ReactNode;\n  displayOverlayContent?: boolean;\n}\n\nconst springValues: SpringOptions = {\n  damping: 30,\n  stiffness: 100,\n  mass: 2,\n};\n\nexport default function TiltedCard({\n  imageSrc,\n  altText = \"Tilted card image\",\n  captionText = \"\",\n  containerHeight = \"300px\",\n  containerWidth = \"100%\",\n  imageHeight = \"300px\",\n  imageWidth = \"300px\",\n  scaleOnHover = 1.1,\n  rotateAmplitude = 14,\n  showMobileWarning = true,\n  showTooltip = true,\n  overlayContent = null,\n  displayOverlayContent = false,\n}: TiltedCardProps) {\n  const ref = useRef<HTMLElement>(null);\n  const x = useMotionValue(0);\n  const y = useMotionValue(0);\n  const rotateX = useSpring(useMotionValue(0), springValues);\n  const rotateY = useSpring(useMotionValue(0), springValues);\n  const scale = useSpring(1, springValues);\n  const opacity = useSpring(0);\n  const rotateFigcaption = useSpring(0, {\n    stiffness: 350,\n    damping: 30,\n    mass: 1,\n  });\n\n  const [lastY, setLastY] = useState(0);\n\n  function handleMouse(e: React.MouseEvent<HTMLElement>) {\n    if (!ref.current) return;\n\n    const rect = ref.current.getBoundingClientRect();\n    const offsetX = e.clientX - rect.left - rect.width / 2;\n    const offsetY = e.clientY - rect.top - rect.height / 2;\n\n    const rotationX = (offsetY / (rect.height / 2)) * -rotateAmplitude;\n    const rotationY = (offsetX / (rect.width / 2)) * rotateAmplitude;\n\n    rotateX.set(rotationX);\n    rotateY.set(rotationY);\n\n    x.set(e.clientX - rect.left);\n    y.set(e.clientY - rect.top);\n\n    const velocityY = offsetY - lastY;\n    rotateFigcaption.set(-velocityY * 0.6);\n    setLastY(offsetY);\n  }\n\n  function handleMouseEnter() {\n    scale.set(scaleOnHover);\n    opacity.set(1);\n  }\n\n  function handleMouseLeave() {\n    opacity.set(0);\n    scale.set(1);\n    rotateX.set(0);\n    rotateY.set(0);\n    rotateFigcaption.set(0);\n  }\n\n  return (\n    <figure\n      ref={ref}\n      className=\"relative w-full h-full [perspective:800px] flex flex-col items-center justify-center\"\n      style={{\n        height: containerHeight,\n        width: containerWidth,\n      }}\n      onMouseMove={handleMouse}\n      onMouseEnter={handleMouseEnter}\n      onMouseLeave={handleMouseLeave}\n    >\n      {showMobileWarning && (\n        <div className=\"absolute top-4 text-center text-sm block sm:hidden\">\n          This effect is not optimized for mobile. Check on desktop.\n        </div>\n      )}\n\n      <motion.div\n        className=\"relative [transform-style:preserve-3d]\"\n        style={{\n          width: imageWidth,\n          height: imageHeight,\n          rotateX,\n          rotateY,\n          scale,\n        }}\n      >\n        <motion.img\n          src={imageSrc}\n          alt={altText}\n          className=\"absolute top-0 left-0 object-cover rounded-[15px] will-change-transform [transform:translateZ(0)]\"\n          style={{\n            width: imageWidth,\n            height: imageHeight,\n          }}\n        />\n\n        {displayOverlayContent && overlayContent && (\n          <motion.div\n            className=\"absolute top-0 left-0 z-[2] will-change-transform [transform:translateZ(30px)]\"\n          >\n            {overlayContent}\n          </motion.div>\n        )}\n      </motion.div>\n\n      {showTooltip && (\n        <motion.figcaption\n          className=\"pointer-events-none absolute left-0 top-0 rounded-[4px] bg-white px-[10px] py-[4px] text-[10px] text-[#2d2d2d] opacity-0 z-[3] hidden sm:block\"\n          style={{\n            x,\n            y,\n            opacity,\n            rotate: rotateFigcaption,\n          }}\n        >\n          {captionText}\n        </motion.figcaption>\n      )}\n    </figure>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;;;;;AAkBA,MAAM,eAA8B;IAClC,SAAS;IACT,WAAW;IACX,MAAM;AACR;AAEe,SAAS,WAAW,KAcjB;QAdiB,EACjC,QAAQ,EACR,UAAU,mBAAmB,EAC7B,cAAc,EAAE,EAChB,kBAAkB,OAAO,EACzB,iBAAiB,MAAM,EACvB,cAAc,OAAO,EACrB,aAAa,OAAO,EACpB,eAAe,GAAG,EAClB,kBAAkB,EAAE,EACpB,oBAAoB,IAAI,EACxB,cAAc,IAAI,EAClB,iBAAiB,IAAI,EACrB,wBAAwB,KAAK,EACb,GAdiB;;IAejC,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAe;IAChC,MAAM,IAAI,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,IAAI,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE,IAAI;IAC7C,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE,IAAI;IAC7C,MAAM,QAAQ,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,GAAG;IAC3B,MAAM,UAAU,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE;IAC1B,MAAM,mBAAmB,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,GAAG;QACpC,WAAW;QACX,SAAS;QACT,MAAM;IACR;IAEA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,SAAS,YAAY,CAAgC;QACnD,IAAI,CAAC,IAAI,OAAO,EAAE;QAElB,MAAM,OAAO,IAAI,OAAO,CAAC,qBAAqB;QAC9C,MAAM,UAAU,EAAE,OAAO,GAAG,KAAK,IAAI,GAAG,KAAK,KAAK,GAAG;QACrD,MAAM,UAAU,EAAE,OAAO,GAAG,KAAK,GAAG,GAAG,KAAK,MAAM,GAAG;QAErD,MAAM,YAAY,AAAC,UAAU,CAAC,KAAK,MAAM,GAAG,CAAC,IAAK,CAAC;QACnD,MAAM,YAAY,AAAC,UAAU,CAAC,KAAK,KAAK,GAAG,CAAC,IAAK;QAEjD,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QAEZ,EAAE,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,IAAI;QAC3B,EAAE,GAAG,CAAC,EAAE,OAAO,GAAG,KAAK,GAAG;QAE1B,MAAM,YAAY,UAAU;QAC5B,iBAAiB,GAAG,CAAC,CAAC,YAAY;QAClC,SAAS;IACX;IAEA,SAAS;QACP,MAAM,GAAG,CAAC;QACV,QAAQ,GAAG,CAAC;IACd;IAEA,SAAS;QACP,QAAQ,GAAG,CAAC;QACZ,MAAM,GAAG,CAAC;QACV,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC;QACZ,iBAAiB,GAAG,CAAC;IACvB;IAEA,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YACL,QAAQ;YACR,OAAO;QACT;QACA,aAAa;QACb,cAAc;QACd,cAAc;;YAEb,mCACC,6LAAC;gBAAI,WAAU;0BAAqD;;;;;;0BAKtE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,OAAO;oBACP,QAAQ;oBACR;oBACA;oBACA;gBACF;;kCAEA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,KAAK;wBACL,KAAK;wBACL,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,QAAQ;wBACV;;;;;;oBAGD,yBAAyB,gCACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;kCAET;;;;;;;;;;;;YAKN,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,UAAU;gBAChB,WAAU;gBACV,OAAO;oBACL;oBACA;oBACA;oBACA,QAAQ;gBACV;0BAEC;;;;;;;;;;;;AAKX;GA9HwB;;QAgBZ,qLAAA,CAAA,iBAAc;QACd,qLAAA,CAAA,iBAAc;QACR,4KAAA,CAAA,YAAS;QACT,4KAAA,CAAA,YAAS;QACX,4KAAA,CAAA,YAAS;QACP,4KAAA,CAAA,YAAS;QACA,4KAAA,CAAA,YAAS;;;KAtBZ", "debugId": null}}, {"offset": {"line": 657, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/CircularText/CircularText.tsx"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport {\n  motion,\n  useAnimation,\n  useMotionValue,\n  MotionValue,\n  Transition,\n} from \"framer-motion\";\ninterface CircularTextProps {\n  text: string;\n  spinDuration?: number;\n  onHover?: \"slowDown\" | \"speedUp\" | \"pause\" | \"goBonkers\";\n  className?: string;\n}\n\nconst getRotationTransition = (\n  duration: number,\n  from: number,\n  loop: boolean = true\n) => ({\n  from,\n  to: from + 360,\n  ease: \"linear\" as const,\n  duration,\n  type: \"tween\" as const,\n  repeat: loop ? Infinity : 0,\n});\n\nconst getTransition = (duration: number, from: number) => ({\n  rotate: getRotationTransition(duration, from),\n  scale: {\n    type: \"spring\" as const,\n    damping: 20,\n    stiffness: 300,\n  },\n});\n\nconst CircularText: React.FC<CircularTextProps> = ({\n  text,\n  spinDuration = 20,\n  onHover = \"speedUp\",\n  className = \"\",\n}) => {\n  const letters = Array.from(text);\n  const controls = useAnimation();\n  const rotation: MotionValue<number> = useMotionValue(0);\n\n  useEffect(() => {\n    const start = rotation.get();\n    controls.start({\n      rotate: start + 360,\n      scale: 1,\n      transition: getTransition(spinDuration, start),\n    });\n  }, [spinDuration, text, onHover, controls]);\n\n  const handleHoverStart = () => {\n    const start = rotation.get();\n\n    if (!onHover) return;\n\n    let transitionConfig: ReturnType<typeof getTransition> | Transition;\n    let scaleVal = 1;\n\n    switch (onHover) {\n      case \"slowDown\":\n        transitionConfig = getTransition(spinDuration * 2, start);\n        break;\n      case \"speedUp\":\n        transitionConfig = getTransition(spinDuration / 4, start);\n        break;\n      case \"pause\":\n        transitionConfig = {\n          rotate: { type: \"spring\", damping: 20, stiffness: 300 },\n          scale: { type: \"spring\", damping: 20, stiffness: 300 },\n        };\n        break;\n      case \"goBonkers\":\n        transitionConfig = getTransition(spinDuration / 20, start);\n        scaleVal = 0.8;\n        break;\n      default:\n        transitionConfig = getTransition(spinDuration, start);\n    }\n\n    controls.start({\n      rotate: start + 360,\n      scale: scaleVal,\n      transition: transitionConfig,\n    });\n  };\n\n  const handleHoverEnd = () => {\n    const start = rotation.get();\n    controls.start({\n      rotate: start + 360,\n      scale: 1,\n      transition: getTransition(spinDuration, start),\n    });\n  };\n\n  return (\n    <motion.div\n    className={`m-0 mx-auto rounded-full w-[200px] h-[200px] relative font-black text-center cursor-pointer origin-center ${className}`}\n    style={{ rotate: rotation }}\n      initial={{ rotate: 0 }}\n      animate={controls}\n      onMouseEnter={handleHoverStart}\n      onMouseLeave={handleHoverEnd}\n    >\n      {letters.map((letter, i) => {\n        const rotationDeg = (360 / letters.length) * i;\n        const factor = Math.PI / letters.length;\n        const x = factor * i;\n        const y = factor * i;\n        const transform = `rotateZ(${rotationDeg}deg) translate3d(${x}px, ${y}px, 0)`;\n\n        return (\n          <span\n            key={i}\n            className=\"absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]\"\n            style={{ transform, WebkitTransform: transform }}\n          >\n            {letter}\n          </span>\n        );\n      })}\n    </motion.div>\n  );\n};\n\nexport default CircularText;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;;;;;AAcA,MAAM,wBAAwB,SAC5B,UACA;QACA,wEAAgB;WACZ;QACJ;QACA,IAAI,OAAO;QACX,MAAM;QACN;QACA,MAAM;QACN,QAAQ,OAAO,WAAW;IAC5B;;AAEA,MAAM,gBAAgB,CAAC,UAAkB,OAAiB,CAAC;QACzD,QAAQ,sBAAsB,UAAU;QACxC,OAAO;YACL,MAAM;YACN,SAAS;YACT,WAAW;QACb;IACF,CAAC;AAED,MAAM,eAA4C;QAAC,EACjD,IAAI,EACJ,eAAe,EAAE,EACjB,UAAU,SAAS,EACnB,YAAY,EAAE,EACf;;IACC,MAAM,UAAU,MAAM,IAAI,CAAC;IAC3B,MAAM,WAAW,CAAA,GAAA,4LAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,WAAgC,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;IAErD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,QAAQ,SAAS,GAAG;YAC1B,SAAS,KAAK,CAAC;gBACb,QAAQ,QAAQ;gBAChB,OAAO;gBACP,YAAY,cAAc,cAAc;YAC1C;QACF;iCAAG;QAAC;QAAc;QAAM;QAAS;KAAS;IAE1C,MAAM,mBAAmB;QACvB,MAAM,QAAQ,SAAS,GAAG;QAE1B,IAAI,CAAC,SAAS;QAEd,IAAI;QACJ,IAAI,WAAW;QAEf,OAAQ;YACN,KAAK;gBACH,mBAAmB,cAAc,eAAe,GAAG;gBACnD;YACF,KAAK;gBACH,mBAAmB,cAAc,eAAe,GAAG;gBACnD;YACF,KAAK;gBACH,mBAAmB;oBACjB,QAAQ;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;oBACtD,OAAO;wBAAE,MAAM;wBAAU,SAAS;wBAAI,WAAW;oBAAI;gBACvD;gBACA;YACF,KAAK;gBACH,mBAAmB,cAAc,eAAe,IAAI;gBACpD,WAAW;gBACX;YACF;gBACE,mBAAmB,cAAc,cAAc;QACnD;QAEA,SAAS,KAAK,CAAC;YACb,QAAQ,QAAQ;YAChB,OAAO;YACP,YAAY;QACd;IACF;IAEA,MAAM,iBAAiB;QACrB,MAAM,QAAQ,SAAS,GAAG;QAC1B,SAAS,KAAK,CAAC;YACb,QAAQ,QAAQ;YAChB,OAAO;YACP,YAAY,cAAc,cAAc;QAC1C;IACF;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACX,WAAW,AAAC,6GAAsH,OAAV;QACxH,OAAO;YAAE,QAAQ;QAAS;QACxB,SAAS;YAAE,QAAQ;QAAE;QACrB,SAAS;QACT,cAAc;QACd,cAAc;kBAEb,QAAQ,GAAG,CAAC,CAAC,QAAQ;YACpB,MAAM,cAAc,AAAC,MAAM,QAAQ,MAAM,GAAI;YAC7C,MAAM,SAAS,KAAK,EAAE,GAAG,QAAQ,MAAM;YACvC,MAAM,IAAI,SAAS;YACnB,MAAM,IAAI,SAAS;YACnB,MAAM,YAAY,AAAC,WAAyC,OAA/B,aAAY,qBAA2B,OAAR,GAAE,QAAQ,OAAF,GAAE;YAEtE,qBACE,6LAAC;gBAEC,WAAU;gBACV,OAAO;oBAAE;oBAAW,iBAAiB;gBAAU;0BAE9C;eAJI;;;;;QAOX;;;;;;AAGN;GA5FM;;QAOa,4LAAA,CAAA,eAAY;QACS,qLAAA,CAAA,iBAAc;;;KARhD;uCA8FS", "debugId": null}}, {"offset": {"line": 811, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/FlippableProfileCard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport TiltedCard from './TiltedCard/TiltedCard';\nimport CircularText from './CircularText/CircularText';\n\ninterface FlippableProfileCardProps {\n  imageSrc: string;\n  altText?: string;\n}\n\nexport default function FlippableProfileCard({ \n  imageSrc, \n  altText = \"Abdullah - Full Stack Developer\" \n}: FlippableProfileCardProps) {\n  const [isFlipped, setIsFlipped] = useState(false);\n\n  const handleCardClick = () => {\n    setIsFlipped(!isFlipped);\n  };\n\n  return (\n    <div className=\"relative w-64 h-64 md:w-80 md:h-80 mx-auto [perspective:1000px]\">\n      <motion.div\n        className=\"relative w-full h-full [transform-style:preserve-3d] cursor-pointer\"\n        animate={{ rotateY: isFlipped ? 180 : 0 }}\n        transition={{ duration: 0.8, ease: \"easeInOut\" }}\n        onClick={handleCardClick}\n      >\n        {/* Front Side */}\n        <div className=\"absolute inset-0 w-full h-full [backface-visibility:hidden]\">\n          <TiltedCard\n            imageSrc={imageSrc}\n            altText={altText}\n            captionText=\"you can click it\"\n            containerHeight=\"100%\"\n            containerWidth=\"100%\"\n            imageHeight=\"100%\"\n            imageWidth=\"100%\"\n            scaleOnHover={1.05}\n            rotateAmplitude={12}\n            showMobileWarning={false}\n            showTooltip={true}\n          />\n        </div>\n\n        {/* Back Side */}\n        <div className=\"absolute inset-0 w-full h-full [backface-visibility:hidden] [transform:rotateY(180deg)]\">\n          <div className=\"w-full h-full bg-white border border-gray-200 rounded-3xl flex items-center justify-center shadow-2xl\">\n            <div className=\"relative\">\n              <CircularText\n                text=\"scroll for more info about me • \"\n                spinDuration={20}\n                onHover=\"speedUp\"\n                className=\"text-gray-600 font-medium text-sm\"\n              />\n              <div className=\"absolute inset-0 flex items-center justify-center\">\n                <div className=\"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center\">\n                  <svg\n                    className=\"w-5 h-5 text-gray-500 animate-bounce\"\n                    fill=\"none\"\n                    stroke=\"currentColor\"\n                    viewBox=\"0 0 24 24\"\n                  >\n                    <path\n                      strokeLinecap=\"round\"\n                      strokeLinejoin=\"round\"\n                      strokeWidth={2}\n                      d=\"M19 14l-7 7m0 0l-7-7m7 7V3\"\n                    />\n                  </svg>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYe,SAAS,qBAAqB,KAGjB;QAHiB,EAC3C,QAAQ,EACR,UAAU,iCAAiC,EACjB,GAHiB;;IAI3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB;QACtB,aAAa,CAAC;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS,YAAY,MAAM;YAAE;YACxC,YAAY;gBAAE,UAAU;gBAAK,MAAM;YAAY;YAC/C,SAAS;;8BAGT,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,0IAAA,CAAA,UAAU;wBACT,UAAU;wBACV,SAAS;wBACT,aAAY;wBACZ,iBAAgB;wBAChB,gBAAe;wBACf,aAAY;wBACZ,YAAW;wBACX,cAAc;wBACd,iBAAiB;wBACjB,mBAAmB;wBACnB,aAAa;;;;;;;;;;;8BAKjB,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,8IAAA,CAAA,UAAY;oCACX,MAAK;oCACL,cAAc;oCACd,SAAQ;oCACR,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;sDAER,cAAA,6LAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxB;GApEwB;KAAA", "debugId": null}}, {"offset": {"line": 962, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/ui/animated-tooltip.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useRef } from \"react\";\nimport {\n  motion,\n  useTransform,\n  AnimatePresence,\n  useMotionValue,\n  useSpring,\n} from \"motion/react\";\n\nexport const AnimatedTooltip = ({\n  items,\n}: {\n  items: {\n    id: number;\n    name: string;\n    designation: string;\n    image: string;\n  }[];\n}) => {\n  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);\n  const springConfig = { stiffness: 100, damping: 15 };\n  const x = useMotionValue(0);\n  const animationFrameRef = useRef<number | null>(null);\n\n  const rotate = useSpring(\n    useTransform(x, [-100, 100], [-45, 45]),\n    springConfig,\n  );\n  const translateX = useSpring(\n    useTransform(x, [-100, 100], [-50, 50]),\n    springConfig,\n  );\n\n  const handleMouseMove = (event: any) => {\n    if (animationFrameRef.current) {\n      cancelAnimationFrame(animationFrameRef.current);\n    }\n\n    animationFrameRef.current = requestAnimationFrame(() => {\n      const halfWidth = event.target.offsetWidth / 2;\n      x.set(event.nativeEvent.offsetX - halfWidth);\n    });\n  };\n\n  return (\n    <>\n      {items.map((item, idx) => (\n        <div\n          className=\"group relative -mr-4\"\n          key={item.name}\n          onMouseEnter={() => setHoveredIndex(item.id)}\n          onMouseLeave={() => setHoveredIndex(null)}\n        >\n          <AnimatePresence>\n            {hoveredIndex === item.id && (\n              <motion.div\n                initial={{ opacity: 0, y: 20, scale: 0.6 }}\n                animate={{\n                  opacity: 1,\n                  y: 0,\n                  scale: 1,\n                  transition: {\n                    type: \"spring\",\n                    stiffness: 260,\n                    damping: 10,\n                  },\n                }}\n                exit={{ opacity: 0, y: 20, scale: 0.6 }}\n                style={{\n                  translateX: translateX,\n                  rotate: rotate,\n                  whiteSpace: \"nowrap\",\n                }}\n                className=\"absolute -top-16 left-1/2 z-50 flex -translate-x-1/2 flex-col items-center justify-center rounded-md bg-black px-4 py-2 text-xs shadow-xl\"\n              >\n                <div className=\"absolute inset-x-10 -bottom-px z-30 h-px w-[20%] bg-gradient-to-r from-transparent via-emerald-500 to-transparent\" />\n                <div className=\"absolute -bottom-px left-10 z-30 h-px w-[40%] bg-gradient-to-r from-transparent via-sky-500 to-transparent\" />\n                <div className=\"relative z-30 text-base font-bold text-white\">\n                  {item.name}\n                </div>\n                <div className=\"text-xs text-white\">{item.designation}</div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n          <img\n            onMouseMove={handleMouseMove}\n            height={100}\n            width={100}\n            src={item.image}\n            alt={item.name}\n            className=\"relative !m-0 h-14 w-14 rounded-full border-2 border-white object-cover object-center !p-0 transition duration-500 group-hover:z-30 group-hover:scale-105\"\n          />\n        </div>\n      ))}\n    </>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAWO,MAAM,kBAAkB;QAAC,EAC9B,KAAK,EAQN;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,eAAe;QAAE,WAAW;QAAK,SAAS;IAAG;IACnD,MAAM,IAAI,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;IACzB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAiB;IAEhD,MAAM,SAAS,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EACrB,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,GAAG;QAAC,CAAC;QAAK;KAAI,EAAE;QAAC,CAAC;QAAI;KAAG,GACtC;IAEF,MAAM,aAAa,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EACzB,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,GAAG;QAAC,CAAC;QAAK;KAAI,EAAE;QAAC,CAAC;QAAI;KAAG,GACtC;IAGF,MAAM,kBAAkB,CAAC;QACvB,IAAI,kBAAkB,OAAO,EAAE;YAC7B,qBAAqB,kBAAkB,OAAO;QAChD;QAEA,kBAAkB,OAAO,GAAG,sBAAsB;YAChD,MAAM,YAAY,MAAM,MAAM,CAAC,WAAW,GAAG;YAC7C,EAAE,GAAG,CAAC,MAAM,WAAW,CAAC,OAAO,GAAG;QACpC;IACF;IAEA,qBACE;kBACG,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,6LAAC;gBACC,WAAU;gBAEV,cAAc,IAAM,gBAAgB,KAAK,EAAE;gBAC3C,cAAc,IAAM,gBAAgB;;kCAEpC,6LAAC,4LAAA,CAAA,kBAAe;kCACb,iBAAiB,KAAK,EAAE,kBACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAI,OAAO;4BAAI;4BACzC,SAAS;gCACP,SAAS;gCACT,GAAG;gCACH,OAAO;gCACP,YAAY;oCACV,MAAM;oCACN,WAAW;oCACX,SAAS;gCACX;4BACF;4BACA,MAAM;gCAAE,SAAS;gCAAG,GAAG;gCAAI,OAAO;4BAAI;4BACtC,OAAO;gCACL,YAAY;gCACZ,QAAQ;gCACR,YAAY;4BACd;4BACA,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;8CACZ,KAAK,IAAI;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;8CAAsB,KAAK,WAAW;;;;;;;;;;;;;;;;;kCAI3D,6LAAC;wBACC,aAAa;wBACb,QAAQ;wBACR,OAAO;wBACP,KAAK,KAAK,KAAK;wBACf,KAAK,KAAK,IAAI;wBACd,WAAU;;;;;;;eAzCP,KAAK,IAAI;;;;;;AA+CxB;GAvFa;;QAYD,qLAAA,CAAA,iBAAc;QAGT,4KAAA,CAAA,YAAS;QAIL,4KAAA,CAAA,YAAS;;;KAnBjB", "debugId": null}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/components/Dock/Dock.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  motion,\n  MotionValue,\n  useMotionValue,\n  useSpring,\n  useTransform,\n  type SpringOptions,\n  AnimatePresence,\n} from \"framer-motion\";\nimport React, {\n  Children,\n  cloneElement,\n  useEffect,\n  useMemo,\n  useRef,\n  useState,\n} from \"react\";\n\nexport type DockItemData = {\n  icon: React.ReactNode;\n  label: React.ReactNode;\n  onClick: () => void;\n  className?: string;\n};\n\nexport type DockProps = {\n  items: DockItemData[];\n  className?: string;\n  distance?: number;\n  panelHeight?: number;\n  baseItemSize?: number;\n  dockHeight?: number;\n  magnification?: number;\n  spring?: SpringOptions;\n};\n\ntype DockItemProps = {\n  className?: string;\n  children: React.ReactNode;\n  onClick?: () => void;\n  mouseX: MotionValue;\n  spring: SpringOptions;\n  distance: number;\n  baseItemSize: number;\n  magnification: number;\n};\n\nfunction DockItem({\n  children,\n  className = \"\",\n  onClick,\n  mouseX,\n  spring,\n  distance,\n  magnification,\n  baseItemSize,\n}: DockItemProps) {\n  const ref = useRef<HTMLDivElement>(null);\n  const isHovered = useMotionValue(0);\n\n  const mouseDistance = useTransform(mouseX, (val) => {\n    const rect = ref.current?.getBoundingClientRect() ?? {\n      x: 0,\n      width: baseItemSize,\n    };\n    return val - rect.x - baseItemSize / 2;\n  });\n\n  const targetSize = useTransform(\n    mouseDistance,\n    [-distance, 0, distance],\n    [baseItemSize, magnification, baseItemSize]\n  );\n  const size = useSpring(targetSize, spring);\n\n  return (\n    <motion.div\n      ref={ref}\n      style={{\n        width: size,\n        height: size,\n      }}\n      onHoverStart={() => isHovered.set(1)}\n      onHoverEnd={() => isHovered.set(0)}\n      onFocus={() => isHovered.set(1)}\n      onBlur={() => isHovered.set(0)}\n      onClick={onClick}\n      className={`relative inline-flex items-center justify-center rounded-2xl bg-white/95 backdrop-blur-md border border-gray-300/50 shadow-lg hover:shadow-xl hover:bg-white transition-all duration-200 ${className}`}\n      tabIndex={0}\n      role=\"button\"\n      aria-haspopup=\"true\"\n    >\n      {Children.map(children, (child) =>\n        cloneElement(child as React.ReactElement, { isHovered })\n      )}\n    </motion.div>\n  );\n}\n\ntype DockLabelProps = {\n  className?: string;\n  children: React.ReactNode;\n};\n\nfunction DockLabel({ children, className = \"\", ...rest }: DockLabelProps) {\n  const { isHovered } = rest as { isHovered: MotionValue<number> };\n  const [isVisible, setIsVisible] = useState(false);\n\n  useEffect(() => {\n    const unsubscribe = isHovered.on(\"change\", (latest) => {\n      setIsVisible(latest === 1);\n    });\n    return () => unsubscribe();\n  }, [isHovered]);\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          initial={{ opacity: 0, y: 0 }}\n          animate={{ opacity: 1, y: -10 }}\n          exit={{ opacity: 0, y: 0 }}\n          transition={{ duration: 0.2 }}\n          className={`${className} absolute -top-6 left-1/2 w-fit whitespace-pre rounded-md border border-gray-200 bg-white/95 backdrop-blur-md px-3 py-1 text-xs text-gray-800 shadow-lg`}\n          role=\"tooltip\"\n          style={{ x: \"-50%\" }}\n        >\n          {children}\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n}\n\ntype DockIconProps = {\n  className?: string;\n  children: React.ReactNode;\n};\n\nfunction DockIcon({ children, className = \"\" }: DockIconProps) {\n  return (\n    <div className={`flex items-center justify-center ${className}`}>\n      {children}\n    </div>\n  );\n}\n\nexport default function Dock({\n  items,\n  className = \"\",\n  spring = { mass: 0.05, stiffness: 200, damping: 15 },\n  magnification = 90,\n  distance = 150,\n  panelHeight = 64,\n  dockHeight = 256,\n  baseItemSize = 50,\n}: DockProps) {\n  const mouseX = useMotionValue(Infinity);\n  const isHovered = useMotionValue(0);\n\n  const maxHeight = useMemo(\n    () => Math.max(dockHeight, magnification + magnification / 2 + 4),\n    [magnification]\n  );\n  const heightRow = useTransform(isHovered, [0, 1], [panelHeight, maxHeight]);\n  const height = useSpring(heightRow, spring);\n\n  return (\n    <motion.div\n      style={{ height, scrollbarWidth: \"none\" }}\n      className=\"mx-2 flex max-w-full items-center\"\n    >\n      <motion.div\n        onMouseMove={({ pageX }) => {\n          isHovered.set(1);\n          mouseX.set(pageX);\n        }}\n        onMouseLeave={() => {\n          isHovered.set(0);\n          mouseX.set(Infinity);\n        }}\n        className={`${className} absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-end w-fit gap-2 rounded-2xl bg-white/80 backdrop-blur-xl border border-gray-200/50 pb-2 px-4 shadow-2xl`}\n        style={{ height: panelHeight }}\n        role=\"toolbar\"\n        aria-label=\"Application dock\"\n      >\n        {items.map((item, index) => (\n          <DockItem\n            key={index}\n            onClick={item.onClick}\n            className={item.className}\n            mouseX={mouseX}\n            spring={spring}\n            distance={distance}\n            magnification={magnification}\n            baseItemSize={baseItemSize}\n          >\n            <DockIcon>{item.icon}</DockIcon>\n            <DockLabel>{item.label}</DockLabel>\n          </DockItem>\n        ))}\n      </motion.div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AASA;;;AAXA;;;AAiDA,SAAS,SAAS,KASF;QATE,EAChB,QAAQ,EACR,YAAY,EAAE,EACd,OAAO,EACP,MAAM,EACN,MAAM,EACN,QAAQ,EACR,aAAa,EACb,YAAY,EACE,GATE;;IAUhB,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,MAAM,YAAY,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;IAEjC,MAAM,gBAAgB,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE;gDAAQ,CAAC;gBAC7B;gBAAA;YAAb,MAAM,OAAO,CAAA,sCAAA,eAAA,IAAI,OAAO,cAAX,mCAAA,aAAa,qBAAqB,gBAAlC,gDAAA,qCAAwC;gBACnD,GAAG;gBACH,OAAO;YACT;YACA,OAAO,MAAM,KAAK,CAAC,GAAG,eAAe;QACvC;;IAEA,MAAM,aAAa,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAC5B,eACA;QAAC,CAAC;QAAU;QAAG;KAAS,EACxB;QAAC;QAAc;QAAe;KAAa;IAE7C,MAAM,OAAO,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,YAAY;IAEnC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,OAAO;YACL,OAAO;YACP,QAAQ;QACV;QACA,cAAc,IAAM,UAAU,GAAG,CAAC;QAClC,YAAY,IAAM,UAAU,GAAG,CAAC;QAChC,SAAS,IAAM,UAAU,GAAG,CAAC;QAC7B,QAAQ,IAAM,UAAU,GAAG,CAAC;QAC5B,SAAS;QACT,WAAW,AAAC,4LAAqM,OAAV;QACvM,UAAU;QACV,MAAK;QACL,iBAAc;kBAEb,6JAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,UAAU,CAAC,sBACvB,CAAA,GAAA,6JAAA,CAAA,eAAY,AAAD,EAAE,OAA6B;gBAAE;YAAU;;;;;;AAI9D;GAlDS;;QAWW,qLAAA,CAAA,iBAAc;QAEV,+KAAA,CAAA,eAAY;QAQf,+KAAA,CAAA,eAAY;QAKlB,4KAAA,CAAA,YAAS;;;KA1Bf;AAyDT,SAAS,UAAU,KAAqD;QAArD,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAE,GAAG,MAAsB,GAArD;;IACjB,MAAM,EAAE,SAAS,EAAE,GAAG;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,cAAc,UAAU,EAAE,CAAC;mDAAU,CAAC;oBAC1C,aAAa,WAAW;gBAC1B;;YACA;uCAAO,IAAM;;QACf;8BAAG;QAAC;KAAU;IAEd,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,GAAG;YAAE;YAC5B,SAAS;gBAAE,SAAS;gBAAG,GAAG,CAAC;YAAG;YAC9B,MAAM;gBAAE,SAAS;gBAAG,GAAG;YAAE;YACzB,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAW,AAAC,GAAY,OAAV,WAAU;YACxB,MAAK;YACL,OAAO;gBAAE,GAAG;YAAO;sBAElB;;;;;;;;;;;AAKX;IA5BS;MAAA;AAmCT,SAAS,SAAS,KAA2C;QAA3C,EAAE,QAAQ,EAAE,YAAY,EAAE,EAAiB,GAA3C;IAChB,qBACE,6LAAC;QAAI,WAAW,AAAC,oCAA6C,OAAV;kBACjD;;;;;;AAGP;MANS;AAQM,SAAS,KAAK,KASjB;QATiB,EAC3B,KAAK,EACL,YAAY,EAAE,EACd,SAAS;QAAE,MAAM;QAAM,WAAW;QAAK,SAAS;IAAG,CAAC,EACpD,gBAAgB,EAAE,EAClB,WAAW,GAAG,EACd,cAAc,EAAE,EAChB,aAAa,GAAG,EAChB,eAAe,EAAE,EACP,GATiB;;IAU3B,MAAM,SAAS,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;IAC9B,MAAM,YAAY,CAAA,GAAA,qLAAA,CAAA,iBAAc,AAAD,EAAE;IAEjC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mCACtB,IAAM,KAAK,GAAG,CAAC,YAAY,gBAAgB,gBAAgB,IAAI;kCAC/D;QAAC;KAAc;IAEjB,MAAM,YAAY,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,WAAW;QAAC;QAAG;KAAE,EAAE;QAAC;QAAa;KAAU;IAC1E,MAAM,SAAS,CAAA,GAAA,4KAAA,CAAA,YAAS,AAAD,EAAE,WAAW;IAEpC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,OAAO;YAAE;YAAQ,gBAAgB;QAAO;QACxC,WAAU;kBAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,aAAa;oBAAC,EAAE,KAAK,EAAE;gBACrB,UAAU,GAAG,CAAC;gBACd,OAAO,GAAG,CAAC;YACb;YACA,cAAc;gBACZ,UAAU,GAAG,CAAC;gBACd,OAAO,GAAG,CAAC;YACb;YACA,WAAW,AAAC,GAAY,OAAV,WAAU;YACxB,OAAO;gBAAE,QAAQ;YAAY;YAC7B,MAAK;YACL,cAAW;sBAEV,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;oBAEC,SAAS,KAAK,OAAO;oBACrB,WAAW,KAAK,SAAS;oBACzB,QAAQ;oBACR,QAAQ;oBACR,UAAU;oBACV,eAAe;oBACf,cAAc;;sCAEd,6LAAC;sCAAU,KAAK,IAAI;;;;;;sCACpB,6LAAC;sCAAW,KAAK,KAAK;;;;;;;mBAVjB;;;;;;;;;;;;;;;AAgBjB;IAzDwB;;QAUP,qLAAA,CAAA,iBAAc;QACX,qLAAA,CAAA,iBAAc;QAMd,+KAAA,CAAA,eAAY;QACf,4KAAA,CAAA,YAAS;;;MAlBF", "debugId": null}}, {"offset": {"line": 1373, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from \"react\";\nimport SplitText from \"../components/SplitText/SplitText\";\nimport ScrollVelocity from \"../components/ScrollVelocity/ScrollVelocity\";\nimport BlurText from \"../components/BlurText/BlurText\";\nimport FlippableProfileCard from \"../components/FlippableProfileCard\";\nimport { AnimatedTooltip } from \"../components/ui/animated-tooltip\";\nimport Dock from \"../components/Dock/Dock\";\n\nexport default function Home() {\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  useEffect(() => {\n    setIsLoaded(true);\n  }, []);\n\n  // Happy clients data for animated tooltip - using professional photos\n  const happyClients = [\n    {\n      id: 1,\n      name: \"<PERSON>\",\n      designation: \"CEO at TechCorp\",\n      image: \"https://randomuser.me/api/portraits/women/44.jpg\",\n    },\n    {\n      id: 2,\n      name: \"<PERSON>\",\n      designation: \"CTO at StartupXYZ\",\n      image: \"https://randomuser.me/api/portraits/men/32.jpg\",\n    },\n    {\n      id: 3,\n      name: \"<PERSON>\",\n      designation: \"Designer at Creative Co\",\n      image: \"https://randomuser.me/api/portraits/women/68.jpg\",\n    },\n    {\n      id: 4,\n      name: \"David Kim\",\n      designation: \"Founder at InnovateLab\",\n      image: \"https://randomuser.me/api/portraits/men/46.jpg\",\n    },\n  ];\n\n  // Dock navigation items\n  const dockItems = [\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-gray-700\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" />\n        </svg>\n      ),\n      label: \"Home\",\n      onClick: () => console.log(\"Home clicked\"),\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-gray-700\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n        </svg>\n      ),\n      label: \"About\",\n      onClick: () => console.log(\"About clicked\"),\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-gray-700\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n        </svg>\n      ),\n      label: \"Projects\",\n      onClick: () => console.log(\"Projects clicked\"),\n    },\n    {\n      icon: (\n        <svg className=\"w-6 h-6 text-gray-700\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n        </svg>\n      ),\n      label: \"Contact\",\n      onClick: () => console.log(\"Contact clicked\"),\n    },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-white relative overflow-hidden\">\n      {/* Full Background Scrolling Text */}\n      <div className=\"fixed inset-0 flex items-center justify-center pointer-events-none z-0\">\n        <ScrollVelocity\n          texts={[\"make experiences you will never forget\"]}\n          velocity={50}\n          className=\"text-gray-400\"\n          parallaxClassName=\"w-full\"\n          scrollerClassName=\"text-6xl md:text-9xl font-black whitespace-nowrap tracking-wider opacity-50\"\n        />\n      </div>\n\n      {/* Main hero content */}\n      <div className=\"container mx-auto px-6 pt-20 pb-32 relative z-20\">\n        <div className=\"text-center max-w-4xl mx-auto\">\n\n          {/* Profile Card */}\n          <div className=\"relative mb-8\">\n            <div className={`relative z-20 transition-all duration-1000 delay-300 ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}>\n              <FlippableProfileCard\n                imageSrc=\"/profile-placeholder.svg\"\n                altText=\"Abdullah - Full Stack Developer\"\n              />\n            </div>\n          </div>\n\n          {/* Name with Split Text Animation */}\n          <div className={`mb-6 transition-all duration-1000 delay-500 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>\n            <SplitText\n              text=\"Abdullah\"\n              className=\"text-5xl md:text-7xl font-bold text-black\"\n              delay={150}\n              duration={0.8}\n              splitType=\"chars\"\n              from={{ opacity: 0, y: 50, rotateX: -90 }}\n              to={{ opacity: 1, y: 0, rotateX: 0 }}\n            />\n          </div>\n\n          {/* Subtitle with Blur Text Animation - Fixed to stay on one line */}\n          <div className={`mb-12 transition-all duration-1000 delay-700 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>\n            <div className=\"text-lg md:text-xl text-gray-600 whitespace-nowrap\">\n              <BlurText\n                text=\"Full Stack Developer & UI/UX Designer\"\n                className=\"inline-block\"\n                delay={100}\n                animateBy=\"words\"\n                direction=\"bottom\"\n              />\n            </div>\n          </div>\n\n          {/* Clean Client Avatars */}\n          <div className={`flex justify-center mb-12 transition-all duration-1000 delay-900 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            <div className=\"flex items-center\">\n              <AnimatedTooltip items={happyClients} />\n            </div>\n          </div>\n\n          {/* CTA Button */}\n          <div className={`transition-all duration-1000 delay-1100 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            <button className=\"bg-black text-white px-10 py-4 rounded-full font-semibold text-lg hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl\">\n              Let's Work Together!\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Bottom Dock Navigation */}\n      <div className=\"fixed bottom-0 left-0 right-0 z-50\">\n        <Dock\n          items={dockItems}\n          magnification={90}\n          distance={150}\n          baseItemSize={50}\n          panelHeight={64}\n          spring={{ mass: 0.05, stiffness: 200, damping: 15 }}\n        />\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAUe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,YAAY;QACd;yBAAG,EAAE;IAEL,sEAAsE;IACtE,MAAM,eAAe;QACnB;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;QACT;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;QACT;KACD;IAED,wBAAwB;IACxB,MAAM,YAAY;QAChB;YACE,oBACE,6LAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,oBACE,6LAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,oBACE,6LAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;QACA;YACE,oBACE,6LAAC;gBAAI,WAAU;gBAAwB,MAAK;gBAAO,QAAO;gBAAe,SAAQ;0BAC/E,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;YAGzE,OAAO;YACP,SAAS,IAAM,QAAQ,GAAG,CAAC;QAC7B;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,kJAAA,CAAA,UAAc;oBACb,OAAO;wBAAC;qBAAyC;oBACjD,UAAU;oBACV,WAAU;oBACV,mBAAkB;oBAClB,mBAAkB;;;;;;;;;;;0BAKtB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAW,AAAC,wDAAiH,OAA1D,WAAW,0BAA0B;0CAC3G,cAAA,6LAAC,sIAAA,CAAA,UAAoB;oCACnB,UAAS;oCACT,SAAQ;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAW,AAAC,+CAAqF,OAAvC,WAAW,gBAAgB;sCACxF,cAAA,6LAAC,wIAAA,CAAA,UAAS;gCACR,MAAK;gCACL,WAAU;gCACV,OAAO;gCACP,UAAU;gCACV,WAAU;gCACV,MAAM;oCAAE,SAAS;oCAAG,GAAG;oCAAI,SAAS,CAAC;gCAAG;gCACxC,IAAI;oCAAE,SAAS;oCAAG,GAAG;oCAAG,SAAS;gCAAE;;;;;;;;;;;sCAKvC,6LAAC;4BAAI,WAAW,AAAC,gDAAsF,OAAvC,WAAW,gBAAgB;sCACzF,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,sIAAA,CAAA,UAAQ;oCACP,MAAK;oCACL,WAAU;oCACV,OAAO;oCACP,WAAU;oCACV,WAAU;;;;;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAW,AAAC,oEAAsI,OAAnE,WAAW,8BAA8B;sCAC3H,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,2IAAA,CAAA,kBAAe;oCAAC,OAAO;;;;;;;;;;;;;;;;sCAK5B,6LAAC;4BAAI,WAAW,AAAC,2CAA6G,OAAnE,WAAW,8BAA8B;sCAClG,cAAA,6LAAC;gCAAO,WAAU;0CAA4J;;;;;;;;;;;;;;;;;;;;;;0BAQpL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,8HAAA,CAAA,UAAI;oBACH,OAAO;oBACP,eAAe;oBACf,UAAU;oBACV,cAAc;oBACd,aAAa;oBACb,QAAQ;wBAAE,MAAM;wBAAM,WAAW;wBAAK,SAAS;oBAAG;;;;;;;;;;;;;;;;;AAK5D;GA7JwB;KAAA", "debugId": null}}]}