/**
 * @license lucide-react v0.535.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M2.048 18.566A2 2 0 0 0 4 21h16a2 2 0 0 0 1.952-2.434l-2-9A2 2 0 0 0 18 8H6a2 2 0 0 0-1.952 1.566z",
      key: "1qbui5"
    }
  ],
  ["path", { d: "M8 11V6a4 4 0 0 1 8 0v5", key: "tcht90" }]
];
const Handbag = createLucideIcon("handbag", __iconNode);

export { __iconNode, Handbag as default };
//# sourceMappingURL=handbag.js.map
