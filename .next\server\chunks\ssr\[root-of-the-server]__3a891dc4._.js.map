{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/portfolio/main/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport Image from \"next/image\";\nimport { useEffect, useState } from \"react\";\n\nexport default function Home() {\n  const [isLoaded, setIsLoaded] = useState(false);\n\n  useEffect(() => {\n    setIsLoaded(true);\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-white relative overflow-hidden\">\n      {/* Decorative top scribble */}\n      <div className=\"absolute top-8 left-1/2 transform -translate-x-1/2 w-96 h-16\">\n        <svg\n          viewBox=\"0 0 400 60\"\n          className={`w-full h-full transition-all duration-1000 ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}\n        >\n          <path\n            d=\"M20 30 Q100 10 200 25 T380 30\"\n            stroke=\"#000\"\n            strokeWidth=\"3\"\n            fill=\"none\"\n            className=\"animate-pulse\"\n          />\n          <circle cx=\"150\" cy=\"20\" r=\"3\" fill=\"#ff6b35\" className=\"animate-bounce\" />\n          <circle cx=\"250\" cy=\"35\" r=\"2\" fill=\"#ff6b35\" className=\"animate-bounce delay-300\" />\n        </svg>\n      </div>\n\n      {/* Main hero content */}\n      <div className=\"container mx-auto px-6 pt-32 pb-20 relative z-10\">\n        <div className=\"text-center max-w-4xl mx-auto\">\n          {/* Main heading */}\n          <div className={`transition-all duration-1000 delay-300 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            <h1 className=\"text-5xl md:text-6xl font-bold mb-4\">\n              Hi, I'm <span className=\"italic text-black\">Abdullah</span>!\n            </h1>\n          </div>\n\n          {/* Subtitle */}\n          <div className={`transition-all duration-1000 delay-500 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            <p className=\"text-lg md:text-xl text-gray-600 mb-12\">\n              Full Stack Developer & UI/UX Designer\n            </p>\n          </div>\n\n          {/* Profile image with background text */}\n          <div className=\"relative mb-12\">\n            {/* Background large text */}\n            <div className=\"absolute inset-0 flex items-center justify-center pointer-events-none overflow-hidden\">\n              <div className={`text-[12rem] md:text-[20rem] font-black text-gray-100 select-none transition-all duration-1500 delay-700 ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-110'}`}>\n                ABDULLAH\n              </div>\n            </div>\n\n            {/* Profile image */}\n            <div className={`relative z-10 transition-all duration-1000 delay-900 ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}>\n              <div className=\"w-48 h-48 md:w-64 md:h-64 mx-auto rounded-3xl bg-gradient-to-br from-blue-400 to-purple-500 p-1 shadow-2xl hover:shadow-3xl transition-shadow duration-300 hover:scale-105\">\n                <div className=\"w-full h-full rounded-3xl overflow-hidden\">\n                  <Image\n                    src=\"/profile-placeholder.svg\"\n                    alt=\"Abdullah - Full Stack Developer\"\n                    width={256}\n                    height={256}\n                    className=\"w-full h-full object-cover hover:scale-110 transition-transform duration-500\"\n                    priority\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Social icons */}\n          <div className={`flex justify-center gap-4 mb-12 transition-all duration-1000 delay-1100 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            <div className=\"flex items-center gap-2 text-sm text-gray-600\">\n              <span className=\"animate-pulse\">🎉</span>\n              <span className=\"animate-pulse delay-150\">😊</span>\n              <span className=\"animate-pulse delay-300\">🚀</span>\n              <span className=\"ml-2\">80+ Happy Clients</span>\n            </div>\n          </div>\n\n          {/* CTA Button */}\n          <div className={`transition-all duration-1000 delay-1300 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>\n            <button className=\"bg-black text-white px-8 py-4 rounded-full font-medium text-lg hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl\">\n              Let's Work Together!\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Decorative bottom scribble */}\n      <div className=\"absolute bottom-8 right-8 w-32 h-16\">\n        <svg\n          viewBox=\"0 0 120 60\"\n          className={`w-full h-full transition-all duration-1000 delay-1500 ${isLoaded ? 'opacity-100 rotate-0' : 'opacity-0 rotate-12'}`}\n        >\n          <path\n            d=\"M10 50 Q30 20 60 40 Q90 60 110 30\"\n            stroke=\"#000\"\n            strokeWidth=\"2\"\n            fill=\"none\"\n          />\n        </svg>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAQ;oBACR,WAAW,CAAC,2CAA2C,EAAE,WAAW,0BAA0B,sBAAsB;;sCAEpH,8OAAC;4BACC,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,MAAK;4BACL,WAAU;;;;;;sCAEZ,8OAAC;4BAAO,IAAG;4BAAM,IAAG;4BAAK,GAAE;4BAAI,MAAK;4BAAU,WAAU;;;;;;sCACxD,8OAAC;4BAAO,IAAG;4BAAM,IAAG;4BAAK,GAAE;4BAAI,MAAK;4BAAU,WAAU;;;;;;;;;;;;;;;;;0BAK5D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAW,CAAC,uCAAuC,EAAE,WAAW,8BAA8B,2BAA2B;sCAC5H,cAAA,8OAAC;gCAAG,WAAU;;oCAAsC;kDAC1C,8OAAC;wCAAK,WAAU;kDAAoB;;;;;;oCAAe;;;;;;;;;;;;sCAK/D,8OAAC;4BAAI,WAAW,CAAC,uCAAuC,EAAE,WAAW,8BAA8B,2BAA2B;sCAC5H,cAAA,8OAAC;gCAAE,WAAU;0CAAyC;;;;;;;;;;;sCAMxD,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAW,CAAC,yGAAyG,EAAE,WAAW,0BAA0B,uBAAuB;kDAAE;;;;;;;;;;;8CAM5L,8OAAC;oCAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW,0BAA0B,sBAAsB;8CACjI,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;gDACR,WAAU;gDACV,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQlB,8OAAC;4BAAI,WAAW,CAAC,wEAAwE,EAAE,WAAW,8BAA8B,2BAA2B;sCAC7J,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;kDAA0B;;;;;;kDAC1C,8OAAC;wCAAK,WAAU;kDAA0B;;;;;;kDAC1C,8OAAC;wCAAK,WAAU;kDAAO;;;;;;;;;;;;;;;;;sCAK3B,8OAAC;4BAAI,WAAW,CAAC,wCAAwC,EAAE,WAAW,8BAA8B,2BAA2B;sCAC7H,cAAA,8OAAC;gCAAO,WAAU;0CAAyJ;;;;;;;;;;;;;;;;;;;;;;0BAQjL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAQ;oBACR,WAAW,CAAC,sDAAsD,EAAE,WAAW,yBAAyB,uBAAuB;8BAE/H,cAAA,8OAAC;wBACC,GAAE;wBACF,QAAO;wBACP,aAAY;wBACZ,MAAK;;;;;;;;;;;;;;;;;;;;;;AAMjB", "debugId": null}}]}