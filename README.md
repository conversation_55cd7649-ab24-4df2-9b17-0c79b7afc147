# <PERSON>'s Portfolio Website

A modern, animated portfolio website built with Next.js, Tailwind CSS, and ReactBits components.

## 🚀 Getting Started

This project uses <PERSON><PERSON> as the package manager and includes various animated components from ReactBits.

### Prerequisites

- Node.js 18+
- Bun package manager

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   bun install
   ```

3. Run the development server:
   ```bash
   bun run dev
   ```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## 📦 ReactBits Components Installation

The following ReactBits components are used in this project. Install them using these commands:

### Text Animations

```bash
# Split Text Animation (for name "<PERSON>")
bunx jsrepo add https://reactbits.dev/ts/tailwind/TextAnimations/SplitText

# Scroll Velocity Animation (for background text "make experiences you will never forget")
bunx jsrepo add https://reactbits.dev/ts/tailwind/TextAnimations/ScrollVelocity

# Blur Text Animation (for "Full Stack Developer & UI/UX Designer")
bunx jsrepo add https://reactbits.dev/ts/tailwind/TextAnimations/BlurText

# Circular Text Animation (for "scroll for more info about me")
bunx jsrepo add https://reactbits.dev/ts/tailwind/TextAnimations/CircularText
```

### Components

```bash
# Tilted Card Component (for profile card with flip functionality)
bunx jsrepo add https://reactbits.dev/ts/tailwind/Components/TiltedCard
```

## 🎨 Features

- **Animated Hero Section**: Clean, modern design with smooth animations
- **Split Text Animation**: Name appears with letter-by-letter animation
- **Background Scroll Text**: Continuous scrolling background message
- **Interactive Profile Card**: Clickable card with flip animation and hover effects
- **Blur Text Effects**: Smooth text reveal animations
- **Circular Text**: Rotating text on card back side
- **Responsive Design**: Works perfectly on all devices
- **Light Theme**: Clean, professional appearance

## 🛠️ Tech Stack

- **Framework**: Next.js 15
- **Styling**: Tailwind CSS
- **Animations**: ReactBits components
- **Package Manager**: Bun
- **Language**: TypeScript

## 📁 Project Structure

```
├── app/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/
│   └── [ReactBits components]
├── public/
│   └── profile-placeholder.svg
└── README.md
```

## 🎯 Component Usage

### SplitText
Used for the main name animation with customizable delay and animation type.

### ScrollVelocity
Creates infinite scrolling background text with adjustable speed and direction.

### TiltedCard
Interactive profile card with 3D tilt effects and flip functionality.

### BlurText
Smooth text reveal animation for subtitle text.

### CircularText
Rotating circular text animation for the card back side.

## 🚀 Deployment

Build the project for production:

```bash
bun run build
```

The project is optimized for deployment on Vercel, Netlify, or any static hosting platform.

## 📝 Notes

- All environment variables should be placed in `.env.local` (not `.env`)
- The project uses Bun instead of npm/yarn for faster package management
- ReactBits components are installed in the `components/` directory

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!
