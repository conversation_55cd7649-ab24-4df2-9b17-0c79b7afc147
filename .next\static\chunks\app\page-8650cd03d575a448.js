(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{4957:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>C});var s=l(5155),a=l(2115),n=l(802),r=l(9088),i=l(5228);n.os.registerPlugin(r.u,i.A);let o=e=>{let{text:t,className:l="",delay:r=100,duration:o=.6,ease:c="power3.out",splitType:d="chars",from:u={opacity:0,y:40},to:p={opacity:1,y:0},threshold:m=.1,rootMargin:f="-100px",textAlign:x="center",onLetterAnimationComplete:h}=e,y=(0,a.useRef)(null),g=(0,a.useRef)(!1),v=(0,a.useRef)(null);return(0,a.useEffect)(()=>{let e,l;if(!y.current||!t)return;let s=y.current;g.current=!1;let a="lines"===d;a&&(s.style.position="relative");try{e=new i.A(s,{type:d,absolute:a,linesClass:"split-line"})}catch(e){console.error("Failed to create SplitText:",e);return}switch(d){case"lines":l=e.lines;break;case"words":l=e.words;break;default:l=e.chars}if(!l||0===l.length){console.warn("No targets found for SplitText animation"),e.revert();return}l.forEach(e=>{e.style.willChange="transform, opacity"});let x=(1-m)*100,b=/^(-?\d+(?:\.\d+)?)(px|em|rem|%)?$/.exec(f),w=b?parseFloat(b[1]):0,j=b&&b[2]||"px",N=w<0?"-=".concat(Math.abs(w)).concat(j):"+=".concat(w).concat(j),k="top ".concat(x,"%").concat(N),C=n.os.timeline({scrollTrigger:{trigger:s,start:k,toggleActions:"play none none none",once:!0,onToggle:e=>{v.current=e}},smoothChildTiming:!0,onComplete:()=>{g.current=!0,n.os.set(l,{...p,clearProps:"willChange",immediateRender:!0}),null==h||h()}});return C.set(l,{...u,immediateRender:!1,force3D:!0}),C.to(l,{...p,duration:o,ease:c,stagger:r/1e3,force3D:!0}),()=>{C.kill(),v.current&&(v.current.kill(),v.current=null),n.os.killTweensOf(l),e&&e.revert()}},[t,r,o,c,d,u,p,m,f,h]),(0,s.jsx)("p",{ref:y,className:"split-parent overflow-hidden inline-block whitespace-normal ".concat(l),style:{textAlign:x,wordWrap:"break-word"},children:t})};var c=l(8619),d=l(2720),u=l(6836),p=l(7602),m=l(8829),f=l(4087),x=l(6295);let h=e=>{let{scrollContainerRef:t,texts:l=[],velocity:n=100,className:r="",damping:i=50,stiffness:o=400,numCopies:h=6,velocityMapping:y={input:[0,1e3],output:[0,5]},parallaxClassName:g,scrollerClassName:v,parallaxStyle:b,scrollerStyle:w}=e;function j(e){let{children:t,baseVelocity:l=n,scrollContainerRef:r,className:i="",damping:o,stiffness:h,numCopies:y,velocityMapping:g,parallaxClassName:v,scrollerClassName:b,parallaxStyle:w,scrollerStyle:j}=e,N=(0,c.d)(0),{scrollY:k}=(0,d.L)(r?{container:r}:{}),C=(0,u.V)(k),D=(0,p.z)(C,{damping:null!=o?o:50,stiffness:null!=h?h:400}),E=(0,m.G)(D,(null==g?void 0:g.input)||[0,1e3],(null==g?void 0:g.output)||[0,5],{clamp:!1}),S=(0,a.useRef)(null),T=function(e){let[t,l]=(0,a.useState)(0);return(0,a.useLayoutEffect)(()=>{function t(){e.current&&l(e.current.offsetWidth)}return t(),window.addEventListener("resize",t),()=>window.removeEventListener("resize",t)},[e]),t}(S),z=(0,m.G)(N,e=>0===T?"0px":"".concat(function(e,t,l){let s=0-e;return((l-e)%s+s)%s+e}(-T,0,e),"px")),M=(0,a.useRef)(1);(0,f.N)((e,t)=>{let s=M.current*l*(t/1e3);0>E.get()?M.current=-1:E.get()>0&&(M.current=1),s+=M.current*s*E.get(),N.set(N.get()+s)});let P=[];for(let e=0;e<y;e++)P.push((0,s.jsx)("span",{className:"flex-shrink-0 ".concat(i),ref:0===e?S:null,children:t},e));return(0,s.jsx)("div",{className:"".concat(v," relative overflow-hidden"),style:w,children:(0,s.jsx)(x.P.div,{className:"".concat(b," flex whitespace-nowrap text-center font-sans text-4xl font-bold tracking-[-0.02em] drop-shadow md:text-[5rem] md:leading-[5rem]"),style:{x:z,...j},children:P})})}return(0,s.jsx)("section",{children:l.map((e,l)=>(0,s.jsxs)(j,{className:r,baseVelocity:l%2!=0?-n:n,scrollContainerRef:t,damping:i,stiffness:o,numCopies:h,velocityMapping:y,parallaxClassName:g,scrollerClassName:v,parallaxStyle:b,scrollerStyle:w,children:[e,"\xa0"]},l))})},y=e=>{let{text:t="",delay:l=200,className:n="",animateBy:r="words",direction:i="top",threshold:o=.1,rootMargin:c="0px",animationFrom:d,animationTo:u,easing:p=e=>e,onAnimationComplete:m,stepDuration:f=.35}=e,h="words"===r?t.split(" "):t.split(""),[y,g]=(0,a.useState)(!1),v=(0,a.useRef)(null);(0,a.useEffect)(()=>{if(!v.current)return;let e=new IntersectionObserver(t=>{let[l]=t;l.isIntersecting&&(g(!0),e.unobserve(v.current))},{threshold:o,rootMargin:c});return e.observe(v.current),()=>e.disconnect()},[o,c]);let b=(0,a.useMemo)(()=>"top"===i?{filter:"blur(10px)",opacity:0,y:-50}:{filter:"blur(10px)",opacity:0,y:50},[i]),w=(0,a.useMemo)(()=>[{filter:"blur(5px)",opacity:.5,y:"top"===i?5:-5},{filter:"blur(0px)",opacity:1,y:0}],[i]),j=null!=d?d:b,N=null!=u?u:w,k=N.length+1,C=f*(k-1),D=Array.from({length:k},(e,t)=>1===k?0:t/(k-1));return(0,s.jsx)("p",{ref:v,className:"blur-text ".concat(n," flex flex-wrap"),children:h.map((e,t)=>{let a=((e,t)=>{let l=new Set([...Object.keys(e),...t.flatMap(e=>Object.keys(e))]),s={};return l.forEach(l=>{s[l]=[e[l],...t.map(e=>e[l])]}),s})(j,N),n={duration:C,times:D,delay:t*l/1e3};return n.ease=p,(0,s.jsxs)(x.P.span,{initial:j,animate:y?a:j,transition:n,onAnimationComplete:t===h.length-1?m:void 0,style:{display:"inline-block",willChange:"transform, filter, opacity"},children:[" "===e?"\xa0":e,"words"===r&&t<h.length-1&&"\xa0"]},t)})})},g={damping:30,stiffness:100,mass:2};function v(e){let{imageSrc:t,altText:l="Tilted card image",captionText:n="",containerHeight:r="300px",containerWidth:i="100%",imageHeight:o="300px",imageWidth:d="300px",scaleOnHover:u=1.1,rotateAmplitude:m=14,showMobileWarning:f=!0,showTooltip:h=!0,overlayContent:y=null,displayOverlayContent:v=!1}=e,b=(0,a.useRef)(null),w=(0,c.d)(0),j=(0,c.d)(0),N=(0,p.z)((0,c.d)(0),g),k=(0,p.z)((0,c.d)(0),g),C=(0,p.z)(1,g),D=(0,p.z)(0),E=(0,p.z)(0,{stiffness:350,damping:30,mass:1}),[S,T]=(0,a.useState)(0);return(0,s.jsxs)("figure",{ref:b,className:"relative w-full h-full [perspective:800px] flex flex-col items-center justify-center",style:{height:r,width:i},onMouseMove:function(e){if(!b.current)return;let t=b.current.getBoundingClientRect(),l=e.clientX-t.left-t.width/2,s=e.clientY-t.top-t.height/2,a=-(s/(t.height/2)*m),n=l/(t.width/2)*m;N.set(a),k.set(n),w.set(e.clientX-t.left),j.set(e.clientY-t.top);let r=s-S;E.set(-(.6*r)),T(s)},onMouseEnter:function(){C.set(u),D.set(1)},onMouseLeave:function(){D.set(0),C.set(1),N.set(0),k.set(0),E.set(0)},children:[f&&(0,s.jsx)("div",{className:"absolute top-4 text-center text-sm block sm:hidden",children:"This effect is not optimized for mobile. Check on desktop."}),(0,s.jsxs)(x.P.div,{className:"relative [transform-style:preserve-3d]",style:{width:d,height:o,rotateX:N,rotateY:k,scale:C},children:[(0,s.jsx)(x.P.img,{src:t,alt:l,className:"absolute top-0 left-0 object-cover rounded-[15px] will-change-transform [transform:translateZ(0)]",style:{width:d,height:o}}),v&&y&&(0,s.jsx)(x.P.div,{className:"absolute top-0 left-0 z-[2] will-change-transform [transform:translateZ(30px)]",children:y})]}),h&&(0,s.jsx)(x.P.figcaption,{className:"pointer-events-none absolute left-0 top-0 rounded-[4px] bg-white px-[10px] py-[4px] text-[10px] text-[#2d2d2d] opacity-0 z-[3] hidden sm:block",style:{x:w,y:j,opacity:D,rotate:E},children:n})]})}var b=l(6896);let w=function(e,t){let l=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return{from:t,to:t+360,ease:"linear",duration:e,type:"tween",repeat:l?1/0:0}},j=(e,t)=>({rotate:w(e,t),scale:{type:"spring",damping:20,stiffness:300}}),N=e=>{let{text:t,spinDuration:l=20,onHover:n="speedUp",className:r=""}=e,i=Array.from(t),o=(0,b.s)(),d=(0,c.d)(0);return(0,a.useEffect)(()=>{let e=d.get();o.start({rotate:e+360,scale:1,transition:j(l,e)})},[l,t,n,o]),(0,s.jsx)(x.P.div,{className:"m-0 mx-auto rounded-full w-[200px] h-[200px] relative font-black text-white text-center cursor-pointer origin-center ".concat(r),style:{rotate:d},initial:{rotate:0},animate:o,onMouseEnter:()=>{let e,t=d.get();if(!n)return;let s=1;switch(n){case"slowDown":e=j(2*l,t);break;case"speedUp":e=j(l/4,t);break;case"pause":e={rotate:{type:"spring",damping:20,stiffness:300},scale:{type:"spring",damping:20,stiffness:300}};break;case"goBonkers":e=j(l/20,t),s=.8;break;default:e=j(l,t)}o.start({rotate:t+360,scale:s,transition:e})},onMouseLeave:()=>{let e=d.get();o.start({rotate:e+360,scale:1,transition:j(l,e)})},children:i.map((e,t)=>{let l=360/i.length*t,a=Math.PI/i.length,n="rotateZ(".concat(l,"deg) translate3d(").concat(a*t,"px, ").concat(a*t,"px, 0)");return(0,s.jsx)("span",{className:"absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]",style:{transform:n,WebkitTransform:n},children:e},t)})})};function k(e){let{imageSrc:t,altText:l="Abdullah - Full Stack Developer"}=e,[n,r]=(0,a.useState)(!1);return(0,s.jsx)("div",{className:"relative w-64 h-64 md:w-80 md:h-80 mx-auto [perspective:1000px]",children:(0,s.jsxs)(x.P.div,{className:"relative w-full h-full [transform-style:preserve-3d] cursor-pointer",animate:{rotateY:180*!!n},transition:{duration:.8,ease:"easeInOut"},onClick:()=>{r(!n)},children:[(0,s.jsx)("div",{className:"absolute inset-0 w-full h-full [backface-visibility:hidden]",children:(0,s.jsx)(v,{imageSrc:t,altText:l,captionText:"you can click it",containerHeight:"100%",containerWidth:"100%",imageHeight:"100%",imageWidth:"100%",scaleOnHover:1.05,rotateAmplitude:12,showMobileWarning:!1,showTooltip:!0})}),(0,s.jsx)("div",{className:"absolute inset-0 w-full h-full [backface-visibility:hidden] [transform:rotateY(180deg)]",children:(0,s.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 rounded-3xl flex items-center justify-center shadow-2xl",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(N,{text:"scroll for more info about me • ",spinDuration:15,onHover:"speedUp",className:"text-white"}),(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsx)("div",{className:"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-white animate-bounce",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})})})]})})})]})})}function C(){let[e,t]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{t(!0)},[]),(0,s.jsxs)("div",{className:"min-h-screen bg-white relative overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center justify-center pointer-events-none opacity-10 z-0",children:(0,s.jsx)(h,{texts:["make experiences you will never forget"],velocity:50,className:"text-gray-800",parallaxClassName:"w-full",scrollerClassName:"text-6xl md:text-8xl font-black"})}),(0,s.jsx)("div",{className:"container mx-auto px-6 pt-20 pb-20 relative z-10",children:(0,s.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,s.jsx)("div",{className:"mb-8 transition-all duration-1000 delay-300 ".concat(e?"opacity-100 scale-100":"opacity-0 scale-95"),children:(0,s.jsx)(k,{imageSrc:"/profile-placeholder.svg",altText:"Abdullah - Full Stack Developer"})}),(0,s.jsx)("div",{className:"mb-6 transition-all duration-1000 delay-500 ".concat(e?"opacity-100":"opacity-0"),children:(0,s.jsx)(o,{text:"Abdullah",className:"text-5xl md:text-7xl font-bold text-black",delay:150,duration:.8,splitType:"chars",from:{opacity:0,y:50,rotateX:-90},to:{opacity:1,y:0,rotateX:0}})}),(0,s.jsx)("div",{className:"mb-12 transition-all duration-1000 delay-700 ".concat(e?"opacity-100":"opacity-0"),children:(0,s.jsx)(y,{text:"Full Stack Developer & UI/UX Designer",className:"text-lg md:text-xl text-gray-600",delay:100,animateBy:"words",direction:"bottom"})}),(0,s.jsx)("div",{className:"flex justify-center gap-4 mb-12 transition-all duration-1000 delay-900 ".concat(e?"opacity-100 translate-y-0":"opacity-0 translate-y-8"),children:(0,s.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,s.jsx)("span",{className:"animate-pulse",children:"\uD83C\uDF89"}),(0,s.jsx)("span",{className:"animate-pulse delay-150",children:"\uD83D\uDE0A"}),(0,s.jsx)("span",{className:"animate-pulse delay-300",children:"\uD83D\uDE80"}),(0,s.jsx)("span",{className:"ml-2",children:"80+ Happy Clients"})]})}),(0,s.jsx)("div",{className:"transition-all duration-1000 delay-1100 ".concat(e?"opacity-100 translate-y-0":"opacity-0 translate-y-8"),children:(0,s.jsx)("button",{className:"bg-black text-white px-8 py-4 rounded-full font-medium text-lg hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl",children:"Let's Work Together!"})})]})})]})}},6983:(e,t,l)=>{Promise.resolve().then(l.bind(l,4957))}},e=>{e.O(0,[592,450,441,964,358],()=>e(e.s=6983)),_N_E=e.O()}]);