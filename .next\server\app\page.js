(()=>{var a={};a.id=974,a.ids=[974],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},597:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\portfolio\\\\main\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\portfolio\\main\\app\\page.tsx","default")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},2704:()=>{},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3401:(a,b,c)=>{Promise.resolve().then(c.bind(c,597))},3873:a=>{"use strict";a.exports=require("path")},6055:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},6351:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,597)),"D:\\portfolio\\main\\app\\page.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,6055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,8014)),"D:\\portfolio\\main\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,6055))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["D:\\portfolio\\main\\app\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||b instanceof A.NoFallbackError||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},7434:()=>{},7590:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},7693:(a,b,c)=>{"use strict";let d,e,f;c.r(b),c.d(b,{default:()=>nj});var g=c(687),h=c(3210);function i(a){if(void 0===a)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return a}function j(a,b){a.prototype=Object.create(b.prototype),a.prototype.constructor=a,a.__proto__=b}var k,l,m,n,o,p,q,r,s,t,u,v={autoSleep:120,force3D:"auto",nullTargetWarn:1,units:{lineHeight:""}},w={duration:.5,overwrite:!1,delay:0},x=2*Math.PI,y=x/4,z=0,A=Math.sqrt,B=Math.cos,C=Math.sin,D=function(a){return"string"==typeof a},E=function(a){return"function"==typeof a},F=function(a){return"number"==typeof a},G=function(a){return void 0===a},H=function(a){return"object"==typeof a},I=function(a){return!1!==a},J=function(){return"undefined"!=typeof window},K=function(a){return E(a)||D(a)},L="function"==typeof ArrayBuffer&&ArrayBuffer.isView||function(){},M=Array.isArray,N=/(?:-?\.?\d|\.)+/gi,O=/[-+=.]*\d+[.e\-+]*\d*[e\-+]*\d*/g,P=/[-+=.]*\d+[.e-]*\d*[a-z%]*/g,Q=/[-+=.]*\d+\.?\d*(?:e-|e\+)?\d*/gi,R=/[+-]=-?[.\d]+/,S=/[^,'"\[\]\s]+/gi,T=/^[+\-=e\s\d]*\d+[.\d]*([a-z]*|%)\s*$/i,U={},V={},W=function(a){return(V=ay(a,U))&&cm},X=function(a,b){return console.warn("Invalid property",a,"set to",b,"Missing plugin? gsap.registerPlugin()")},Y=function(a,b){return!b&&console.warn(a)},Z=function(a,b){return a&&(U[a]=b)&&V&&(V[a]=b)||U},$=function(){return 0},_={suppressEvents:!0,isStart:!0,kill:!1},aa={suppressEvents:!0,kill:!1},ab={suppressEvents:!0},ac={},ad=[],ae={},af={},ag={},ah=30,ai=[],aj="",ak=function(a){var b,c,d=a[0];if(H(d)||E(d)||(a=[a]),!(b=(d._gsap||{}).harness)){for(c=ai.length;c--&&!ai[c].targetTest(d););b=ai[c]}for(c=a.length;c--;)a[c]&&(a[c]._gsap||(a[c]._gsap=new bG(a[c],b)))||a.splice(c,1);return a},al=function(a){return a._gsap||ak(a4(a))[0]._gsap},am=function(a,b,c){return(c=a[b])&&E(c)?a[b]():G(c)&&a.getAttribute&&a.getAttribute(b)||c},an=function(a,b){return(a=a.split(",")).forEach(b)||a},ao=function(a){return Math.round(1e5*a)/1e5||0},ap=function(a){return Math.round(1e7*a)/1e7||0},aq=function(a,b){var c=b.charAt(0),d=parseFloat(b.substr(2));return a=parseFloat(a),"+"===c?a+d:"-"===c?a-d:"*"===c?a*d:a/d},ar=function(a,b){for(var c=b.length,d=0;0>a.indexOf(b[d])&&++d<c;);return d<c},as=function(){var a,b,c=ad.length,d=ad.slice(0);for(a=0,ae={},ad.length=0;a<c;a++)(b=d[a])&&b._lazy&&(b.render(b._lazy[0],b._lazy[1],!0)._lazy=0)},at=function(a){return!!(a._initted||a._startAt||a.add)},au=function(a,b,c,d){ad.length&&!l&&as(),a.render(b,c,d||!!(l&&b<0&&at(a))),ad.length&&!l&&as()},av=function(a){var b=parseFloat(a);return(b||0===b)&&(a+"").match(S).length<2?b:D(a)?a.trim():a},aw=function(a){return a},ax=function(a,b){for(var c in b)c in a||(a[c]=b[c]);return a},ay=function(a,b){for(var c in b)a[c]=b[c];return a},az=function a(b,c){for(var d in c)"__proto__"!==d&&"constructor"!==d&&"prototype"!==d&&(b[d]=H(c[d])?a(b[d]||(b[d]={}),c[d]):c[d]);return b},aA=function(a,b){var c,d={};for(c in a)c in b||(d[c]=a[c]);return d},aB=function(a){var b,c=a.parent||n,d=a.keyframes?(b=M(a.keyframes),function(a,c){for(var d in c)d in a||"duration"===d&&b||"ease"===d||(a[d]=c[d])}):ax;if(I(a.inherit))for(;c;)d(a,c.vars.defaults),c=c.parent||c._dp;return a},aC=function(a,b){for(var c=a.length,d=c===b.length;d&&c--&&a[c]===b[c];);return c<0},aD=function(a,b,c,d,e){void 0===c&&(c="_first"),void 0===d&&(d="_last");var f,g=a[d];if(e)for(f=b[e];g&&g[e]>f;)g=g._prev;return g?(b._next=g._next,g._next=b):(b._next=a[c],a[c]=b),b._next?b._next._prev=b:a[d]=b,b._prev=g,b.parent=b._dp=a,b},aE=function(a,b,c,d){void 0===c&&(c="_first"),void 0===d&&(d="_last");var e=b._prev,f=b._next;e?e._next=f:a[c]===b&&(a[c]=f),f?f._prev=e:a[d]===b&&(a[d]=e),b._next=b._prev=b.parent=null},aF=function(a,b){a.parent&&(!b||a.parent.autoRemoveChildren)&&a.parent.remove&&a.parent.remove(a),a._act=0},aG=function(a,b){if(a&&(!b||b._end>a._dur||b._start<0))for(var c=a;c;)c._dirty=1,c=c.parent;return a},aH=function(a){for(var b=a.parent;b&&b.parent;)b._dirty=1,b.totalDuration(),b=b.parent;return a},aI=function(a,b,c,d){return a._startAt&&(l?a._startAt.revert(aa):a.vars.immediateRender&&!a.vars.autoRevert||a._startAt.render(b,!0,d))},aJ=function(a){return a._repeat?aK(a._tTime,a=a.duration()+a._rDelay)*a:0},aK=function(a,b){var c=Math.floor(a=ap(a/b));return a&&c===a?c-1:c},aL=function(a,b){return(a-b._start)*b._ts+(b._ts>=0?0:b._dirty?b.totalDuration():b._tDur)},aM=function(a){return a._end=ap(a._start+(a._tDur/Math.abs(a._ts||a._rts||1e-8)||0))},aN=function(a,b){var c=a._dp;return c&&c.smoothChildTiming&&a._ts&&(a._start=ap(c._time-(a._ts>0?b/a._ts:-(((a._dirty?a.totalDuration():a._tDur)-b)/a._ts))),aM(a),c._dirty||aG(c,a)),a},aO=function(a,b){var c;if((b._time||!b._dur&&b._initted||b._start<a._time&&(b._dur||!b.add))&&(c=aL(a.rawTime(),b),(!b._dur||a0(0,b.totalDuration(),c)-b._tTime>1e-8)&&b.render(c,!0)),aG(a,b)._dp&&a._initted&&a._time>=a._dur&&a._ts){if(a._dur<a.duration())for(c=a;c._dp;)c.rawTime()>=0&&c.totalTime(c._tTime),c=c._dp;a._zTime=-1e-8}},aP=function(a,b,c,d){return b.parent&&aF(b),b._start=ap((F(c)?c:c||a!==n?aZ(a,c,b):a._time)+b._delay),b._end=ap(b._start+(b.totalDuration()/Math.abs(b.timeScale())||0)),aD(a,b,"_first","_last",a._sort?"_start":0),aT(b)||(a._recent=b),d||aO(a,b),a._ts<0&&aN(a,a._tTime),a},aQ=function(a,b){return(U.ScrollTrigger||X("scrollTrigger",b))&&U.ScrollTrigger.create(b,a)},aR=function(a,b,c,d,e){return(bP(a,b,e),a._initted)?!c&&a._pt&&!l&&(a._dur&&!1!==a.vars.lazy||!a._dur&&a.vars.lazy)&&s!==br.frame?(ad.push(a),a._lazy=[e,d],1):void 0:1},aS=function a(b){var c=b.parent;return c&&c._ts&&c._initted&&!c._lock&&(0>c.rawTime()||a(c))},aT=function(a){var b=a.data;return"isFromStart"===b||"isStart"===b},aU=function(a,b,c,d){var e,f,g,h=a.ratio,i=b<0||!b&&(!a._start&&aS(a)&&!(!a._initted&&aT(a))||(a._ts<0||a._dp._ts<0)&&!aT(a))?0:1,j=a._rDelay,k=0;if(j&&a._repeat&&(f=aK(k=a0(0,a._tDur,b),j),a._yoyo&&1&f&&(i=1-i),f!==aK(a._tTime,j)&&(h=1-i,a.vars.repeatRefresh&&a._initted&&a.invalidate())),i!==h||l||d||1e-8===a._zTime||!b&&a._zTime){if(!a._initted&&aR(a,b,d,c,k))return;for(g=a._zTime,a._zTime=b||1e-8*!!c,c||(c=b&&!g),a.ratio=i,a._from&&(i=1-i),a._time=0,a._tTime=k,e=a._pt;e;)e.r(i,e.d),e=e._next;b<0&&aI(a,b,c,!0),a._onUpdate&&!c&&bf(a,"onUpdate"),k&&a._repeat&&!c&&a.parent&&bf(a,"onRepeat"),(b>=a._tDur||b<0)&&a.ratio===i&&(i&&aF(a,1),c||l||(bf(a,i?"onComplete":"onReverseComplete",!0),a._prom&&a._prom()))}else a._zTime||(a._zTime=b)},aV=function(a,b,c){var d;if(c>b)for(d=a._first;d&&d._start<=c;){if("isPause"===d.data&&d._start>b)return d;d=d._next}else for(d=a._last;d&&d._start>=c;){if("isPause"===d.data&&d._start<b)return d;d=d._prev}},aW=function(a,b,c,d){var e=a._repeat,f=ap(b)||0,g=a._tTime/a._tDur;return g&&!d&&(a._time*=f/a._dur),a._dur=f,a._tDur=e?e<0?1e10:ap(f*(e+1)+a._rDelay*e):f,g>0&&!d&&aN(a,a._tTime=a._tDur*g),a.parent&&aM(a),c||aG(a.parent,a),a},aX=function(a){return a instanceof bI?aG(a):aW(a,a._dur)},aY={_start:0,endTime:$,totalDuration:$},aZ=function a(b,c,d){var e,f,g,h=b.labels,i=b._recent||aY,j=b.duration()>=1e8?i.endTime(!1):b._dur;return D(c)&&(isNaN(c)||c in h)?(f=c.charAt(0),g="%"===c.substr(-1),e=c.indexOf("="),"<"===f||">"===f)?(e>=0&&(c=c.replace(/=/,"")),("<"===f?i._start:i.endTime(i._repeat>=0))+(parseFloat(c.substr(1))||0)*(g?(e<0?i:d).totalDuration()/100:1)):e<0?(c in h||(h[c]=j),h[c]):(f=parseFloat(c.charAt(e-1)+c.substr(e+1)),g&&d&&(f=f/100*(M(d)?d[0]:d).totalDuration()),e>1?a(b,c.substr(0,e-1),d)+f:j+f):null==c?j:+c},a$=function(a,b,c){var d,e,f=F(b[1]),g=(f?2:1)+(a<2?0:1),h=b[g];if(f&&(h.duration=b[1]),h.parent=c,a){for(d=h,e=c;e&&!("immediateRender"in d);)d=e.vars.defaults||{},e=I(e.vars.inherit)&&e.parent;h.immediateRender=I(d.immediateRender),a<2?h.runBackwards=1:h.startAt=b[g-1]}return new bW(b[0],h,b[g+1])},a_=function(a,b){return a||0===a?b(a):b},a0=function(a,b,c){return c<a?a:c>b?b:c},a1=function(a,b){return D(a)&&(b=T.exec(a))?b[1]:""},a2=[].slice,a3=function(a,b){return a&&H(a)&&"length"in a&&(!b&&!a.length||a.length-1 in a&&H(a[0]))&&!a.nodeType&&a!==o},a4=function(a,b,c){var d;return m&&!b&&m.selector?m.selector(a):D(a)&&!c&&(p||!bs())?a2.call((b||q).querySelectorAll(a),0):M(a)?(void 0===d&&(d=[]),a.forEach(function(a){var b;return D(a)&&!c||a3(a,1)?(b=d).push.apply(b,a4(a)):d.push(a)})||d):a3(a)?a2.call(a,0):a?[a]:[]},a5=function(a){return a=a4(a)[0]||Y("Invalid scope")||{},function(b){var c=a.current||a.nativeElement||a;return a4(b,c.querySelectorAll?c:c===a?Y("Invalid scope")||q.createElement("div"):a)}},a6=function(a){return a.sort(function(){return .5-Math.random()})},a7=function(a){if(E(a))return a;var b=H(a)?a:{each:a},c=bB(b.ease),d=b.from||0,e=parseFloat(b.base)||0,f={},g=d>0&&d<1,h=isNaN(d)||g,i=b.axis,j=d,k=d;return D(d)?j=k=({center:.5,edges:.5,end:1})[d]||0:!g&&h&&(j=d[0],k=d[1]),function(a,g,l){var m,n,o,p,q,r,s,t,u,v=(l||b).length,w=f[v];if(!w){if(!(u="auto"===b.grid?0:(b.grid||[1,1e8])[1])){for(s=-1e8;s<(s=l[u++].getBoundingClientRect().left)&&u<v;);u<v&&u--}for(r=0,w=f[v]=[],m=h?Math.min(u,v)*j-.5:d%u,n=1e8===u?0:h?v*k/u-.5:d/u|0,s=0,t=1e8;r<v;r++)o=r%u-m,p=n-(r/u|0),w[r]=q=i?Math.abs("y"===i?p:o):A(o*o+p*p),q>s&&(s=q),q<t&&(t=q);"random"===d&&a6(w),w.max=s-t,w.min=t,w.v=v=(parseFloat(b.amount)||parseFloat(b.each)*(u>v?v-1:i?"y"===i?v/u:u:Math.max(u,v/u))||0)*("edges"===d?-1:1),w.b=v<0?e-v:e,w.u=a1(b.amount||b.each)||0,c=c&&v<0?bz(c):c}return v=(w[a]-w.min)/w.max||0,ap(w.b+(c?c(v):v)*w.v)+w.u}},a8=function(a){var b=Math.pow(10,((a+"").split(".")[1]||"").length);return function(c){var d=ap(Math.round(parseFloat(c)/a)*a*b);return(d-d%1)/b+(F(c)?0:a1(c))}},a9=function(a,b){var c,d,e=M(a);return!e&&H(a)&&(c=e=a.radius||1e8,a.values?(d=!F((a=a4(a.values))[0]))&&(c*=c):a=a8(a.increment)),a_(b,e?E(a)?function(b){return Math.abs((d=a(b))-b)<=c?d:b}:function(b){for(var e,f,g=parseFloat(d?b.x:b),h=parseFloat(d?b.y:0),i=1e8,j=0,k=a.length;k--;)(e=d?(e=a[k].x-g)*e+(f=a[k].y-h)*f:Math.abs(a[k]-g))<i&&(i=e,j=k);return j=!c||i<=c?a[j]:b,d||j===b||F(b)?j:j+a1(b)}:a8(a))},ba=function(a,b,c,d){return a_(M(a)?!b:!0===c?(c=0,!1):!d,function(){return M(a)?a[~~(Math.random()*a.length)]:(d=(c=c||1e-5)<1?Math.pow(10,(c+"").length-2):1)&&Math.floor(Math.round((a-c/2+Math.random()*(b-a+.99*c))/c)*c*d)/d})},bb=function(a,b,c){return a_(c,function(c){return a[~~b(c)]})},bc=function(a){for(var b,c,d,e,f=0,g="";~(b=a.indexOf("random(",f));)d=a.indexOf(")",b),e="["===a.charAt(b+7),c=a.substr(b+7,d-b-7).match(e?S:N),g+=a.substr(f,b-f)+ba(e?c:+c[0],e?0:+c[1],+c[2]||1e-5),f=d+1;return g+a.substr(f,a.length-f)},bd=function(a,b,c,d,e){var f=b-a,g=d-c;return a_(e,function(b){return c+((b-a)/f*g||0)})},be=function(a,b,c){var d,e,f,g=a.labels,h=1e8;for(d in g)(e=g[d]-b)<0==!!c&&e&&h>(e=Math.abs(e))&&(f=d,h=e);return f},bf=function(a,b,c){var d,e,f,g=a.vars,h=g[b],i=m,j=a._ctx;if(h)return d=g[b+"Params"],e=g.callbackScope||a,c&&ad.length&&as(),j&&(m=j),f=d?h.apply(e,d):h.call(e),m=i,f},bg=function(a){return aF(a),a.scrollTrigger&&a.scrollTrigger.kill(!!l),1>a.progress()&&bf(a,"onInterrupt"),a},bh=[],bi=function(a){if(a)if(a=!a.name&&a.default||a,J()||a.headless){var b=a.name,c=E(a),d=b&&!c&&a.init?function(){this._props=[]}:a,e={init:$,render:b3,add:bM,kill:b5,modifier:b4,rawVars:0},f={targetTest:0,get:0,getSetter:b_,aliases:{},register:0};if(bs(),a!==d){if(af[b])return;ax(d,ax(aA(a,e),f)),ay(d.prototype,ay(e,aA(a,f))),af[d.prop=b]=d,a.targetTest&&(ai.push(d),ac[b]=1),b=("css"===b?"CSS":b.charAt(0).toUpperCase()+b.substr(1))+"Plugin"}Z(b,d),a.register&&a.register(cm,d,b8)}else bh.push(a)},bj={aqua:[0,255,255],lime:[0,255,0],silver:[192,192,192],black:[0,0,0],maroon:[128,0,0],teal:[0,128,128],blue:[0,0,255],navy:[0,0,128],white:[255,255,255],olive:[128,128,0],yellow:[255,255,0],orange:[255,165,0],gray:[128,128,128],purple:[128,0,128],green:[0,128,0],red:[255,0,0],pink:[255,192,203],cyan:[0,255,255],transparent:[255,255,255,0]},bk=function(a,b,c){return(6*(a+=a<0?1:a>1?-1:0)<1?b+(c-b)*a*6:a<.5?c:3*a<2?b+(c-b)*(2/3-a)*6:b)*255+.5|0},bl=function(a,b,c){var d,e,f,g,h,i,j,k,l,m,n=a?F(a)?[a>>16,a>>8&255,255&a]:0:bj.black;if(!n){if(","===a.substr(-1)&&(a=a.substr(0,a.length-1)),bj[a])n=bj[a];else if("#"===a.charAt(0)){if(a.length<6&&(d=a.charAt(1),a="#"+d+d+(e=a.charAt(2))+e+(f=a.charAt(3))+f+(5===a.length?a.charAt(4)+a.charAt(4):"")),9===a.length)return[(n=parseInt(a.substr(1,6),16))>>16,n>>8&255,255&n,parseInt(a.substr(7),16)/255];n=[(a=parseInt(a.substr(1),16))>>16,a>>8&255,255&a]}else if("hsl"===a.substr(0,3))if(n=m=a.match(N),b){if(~a.indexOf("="))return n=a.match(O),c&&n.length<4&&(n[3]=1),n}else g=n[0]%360/360,h=n[1]/100,e=(i=n[2]/100)<=.5?i*(h+1):i+h-i*h,d=2*i-e,n.length>3&&(n[3]*=1),n[0]=bk(g+1/3,d,e),n[1]=bk(g,d,e),n[2]=bk(g-1/3,d,e);else n=a.match(N)||bj.transparent;n=n.map(Number)}return b&&!m&&(d=n[0]/255,i=((j=Math.max(d,e=n[1]/255,f=n[2]/255))+(k=Math.min(d,e,f)))/2,j===k?g=h=0:(l=j-k,h=i>.5?l/(2-j-k):l/(j+k),g=(j===d?(e-f)/l+6*(e<f):j===e?(f-d)/l+2:(d-e)/l+4)*60),n[0]=~~(g+.5),n[1]=~~(100*h+.5),n[2]=~~(100*i+.5)),c&&n.length<4&&(n[3]=1),n},bm=function(a){var b=[],c=[],d=-1;return a.split(bo).forEach(function(a){var e=a.match(P)||[];b.push.apply(b,e),c.push(d+=e.length+1)}),b.c=c,b},bn=function(a,b,c){var d,e,f,g,h="",i=(a+h).match(bo),j=b?"hsla(":"rgba(",k=0;if(!i)return a;if(i=i.map(function(a){return(a=bl(a,b,1))&&j+(b?a[0]+","+a[1]+"%,"+a[2]+"%,"+a[3]:a.join(","))+")"}),c&&(f=bm(a),(d=c.c).join(h)!==f.c.join(h)))for(g=(e=a.replace(bo,"1").split(P)).length-1;k<g;k++)h+=e[k]+(~d.indexOf(k)?i.shift()||j+"0,0,0,0)":(f.length?f:i.length?i:c).shift());if(!e)for(g=(e=a.split(bo)).length-1;k<g;k++)h+=e[k]+i[k];return h+e[g]},bo=function(){var a,b="(?:\\b(?:(?:rgb|rgba|hsl|hsla)\\(.+?\\))|\\B#(?:[0-9a-f]{3,4}){1,2}\\b";for(a in bj)b+="|"+a+"\\b";return RegExp(b+")","gi")}(),bp=/hsl[a]?\(/,bq=function(a){var b,c=a.join(" ");if(bo.lastIndex=0,bo.test(c))return b=bp.test(c),a[1]=bn(a[1],b),a[0]=bn(a[0],b,bm(a[1])),!0},br=function(){var a,b,c,d,e,f,g=Date.now,h=500,i=33,j=g(),k=j,l=1e3/240,m=1e3/240,n=[],s=function c(o){var p,q,r,s,t=g()-k,u=!0===o;if((t>h||t<0)&&(j+=t-i),k+=t,((p=(r=k-j)-m)>0||u)&&(s=++d.frame,e=r-1e3*d.time,d.time=r/=1e3,m+=p+(p>=l?4:l-p),q=1),u||(a=b(c)),q)for(f=0;f<n.length;f++)n[f](r,e,s,o)};return d={time:0,frame:0,tick:function(){s(!0)},deltaRatio:function(a){return e/(1e3/(a||60))},wake:function(){r&&(!p&&J()&&(q=(o=p=window).document||{},U.gsap=cm,(o.gsapVersions||(o.gsapVersions=[])).push(cm.version),W(V||o.GreenSockGlobals||!o.gsap&&o||{}),bh.forEach(bi)),c="undefined"!=typeof requestAnimationFrame&&requestAnimationFrame,a&&d.sleep(),b=c||function(a){return setTimeout(a,m-1e3*d.time+1|0)},u=1,s(2))},sleep:function(){(c?cancelAnimationFrame:clearTimeout)(a),u=0,b=$},lagSmoothing:function(a,b){i=Math.min(b||33,h=a||1/0)},fps:function(a){l=1e3/(a||240),m=1e3*d.time+l},add:function(a,b,c){var e=b?function(b,c,f,g){a(b,c,f,g),d.remove(e)}:a;return d.remove(a),n[c?"unshift":"push"](e),bs(),e},remove:function(a,b){~(b=n.indexOf(a))&&n.splice(b,1)&&f>=b&&f--},_listeners:n}}(),bs=function(){return!u&&br.wake()},bt={},bu=/^[\d.\-M][\d.\-,\s]/,bv=/["']/g,bw=function(a){for(var b,c,d,e={},f=a.substr(1,a.length-3).split(":"),g=f[0],h=1,i=f.length;h<i;h++)c=f[h],b=h!==i-1?c.lastIndexOf(","):c.length,d=c.substr(0,b),e[g]=isNaN(d)?d.replace(bv,"").trim():+d,g=c.substr(b+1).trim();return e},bx=function(a){var b=a.indexOf("(")+1,c=a.indexOf(")"),d=a.indexOf("(",b);return a.substring(b,~d&&d<c?a.indexOf(")",c+1):c)},by=function(a){var b=(a+"").split("("),c=bt[b[0]];return c&&b.length>1&&c.config?c.config.apply(null,~a.indexOf("{")?[bw(b[1])]:bx(a).split(",").map(av)):bt._CE&&bu.test(a)?bt._CE("",a):c},bz=function(a){return function(b){return 1-a(1-b)}},bA=function a(b,c){for(var d,e=b._first;e;)e instanceof bI?a(e,c):!e.vars.yoyoEase||e._yoyo&&e._repeat||e._yoyo===c||(e.timeline?a(e.timeline,c):(d=e._ease,e._ease=e._yEase,e._yEase=d,e._yoyo=c)),e=e._next},bB=function(a,b){return a&&(E(a)?a:bt[a]||by(a))||b},bC=function(a,b,c,d){void 0===c&&(c=function(a){return 1-b(1-a)}),void 0===d&&(d=function(a){return a<.5?b(2*a)/2:1-b((1-a)*2)/2});var e,f={easeIn:b,easeOut:c,easeInOut:d};return an(a,function(a){for(var b in bt[a]=U[a]=f,bt[e=a.toLowerCase()]=c,f)bt[e+("easeIn"===b?".in":"easeOut"===b?".out":".inOut")]=bt[a+"."+b]=f[b]}),f},bD=function(a){return function(b){return b<.5?(1-a(1-2*b))/2:.5+a((b-.5)*2)/2}},bE=function a(b,c,d){var e=c>=1?c:1,f=(d||(b?.3:.45))/(c<1?c:1),g=f/x*(Math.asin(1/e)||0),h=function(a){return 1===a?1:e*Math.pow(2,-10*a)*C((a-g)*f)+1},i="out"===b?h:"in"===b?function(a){return 1-h(1-a)}:bD(h);return f=x/f,i.config=function(c,d){return a(b,c,d)},i},bF=function a(b,c){void 0===c&&(c=1.70158);var d=function(a){return a?--a*a*((c+1)*a+c)+1:0},e="out"===b?d:"in"===b?function(a){return 1-d(1-a)}:bD(d);return e.config=function(c){return a(b,c)},e};an("Linear,Quad,Cubic,Quart,Quint,Strong",function(a,b){var c=b<5?b+1:b;bC(a+",Power"+(c-1),b?function(a){return Math.pow(a,c)}:function(a){return a},function(a){return 1-Math.pow(1-a,c)},function(a){return a<.5?Math.pow(2*a,c)/2:1-Math.pow((1-a)*2,c)/2})}),bt.Linear.easeNone=bt.none=bt.Linear.easeIn,bC("Elastic",bE("in"),bE("out"),bE()),function(a,b){var c=1/2.75,d=1/2.75*2,e=1/2.75*2.5,f=function(f){return f<c?7.5625*f*f:f<d?7.5625*Math.pow(f-1.5/2.75,2)+.75:f<e?a*(f-=2.25/b)*f+.9375:a*Math.pow(f-2.625/b,2)+.984375};bC("Bounce",function(a){return 1-f(1-a)},f)}(7.5625,2.75),bC("Expo",function(a){return Math.pow(2,10*(a-1))*a+a*a*a*a*a*a*(1-a)}),bC("Circ",function(a){return-(A(1-a*a)-1)}),bC("Sine",function(a){return 1===a?1:-B(a*y)+1}),bC("Back",bF("in"),bF("out"),bF()),bt.SteppedEase=bt.steps=U.SteppedEase={config:function(a,b){void 0===a&&(a=1);var c=1/a,d=a+ +!b,e=+!!b,f=.99999999;return function(a){return((d*a0(0,f,a)|0)+e)*c}}},w.ease=bt["quad.out"],an("onComplete,onUpdate,onStart,onRepeat,onReverseComplete,onInterrupt",function(a){return aj+=a+","+a+"Params,"});var bG=function(a,b){this.id=z++,a._gsap=this,this.target=a,this.harness=b,this.get=b?b.get:am,this.set=b?b.getSetter:b_},bH=function(){function a(a){this.vars=a,this._delay=+a.delay||0,(this._repeat=a.repeat===1/0?-2:a.repeat||0)&&(this._rDelay=a.repeatDelay||0,this._yoyo=!!a.yoyo||!!a.yoyoEase),this._ts=1,aW(this,+a.duration,1,1),this.data=a.data,m&&(this._ctx=m,m.data.push(this)),u||br.wake()}var b=a.prototype;return b.delay=function(a){return a||0===a?(this.parent&&this.parent.smoothChildTiming&&this.startTime(this._start+a-this._delay),this._delay=a,this):this._delay},b.duration=function(a){return arguments.length?this.totalDuration(this._repeat>0?a+(a+this._rDelay)*this._repeat:a):this.totalDuration()&&this._dur},b.totalDuration=function(a){return arguments.length?(this._dirty=0,aW(this,this._repeat<0?a:(a-this._repeat*this._rDelay)/(this._repeat+1))):this._tDur},b.totalTime=function(a,b){if(bs(),!arguments.length)return this._tTime;var c=this._dp;if(c&&c.smoothChildTiming&&this._ts){for(aN(this,a),!c._dp||c.parent||aO(c,this);c&&c.parent;)c.parent._time!==c._start+(c._ts>=0?c._tTime/c._ts:-((c.totalDuration()-c._tTime)/c._ts))&&c.totalTime(c._tTime,!0),c=c.parent;!this.parent&&this._dp.autoRemoveChildren&&(this._ts>0&&a<this._tDur||this._ts<0&&a>0||!this._tDur&&!a)&&aP(this._dp,this,this._start-this._delay)}return this._tTime===a&&(this._dur||b)&&(!this._initted||1e-8!==Math.abs(this._zTime))&&(a||this._initted||!this.add&&!this._ptLookup)||(this._ts||(this._pTime=a),au(this,a,b)),this},b.time=function(a,b){return arguments.length?this.totalTime(Math.min(this.totalDuration(),a+aJ(this))%(this._dur+this._rDelay)||(a?this._dur:0),b):this._time},b.totalProgress=function(a,b){return arguments.length?this.totalTime(this.totalDuration()*a,b):this.totalDuration()?Math.min(1,this._tTime/this._tDur):this.rawTime()>=0&&this._initted?1:0},b.progress=function(a,b){return arguments.length?this.totalTime(this.duration()*(this._yoyo&&!(1&this.iteration())?1-a:a)+aJ(this),b):this.duration()?Math.min(1,this._time/this._dur):+(this.rawTime()>0)},b.iteration=function(a,b){var c=this.duration()+this._rDelay;return arguments.length?this.totalTime(this._time+(a-1)*c,b):this._repeat?aK(this._tTime,c)+1:1},b.timeScale=function(a,b){if(!arguments.length)return -1e-8===this._rts?0:this._rts;if(this._rts===a)return this;var c=this.parent&&this._ts?aL(this.parent._time,this):this._tTime;return this._rts=+a||0,this._ts=this._ps||-1e-8===a?0:this._rts,this.totalTime(a0(-Math.abs(this._delay),this.totalDuration(),c),!1!==b),aM(this),aH(this)},b.paused=function(a){return arguments.length?(this._ps!==a&&(this._ps=a,a?(this._pTime=this._tTime||Math.max(-this._delay,this.rawTime()),this._ts=this._act=0):(bs(),this._ts=this._rts,this.totalTime(this.parent&&!this.parent.smoothChildTiming?this.rawTime():this._tTime||this._pTime,1===this.progress()&&1e-8!==Math.abs(this._zTime)&&(this._tTime-=1e-8)))),this):this._ps},b.startTime=function(a){if(arguments.length){this._start=a;var b=this.parent||this._dp;return b&&(b._sort||!this.parent)&&aP(b,this,a-this._delay),this}return this._start},b.endTime=function(a){return this._start+(I(a)?this.totalDuration():this.duration())/Math.abs(this._ts||1)},b.rawTime=function(a){var b=this.parent||this._dp;return b?a&&(!this._ts||this._repeat&&this._time&&1>this.totalProgress())?this._tTime%(this._dur+this._rDelay):this._ts?aL(b.rawTime(a),this):this._tTime:this._tTime},b.revert=function(a){void 0===a&&(a=ab);var b=l;return l=a,at(this)&&(this.timeline&&this.timeline.revert(a),this.totalTime(-.01,a.suppressEvents)),"nested"!==this.data&&!1!==a.kill&&this.kill(),l=b,this},b.globalTime=function(a){for(var b=this,c=arguments.length?a:b.rawTime();b;)c=b._start+c/(Math.abs(b._ts)||1),b=b._dp;return!this.parent&&this._sat?this._sat.globalTime(a):c},b.repeat=function(a){return arguments.length?(this._repeat=a===1/0?-2:a,aX(this)):-2===this._repeat?1/0:this._repeat},b.repeatDelay=function(a){if(arguments.length){var b=this._time;return this._rDelay=a,aX(this),b?this.time(b):this}return this._rDelay},b.yoyo=function(a){return arguments.length?(this._yoyo=a,this):this._yoyo},b.seek=function(a,b){return this.totalTime(aZ(this,a),I(b))},b.restart=function(a,b){return this.play().totalTime(a?-this._delay:0,I(b)),this._dur||(this._zTime=-1e-8),this},b.play=function(a,b){return null!=a&&this.seek(a,b),this.reversed(!1).paused(!1)},b.reverse=function(a,b){return null!=a&&this.seek(a||this.totalDuration(),b),this.reversed(!0).paused(!1)},b.pause=function(a,b){return null!=a&&this.seek(a,b),this.paused(!0)},b.resume=function(){return this.paused(!1)},b.reversed=function(a){return arguments.length?(!!a!==this.reversed()&&this.timeScale(-this._rts||(a?-1e-8:0)),this):this._rts<0},b.invalidate=function(){return this._initted=this._act=0,this._zTime=-1e-8,this},b.isActive=function(){var a,b=this.parent||this._dp,c=this._start;return!!(!b||this._ts&&this._initted&&b.isActive()&&(a=b.rawTime(!0))>=c&&a<this.endTime(!0)-1e-8)},b.eventCallback=function(a,b,c){var d=this.vars;return arguments.length>1?(b?(d[a]=b,c&&(d[a+"Params"]=c),"onUpdate"===a&&(this._onUpdate=b)):delete d[a],this):d[a]},b.then=function(a){var b=this;return new Promise(function(c){var d=E(a)?a:aw,e=function(){var a=b.then;b.then=null,E(d)&&(d=d(b))&&(d.then||d===b)&&(b.then=a),c(d),b.then=a};b._initted&&1===b.totalProgress()&&b._ts>=0||!b._tTime&&b._ts<0?e():b._prom=e})},b.kill=function(){bg(this)},a}();ax(bH.prototype,{_time:0,_start:0,_end:0,_tTime:0,_tDur:0,_dirty:0,_repeat:0,_yoyo:!1,parent:null,_initted:!1,_rDelay:0,_ts:1,_dp:0,ratio:0,_zTime:-1e-8,_prom:0,_ps:!1,_rts:1});var bI=function(a){function b(b,c){var d;return void 0===b&&(b={}),(d=a.call(this,b)||this).labels={},d.smoothChildTiming=!!b.smoothChildTiming,d.autoRemoveChildren=!!b.autoRemoveChildren,d._sort=I(b.sortChildren),n&&aP(b.parent||n,i(d),c),b.reversed&&d.reverse(),b.paused&&d.paused(!0),b.scrollTrigger&&aQ(i(d),b.scrollTrigger),d}j(b,a);var c=b.prototype;return c.to=function(a,b,c){return a$(0,arguments,this),this},c.from=function(a,b,c){return a$(1,arguments,this),this},c.fromTo=function(a,b,c,d){return a$(2,arguments,this),this},c.set=function(a,b,c){return b.duration=0,b.parent=this,aB(b).repeatDelay||(b.repeat=0),b.immediateRender=!!b.immediateRender,new bW(a,b,aZ(this,c),1),this},c.call=function(a,b,c){return aP(this,bW.delayedCall(0,a,b),c)},c.staggerTo=function(a,b,c,d,e,f,g){return c.duration=b,c.stagger=c.stagger||d,c.onComplete=f,c.onCompleteParams=g,c.parent=this,new bW(a,c,aZ(this,e)),this},c.staggerFrom=function(a,b,c,d,e,f,g){return c.runBackwards=1,aB(c).immediateRender=I(c.immediateRender),this.staggerTo(a,b,c,d,e,f,g)},c.staggerFromTo=function(a,b,c,d,e,f,g,h){return d.startAt=c,aB(d).immediateRender=I(d.immediateRender),this.staggerTo(a,b,d,e,f,g,h)},c.render=function(a,b,c){var d,e,f,g,h,i,j,k,m,o,p,q,r=this._time,s=this._dirty?this.totalDuration():this._tDur,t=this._dur,u=a<=0?0:ap(a),v=this._zTime<0!=a<0&&(this._initted||!t);if(this!==n&&u>s&&a>=0&&(u=s),u!==this._tTime||c||v){if(r!==this._time&&t&&(u+=this._time-r,a+=this._time-r),d=u,m=this._start,i=!(k=this._ts),v&&(t||(r=this._zTime),(a||!b)&&(this._zTime=a)),this._repeat){if(p=this._yoyo,h=t+this._rDelay,this._repeat<-1&&a<0)return this.totalTime(100*h+a,b,c);if(d=ap(u%h),u===s?(g=this._repeat,d=t):((g=~~(o=ap(u/h)))&&g===o&&(d=t,g--),d>t&&(d=t)),o=aK(this._tTime,h),!r&&this._tTime&&o!==g&&this._tTime-o*h-this._dur<=0&&(o=g),p&&1&g&&(d=t-d,q=1),g!==o&&!this._lock){var w=p&&1&o,x=w===(p&&1&g);if(g<o&&(w=!w),r=w?0:u%t?t:u,this._lock=1,this.render(r||(q?0:ap(g*h)),b,!t)._lock=0,this._tTime=u,!b&&this.parent&&bf(this,"onRepeat"),this.vars.repeatRefresh&&!q&&(this.invalidate()._lock=1),r&&r!==this._time||!this._ts!==i||this.vars.onRepeat&&!this.parent&&!this._act||(t=this._dur,s=this._tDur,x&&(this._lock=2,r=w?t:-1e-4,this.render(r,!0),this.vars.repeatRefresh&&!q&&this.invalidate()),this._lock=0,!this._ts&&!i))return this;bA(this,q)}}if(this._hasPause&&!this._forcing&&this._lock<2&&(j=aV(this,ap(r),ap(d)))&&(u-=d-(d=j._start)),this._tTime=u,this._time=d,this._act=!k,this._initted||(this._onUpdate=this.vars.onUpdate,this._initted=1,this._zTime=a,r=0),!r&&u&&!b&&!o&&(bf(this,"onStart"),this._tTime!==u))return this;if(d>=r&&a>=0)for(e=this._first;e;){if(f=e._next,(e._act||d>=e._start)&&e._ts&&j!==e){if(e.parent!==this)return this.render(a,b,c);if(e.render(e._ts>0?(d-e._start)*e._ts:(e._dirty?e.totalDuration():e._tDur)+(d-e._start)*e._ts,b,c),d!==this._time||!this._ts&&!i){j=0,f&&(u+=this._zTime=-1e-8);break}}e=f}else{e=this._last;for(var y=a<0?a:d;e;){if(f=e._prev,(e._act||y<=e._end)&&e._ts&&j!==e){if(e.parent!==this)return this.render(a,b,c);if(e.render(e._ts>0?(y-e._start)*e._ts:(e._dirty?e.totalDuration():e._tDur)+(y-e._start)*e._ts,b,c||l&&at(e)),d!==this._time||!this._ts&&!i){j=0,f&&(u+=this._zTime=y?-1e-8:1e-8);break}}e=f}}if(j&&!b&&(this.pause(),j.render(d>=r?0:-1e-8)._zTime=d>=r?1:-1,this._ts))return this._start=m,aM(this),this.render(a,b,c);this._onUpdate&&!b&&bf(this,"onUpdate",!0),(u===s&&this._tTime>=this.totalDuration()||!u&&r)&&(m===this._start||Math.abs(k)!==Math.abs(this._ts))&&!this._lock&&((a||!t)&&(u===s&&this._ts>0||!u&&this._ts<0)&&aF(this,1),b||a<0&&!r||!u&&!r&&s||(bf(this,u===s&&a>=0?"onComplete":"onReverseComplete",!0),this._prom&&!(u<s&&this.timeScale()>0)&&this._prom()))}return this},c.add=function(a,b){var c=this;if(F(b)||(b=aZ(this,b,a)),!(a instanceof bH)){if(M(a))return a.forEach(function(a){return c.add(a,b)}),this;if(D(a))return this.addLabel(a,b);if(!E(a))return this;a=bW.delayedCall(0,a)}return this!==a?aP(this,a,b):this},c.getChildren=function(a,b,c,d){void 0===a&&(a=!0),void 0===b&&(b=!0),void 0===c&&(c=!0),void 0===d&&(d=-1e8);for(var e=[],f=this._first;f;)f._start>=d&&(f instanceof bW?b&&e.push(f):(c&&e.push(f),a&&e.push.apply(e,f.getChildren(!0,b,c)))),f=f._next;return e},c.getById=function(a){for(var b=this.getChildren(1,1,1),c=b.length;c--;)if(b[c].vars.id===a)return b[c]},c.remove=function(a){return D(a)?this.removeLabel(a):E(a)?this.killTweensOf(a):(a.parent===this&&aE(this,a),a===this._recent&&(this._recent=this._last),aG(this))},c.totalTime=function(b,c){return arguments.length?(this._forcing=1,!this._dp&&this._ts&&(this._start=ap(br.time-(this._ts>0?b/this._ts:-((this.totalDuration()-b)/this._ts)))),a.prototype.totalTime.call(this,b,c),this._forcing=0,this):this._tTime},c.addLabel=function(a,b){return this.labels[a]=aZ(this,b),this},c.removeLabel=function(a){return delete this.labels[a],this},c.addPause=function(a,b,c){var d=bW.delayedCall(0,b||$,c);return d.data="isPause",this._hasPause=1,aP(this,d,aZ(this,a))},c.removePause=function(a){var b=this._first;for(a=aZ(this,a);b;)b._start===a&&"isPause"===b.data&&aF(b),b=b._next},c.killTweensOf=function(a,b,c){for(var d=this.getTweensOf(a,c),e=d.length;e--;)bJ!==d[e]&&d[e].kill(a,b);return this},c.getTweensOf=function(a,b){for(var c,d=[],e=a4(a),f=this._first,g=F(b);f;)f instanceof bW?ar(f._targets,e)&&(g?(!bJ||f._initted&&f._ts)&&f.globalTime(0)<=b&&f.globalTime(f.totalDuration())>b:!b||f.isActive())&&d.push(f):(c=f.getTweensOf(e,b)).length&&d.push.apply(d,c),f=f._next;return d},c.tweenTo=function(a,b){b=b||{};var c,d=this,e=aZ(d,a),f=b,g=f.startAt,h=f.onStart,i=f.onStartParams,j=f.immediateRender,k=bW.to(d,ax({ease:b.ease||"none",lazy:!1,immediateRender:!1,time:e,overwrite:"auto",duration:b.duration||Math.abs((e-(g&&"time"in g?g.time:d._time))/d.timeScale())||1e-8,onStart:function(){if(d.pause(),!c){var a=b.duration||Math.abs((e-(g&&"time"in g?g.time:d._time))/d.timeScale());k._dur!==a&&aW(k,a,0,1).render(k._time,!0,!0),c=1}h&&h.apply(k,i||[])}},b));return j?k.render(0):k},c.tweenFromTo=function(a,b,c){return this.tweenTo(b,ax({startAt:{time:aZ(this,a)}},c))},c.recent=function(){return this._recent},c.nextLabel=function(a){return void 0===a&&(a=this._time),be(this,aZ(this,a))},c.previousLabel=function(a){return void 0===a&&(a=this._time),be(this,aZ(this,a),1)},c.currentLabel=function(a){return arguments.length?this.seek(a,!0):this.previousLabel(this._time+1e-8)},c.shiftChildren=function(a,b,c){void 0===c&&(c=0);for(var d,e=this._first,f=this.labels;e;)e._start>=c&&(e._start+=a,e._end+=a),e=e._next;if(b)for(d in f)f[d]>=c&&(f[d]+=a);return aG(this)},c.invalidate=function(b){var c=this._first;for(this._lock=0;c;)c.invalidate(b),c=c._next;return a.prototype.invalidate.call(this,b)},c.clear=function(a){void 0===a&&(a=!0);for(var b,c=this._first;c;)b=c._next,this.remove(c),c=b;return this._dp&&(this._time=this._tTime=this._pTime=0),a&&(this.labels={}),aG(this)},c.totalDuration=function(a){var b,c,d,e=0,f=this._last,g=1e8;if(arguments.length)return this.timeScale((this._repeat<0?this.duration():this.totalDuration())/(this.reversed()?-a:a));if(this._dirty){for(d=this.parent;f;)b=f._prev,f._dirty&&f.totalDuration(),(c=f._start)>g&&this._sort&&f._ts&&!this._lock?(this._lock=1,aP(this,f,c-f._delay,1)._lock=0):g=c,c<0&&f._ts&&(e-=c,(!d&&!this._dp||d&&d.smoothChildTiming)&&(this._start+=c/this._ts,this._time-=c,this._tTime-=c),this.shiftChildren(-c,!1,-Infinity),g=0),f._end>e&&f._ts&&(e=f._end),f=b;aW(this,this===n&&this._time>e?this._time:e,1,1),this._dirty=0}return this._tDur},b.updateRoot=function(a){if(n._ts&&(au(n,aL(a,n)),s=br.frame),br.frame>=ah){ah+=v.autoSleep||120;var b=n._first;if((!b||!b._ts)&&v.autoSleep&&br._listeners.length<2){for(;b&&!b._ts;)b=b._next;b||br.sleep()}}},b}(bH);ax(bI.prototype,{_lock:0,_hasPause:0,_forcing:0});var bJ,bK,bL=function(a,b,c,d,e,f,g){var h,i,j,k,l,m,n,o,p=new b8(this._pt,a,b,0,1,b2,null,e),q=0,r=0;for(p.b=c,p.e=d,c+="",d+="",(n=~d.indexOf("random("))&&(d=bc(d)),f&&(f(o=[c,d],a,b),c=o[0],d=o[1]),i=c.match(Q)||[];h=Q.exec(d);)k=h[0],l=d.substring(q,h.index),j?j=(j+1)%5:"rgba("===l.substr(-5)&&(j=1),k!==i[r++]&&(m=parseFloat(i[r-1])||0,p._pt={_next:p._pt,p:l||1===r?l:",",s:m,c:"="===k.charAt(1)?aq(m,k)-m:parseFloat(k)-m,m:j&&j<4?Math.round:0},q=Q.lastIndex);return p.c=q<d.length?d.substring(q,d.length):"",p.fp=g,(R.test(d)||n)&&(p.e=0),this._pt=p,p},bM=function(a,b,c,d,e,f,g,h,i,j){E(d)&&(d=d(e||0,a,f));var k,l=a[b],m="get"!==c?c:E(l)?i?a[b.indexOf("set")||!E(a["get"+b.substr(3)])?b:"get"+b.substr(3)](i):a[b]():l,n=E(l)?i?bZ:bY:bX;if(D(d)&&(~d.indexOf("random(")&&(d=bc(d)),"="===d.charAt(1)&&((k=aq(m,d)+(a1(m)||0))||0===k)&&(d=k)),!j||m!==d||bK)return isNaN(m*d)||""===d?(l||b in a||X(b,d),bL.call(this,a,b,m,d,n,h||v.stringFilter,i)):(k=new b8(this._pt,a,b,+m||0,d-(m||0),"boolean"==typeof l?b1:b0,0,n),i&&(k.fp=i),g&&k.modifier(g,this,a),this._pt=k)},bN=function(a,b,c,d,e){if(E(a)&&(a=bT(a,e,b,c,d)),!H(a)||a.style&&a.nodeType||M(a)||L(a))return D(a)?bT(a,e,b,c,d):a;var f,g={};for(f in a)g[f]=bT(a[f],e,b,c,d);return g},bO=function(a,b,c,d,e,f){var g,h,i,j;if(af[a]&&!1!==(g=new af[a]).init(e,g.rawVars?b[a]:bN(b[a],d,e,f,c),c,d,f)&&(c._pt=h=new b8(c._pt,e,a,0,1,g.render,g,0,g.priority),c!==t))for(i=c._ptLookup[c._targets.indexOf(e)],j=g._props.length;j--;)i[g._props[j]]=h;return g},bP=function a(b,c,d){var e,f,g,h,i,j,m,o,p,q,r,s,t,u=b.vars,v=u.ease,x=u.startAt,y=u.immediateRender,z=u.lazy,A=u.onUpdate,B=u.runBackwards,C=u.yoyoEase,D=u.keyframes,E=u.autoRevert,F=b._dur,G=b._startAt,H=b._targets,J=b.parent,K=J&&"nested"===J.data?J.vars.targets:H,L="auto"===b._overwrite&&!k,M=b.timeline;if(!M||D&&v||(v="none"),b._ease=bB(v,w.ease),b._yEase=C?bz(bB(!0===C?v:C,w.ease)):0,C&&b._yoyo&&!b._repeat&&(C=b._yEase,b._yEase=b._ease,b._ease=C),b._from=!M&&!!u.runBackwards,!M||D&&!u.stagger){if(s=(o=H[0]?al(H[0]).harness:0)&&u[o.prop],e=aA(u,ac),G&&(G._zTime<0&&G.progress(1),c<0&&B&&y&&!E?G.render(-1,!0):G.revert(B&&F?aa:_),G._lazy=0),x){if(aF(b._startAt=bW.set(H,ax({data:"isStart",overwrite:!1,parent:J,immediateRender:!0,lazy:!G&&I(z),startAt:null,delay:0,onUpdate:A&&function(){return bf(b,"onUpdate")},stagger:0},x))),b._startAt._dp=0,b._startAt._sat=b,c<0&&(l||!y&&!E)&&b._startAt.revert(aa),y&&F&&c<=0&&d<=0){c&&(b._zTime=c);return}}else if(B&&F&&!G)if(c&&(y=!1),g=ax({overwrite:!1,data:"isFromStart",lazy:y&&!G&&I(z),immediateRender:y,stagger:0,parent:J},e),s&&(g[o.prop]=s),aF(b._startAt=bW.set(H,g)),b._startAt._dp=0,b._startAt._sat=b,c<0&&(l?b._startAt.revert(aa):b._startAt.render(-1,!0)),b._zTime=c,y){if(!c)return}else a(b._startAt,1e-8,1e-8);for(f=0,b._pt=b._ptCache=0,z=F&&I(z)||z&&!F;f<H.length;f++){if(m=(i=H[f])._gsap||ak(H)[f]._gsap,b._ptLookup[f]=q={},ae[m.id]&&ad.length&&as(),r=K===H?f:K.indexOf(i),o&&!1!==(p=new o).init(i,s||e,b,r,K)&&(b._pt=h=new b8(b._pt,i,p.name,0,1,p.render,p,0,p.priority),p._props.forEach(function(a){q[a]=h}),p.priority&&(j=1)),!o||s)for(g in e)af[g]&&(p=bO(g,e,b,r,i,K))?p.priority&&(j=1):q[g]=h=bM.call(b,i,g,"get",e[g],r,K,0,u.stringFilter);b._op&&b._op[f]&&b.kill(i,b._op[f]),L&&b._pt&&(bJ=b,n.killTweensOf(i,q,b.globalTime(c)),t=!b.parent,bJ=0),b._pt&&z&&(ae[m.id]=1)}j&&b7(b),b._onInit&&b._onInit(b)}b._onUpdate=A,b._initted=(!b._op||b._pt)&&!t,D&&c<=0&&M.render(1e8,!0,!0)},bQ=function(a,b,c,d,e,f,g,h){var i,j,k,l,m=(a._pt&&a._ptCache||(a._ptCache={}))[b];if(!m)for(m=a._ptCache[b]=[],k=a._ptLookup,l=a._targets.length;l--;){if((i=k[l][b])&&i.d&&i.d._pt)for(i=i.d._pt;i&&i.p!==b&&i.fp!==b;)i=i._next;if(!i)return bK=1,a.vars[b]="+=0",bP(a,g),bK=0,h?Y(b+" not eligible for reset"):1;m.push(i)}for(l=m.length;l--;)(i=(j=m[l])._pt||j).s=(d||0===d)&&!e?d:i.s+(d||0)+f*i.c,i.c=c-i.s,j.e&&(j.e=ao(c)+a1(j.e)),j.b&&(j.b=i.s+a1(j.b))},bR=function(a,b){var c,d,e,f,g=a[0]?al(a[0]).harness:0,h=g&&g.aliases;if(!h)return b;for(d in c=ay({},b),h)if(d in c)for(e=(f=h[d].split(",")).length;e--;)c[f[e]]=c[d];return c},bS=function(a,b,c,d){var e,f,g=b.ease||d||"power1.inOut";if(M(b))f=c[a]||(c[a]=[]),b.forEach(function(a,c){return f.push({t:c/(b.length-1)*100,v:a,e:g})});else for(e in b)f=c[e]||(c[e]=[]),"ease"===e||f.push({t:parseFloat(a),v:b[e],e:g})},bT=function(a,b,c,d,e){return E(a)?a.call(b,c,d,e):D(a)&&~a.indexOf("random(")?bc(a):a},bU=aj+"repeat,repeatDelay,yoyo,repeatRefresh,yoyoEase,autoRevert",bV={};an(bU+",id,stagger,delay,duration,paused,scrollTrigger",function(a){return bV[a]=1});var bW=function(a){function b(b,c,d,e){"number"==typeof c&&(d.duration=c,c=d,d=null);var f,g,h,j,l,m,o,p,q=a.call(this,e?c:aB(c))||this,r=q.vars,s=r.duration,t=r.delay,u=r.immediateRender,w=r.stagger,x=r.overwrite,y=r.keyframes,z=r.defaults,A=r.scrollTrigger,B=r.yoyoEase,C=c.parent||n,D=(M(b)||L(b)?F(b[0]):"length"in c)?[b]:a4(b);if(q._targets=D.length?ak(D):Y("GSAP target "+b+" not found. https://gsap.com",!v.nullTargetWarn)||[],q._ptLookup=[],q._overwrite=x,y||w||K(s)||K(t)){if(c=q.vars,(f=q.timeline=new bI({data:"nested",defaults:z||{},targets:C&&"nested"===C.data?C.vars.targets:D})).kill(),f.parent=f._dp=i(q),f._start=0,w||K(s)||K(t)){if(j=D.length,o=w&&a7(w),H(w))for(l in w)~bU.indexOf(l)&&(p||(p={}),p[l]=w[l]);for(g=0;g<j;g++)(h=aA(c,bV)).stagger=0,B&&(h.yoyoEase=B),p&&ay(h,p),m=D[g],h.duration=+bT(s,i(q),g,m,D),h.delay=(+bT(t,i(q),g,m,D)||0)-q._delay,!w&&1===j&&h.delay&&(q._delay=t=h.delay,q._start+=t,h.delay=0),f.to(m,h,o?o(g,m,D):0),f._ease=bt.none;f.duration()?s=t=0:q.timeline=0}else if(y){aB(ax(f.vars.defaults,{ease:"none"})),f._ease=bB(y.ease||c.ease||"none");var E,G,J,N=0;if(M(y))y.forEach(function(a){return f.to(D,a,">")}),f.duration();else{for(l in h={},y)"ease"===l||"easeEach"===l||bS(l,y[l],h,y.easeEach);for(l in h)for(g=0,E=h[l].sort(function(a,b){return a.t-b.t}),N=0;g<E.length;g++)(J={ease:(G=E[g]).e,duration:(G.t-(g?E[g-1].t:0))/100*s})[l]=G.v,f.to(D,J,N),N+=J.duration;f.duration()<s&&f.to({},{duration:s-f.duration()})}}s||q.duration(s=f.duration())}else q.timeline=0;return!0!==x||k||(bJ=i(q),n.killTweensOf(D),bJ=0),aP(C,i(q),d),c.reversed&&q.reverse(),c.paused&&q.paused(!0),(u||!s&&!y&&q._start===ap(C._time)&&I(u)&&function a(b){return!b||b._ts&&a(b.parent)}(i(q))&&"nested"!==C.data)&&(q._tTime=-1e-8,q.render(Math.max(0,-t)||0)),A&&aQ(i(q),A),q}j(b,a);var c=b.prototype;return c.render=function(a,b,c){var d,e,f,g,h,i,j,k,l,m=this._time,n=this._tDur,o=this._dur,p=a<0,q=a>n-1e-8&&!p?n:a<1e-8?0:a;if(o){if(q!==this._tTime||!a||c||!this._initted&&this._tTime||this._startAt&&this._zTime<0!==p||this._lazy){if(d=q,k=this.timeline,this._repeat){if(g=o+this._rDelay,this._repeat<-1&&p)return this.totalTime(100*g+a,b,c);if(d=ap(q%g),q===n?(f=this._repeat,d=o):(f=~~(h=ap(q/g)))&&f===h?(d=o,f--):d>o&&(d=o),(i=this._yoyo&&1&f)&&(l=this._yEase,d=o-d),h=aK(this._tTime,g),d===m&&!c&&this._initted&&f===h)return this._tTime=q,this;f!==h&&(k&&this._yEase&&bA(k,i),this.vars.repeatRefresh&&!i&&!this._lock&&d!==g&&this._initted&&(this._lock=c=1,this.render(ap(g*f),!0).invalidate()._lock=0))}if(!this._initted){if(aR(this,p?a:d,c,b,q))return this._tTime=0,this;if(m!==this._time&&!(c&&this.vars.repeatRefresh&&f!==h))return this;if(o!==this._dur)return this.render(a,b,c)}if(this._tTime=q,this._time=d,!this._act&&this._ts&&(this._act=1,this._lazy=0),this.ratio=j=(l||this._ease)(d/o),this._from&&(this.ratio=j=1-j),!m&&q&&!b&&!h&&(bf(this,"onStart"),this._tTime!==q))return this;for(e=this._pt;e;)e.r(j,e.d),e=e._next;k&&k.render(a<0?a:k._dur*k._ease(d/this._dur),b,c)||this._startAt&&(this._zTime=a),this._onUpdate&&!b&&(p&&aI(this,a,b,c),bf(this,"onUpdate")),this._repeat&&f!==h&&this.vars.onRepeat&&!b&&this.parent&&bf(this,"onRepeat"),(q===this._tDur||!q)&&this._tTime===q&&(p&&!this._onUpdate&&aI(this,a,!0,!0),(a||!o)&&(q===this._tDur&&this._ts>0||!q&&this._ts<0)&&aF(this,1),!b&&!(p&&!m)&&(q||m||i)&&(bf(this,q===n?"onComplete":"onReverseComplete",!0),this._prom&&!(q<n&&this.timeScale()>0)&&this._prom()))}}else aU(this,a,b,c);return this},c.targets=function(){return this._targets},c.invalidate=function(b){return b&&this.vars.runBackwards||(this._startAt=0),this._pt=this._op=this._onUpdate=this._lazy=this.ratio=0,this._ptLookup=[],this.timeline&&this.timeline.invalidate(b),a.prototype.invalidate.call(this,b)},c.resetTo=function(a,b,c,d,e){u||br.wake(),this._ts||this.play();var f=Math.min(this._dur,(this._dp._time-this._start)*this._ts);return(this._initted||bP(this,f),bQ(this,a,b,c,d,this._ease(f/this._dur),f,e))?this.resetTo(a,b,c,d,1):(aN(this,0),this.parent||aD(this._dp,this,"_first","_last",this._dp._sort?"_start":0),this.render(0))},c.kill=function(a,b){if(void 0===b&&(b="all"),!a&&(!b||"all"===b))return this._lazy=this._pt=0,this.parent?bg(this):this.scrollTrigger&&this.scrollTrigger.kill(!!l),this;if(this.timeline){var c=this.timeline.totalDuration();return this.timeline.killTweensOf(a,b,bJ&&!0!==bJ.vars.overwrite)._first||bg(this),this.parent&&c!==this.timeline.totalDuration()&&aW(this,this._dur*this.timeline._tDur/c,0,1),this}var d,e,f,g,h,i,j,k=this._targets,m=a?a4(a):k,n=this._ptLookup,o=this._pt;if((!b||"all"===b)&&aC(k,m))return"all"===b&&(this._pt=0),bg(this);for(d=this._op=this._op||[],"all"!==b&&(D(b)&&(h={},an(b,function(a){return h[a]=1}),b=h),b=bR(k,b)),j=k.length;j--;)if(~m.indexOf(k[j]))for(h in e=n[j],"all"===b?(d[j]=b,g=e,f={}):(f=d[j]=d[j]||{},g=b),g)(i=e&&e[h])&&("kill"in i.d&&!0!==i.d.kill(h)||aE(this,i,"_pt"),delete e[h]),"all"!==f&&(f[h]=1);return this._initted&&!this._pt&&o&&bg(this),this},b.to=function(a,c){return new b(a,c,arguments[2])},b.from=function(a,b){return a$(1,arguments)},b.delayedCall=function(a,c,d,e){return new b(c,0,{immediateRender:!1,lazy:!1,overwrite:!1,delay:a,onComplete:c,onReverseComplete:c,onCompleteParams:d,onReverseCompleteParams:d,callbackScope:e})},b.fromTo=function(a,b,c){return a$(2,arguments)},b.set=function(a,c){return c.duration=0,c.repeatDelay||(c.repeat=0),new b(a,c)},b.killTweensOf=function(a,b,c){return n.killTweensOf(a,b,c)},b}(bH);ax(bW.prototype,{_targets:[],_lazy:0,_startAt:0,_op:0,_onInit:0}),an("staggerTo,staggerFrom,staggerFromTo",function(a){bW[a]=function(){var b=new bI,c=a2.call(arguments,0);return c.splice("staggerFromTo"===a?5:4,0,0),b[a].apply(b,c)}});var bX=function(a,b,c){return a[b]=c},bY=function(a,b,c){return a[b](c)},bZ=function(a,b,c,d){return a[b](d.fp,c)},b$=function(a,b,c){return a.setAttribute(b,c)},b_=function(a,b){return E(a[b])?bY:G(a[b])&&a.setAttribute?b$:bX},b0=function(a,b){return b.set(b.t,b.p,Math.round((b.s+b.c*a)*1e6)/1e6,b)},b1=function(a,b){return b.set(b.t,b.p,!!(b.s+b.c*a),b)},b2=function(a,b){var c=b._pt,d="";if(!a&&b.b)d=b.b;else if(1===a&&b.e)d=b.e;else{for(;c;)d=c.p+(c.m?c.m(c.s+c.c*a):Math.round((c.s+c.c*a)*1e4)/1e4)+d,c=c._next;d+=b.c}b.set(b.t,b.p,d,b)},b3=function(a,b){for(var c=b._pt;c;)c.r(a,c.d),c=c._next},b4=function(a,b,c,d){for(var e,f=this._pt;f;)e=f._next,f.p===d&&f.modifier(a,b,c),f=e},b5=function(a){for(var b,c,d=this._pt;d;)c=d._next,(d.p!==a||d.op)&&d.op!==a?d.dep||(b=1):aE(this,d,"_pt"),d=c;return!b},b6=function(a,b,c,d){d.mSet(a,b,d.m.call(d.tween,c,d.mt),d)},b7=function(a){for(var b,c,d,e,f=a._pt;f;){for(b=f._next,c=d;c&&c.pr>f.pr;)c=c._next;(f._prev=c?c._prev:e)?f._prev._next=f:d=f,(f._next=c)?c._prev=f:e=f,f=b}a._pt=d},b8=function(){function a(a,b,c,d,e,f,g,h,i){this.t=b,this.s=d,this.c=e,this.p=c,this.r=f||b0,this.d=g||this,this.set=h||bX,this.pr=i||0,this._next=a,a&&(a._prev=this)}return a.prototype.modifier=function(a,b,c){this.mSet=this.mSet||this.set,this.set=b6,this.m=a,this.mt=c,this.tween=b},a}();an(aj+"parent,duration,ease,delay,overwrite,runBackwards,startAt,yoyo,immediateRender,repeat,repeatDelay,data,paused,reversed,lazy,callbackScope,stringFilter,id,yoyoEase,stagger,inherit,repeatRefresh,keyframes,autoRevert,scrollTrigger",function(a){return ac[a]=1}),U.TweenMax=U.TweenLite=bW,U.TimelineLite=U.TimelineMax=bI,n=new bI({sortChildren:!1,defaults:w,autoRemoveChildren:!0,id:"root",smoothChildTiming:!0}),v.stringFilter=bq;var b9=[],ca={},cb=[],cc=0,cd=0,ce=function(a){return(ca[a]||cb).map(function(a){return a()})},cf=function(){var a=Date.now(),b=[];a-cc>2&&(ce("matchMediaInit"),b9.forEach(function(a){var c,d,e,f,g=a.queries,h=a.conditions;for(d in g)(c=o.matchMedia(g[d]).matches)&&(e=1),c!==h[d]&&(h[d]=c,f=1);f&&(a.revert(),e&&b.push(a))}),ce("matchMediaRevert"),b.forEach(function(a){return a.onMatch(a,function(b){return a.add(null,b)})}),cc=a,ce("matchMedia"))},cg=function(){function a(a,b){this.selector=b&&a5(b),this.data=[],this._r=[],this.isReverted=!1,this.id=cd++,a&&this.add(a)}var b=a.prototype;return b.add=function(a,b,c){E(a)&&(c=b,b=a,a=E);var d=this,e=function(){var a,e=m,f=d.selector;return e&&e!==d&&e.data.push(d),c&&(d.selector=a5(c)),m=d,a=b.apply(d,arguments),E(a)&&d._r.push(a),m=e,d.selector=f,d.isReverted=!1,a};return d.last=e,a===E?e(d,function(a){return d.add(null,a)}):a?d[a]=e:e},b.ignore=function(a){var b=m;m=null,a(this),m=b},b.getTweens=function(){var b=[];return this.data.forEach(function(c){return c instanceof a?b.push.apply(b,c.getTweens()):c instanceof bW&&!(c.parent&&"nested"===c.parent.data)&&b.push(c)}),b},b.clear=function(){this._r.length=this.data.length=0},b.kill=function(a,b){var c=this;if(a){for(var d,e=c.getTweens(),f=c.data.length;f--;)"isFlip"===(d=c.data[f]).data&&(d.revert(),d.getChildren(!0,!0,!1).forEach(function(a){return e.splice(e.indexOf(a),1)}));for(e.map(function(a){return{g:a._dur||a._delay||a._sat&&!a._sat.vars.immediateRender?a.globalTime(0):-1/0,t:a}}).sort(function(a,b){return b.g-a.g||-1/0}).forEach(function(b){return b.t.revert(a)}),f=c.data.length;f--;)(d=c.data[f])instanceof bI?"nested"!==d.data&&(d.scrollTrigger&&d.scrollTrigger.revert(),d.kill()):d instanceof bW||!d.revert||d.revert(a);c._r.forEach(function(b){return b(a,c)}),c.isReverted=!0}else this.data.forEach(function(a){return a.kill&&a.kill()});if(this.clear(),b)for(var g=b9.length;g--;)b9[g].id===this.id&&b9.splice(g,1)},b.revert=function(a){this.kill(a||{})},a}(),ch=function(){function a(a){this.contexts=[],this.scope=a,m&&m.data.push(this)}var b=a.prototype;return b.add=function(a,b,c){H(a)||(a={matches:a});var d,e,f,g=new cg(0,c||this.scope),h=g.conditions={};for(e in m&&!g.selector&&(g.selector=m.selector),this.contexts.push(g),b=g.add("onMatch",b),g.queries=a,a)"all"===e?f=1:(d=o.matchMedia(a[e]))&&(0>b9.indexOf(g)&&b9.push(g),(h[e]=d.matches)&&(f=1),d.addListener?d.addListener(cf):d.addEventListener("change",cf));return f&&b(g,function(a){return g.add(null,a)}),this},b.revert=function(a){this.kill(a||{})},b.kill=function(a){this.contexts.forEach(function(b){return b.kill(a,!0)})},a}(),ci={registerPlugin:function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];b.forEach(function(a){return bi(a)})},timeline:function(a){return new bI(a)},getTweensOf:function(a,b){return n.getTweensOf(a,b)},getProperty:function(a,b,c,d){D(a)&&(a=a4(a)[0]);var e=al(a||{}).get,f=c?aw:av;return"native"===c&&(c=""),a?b?f((af[b]&&af[b].get||e)(a,b,c,d)):function(b,c,d){return f((af[b]&&af[b].get||e)(a,b,c,d))}:a},quickSetter:function(a,b,c){if((a=a4(a)).length>1){var d=a.map(function(a){return cm.quickSetter(a,b,c)}),e=d.length;return function(a){for(var b=e;b--;)d[b](a)}}a=a[0]||{};var f=af[b],g=al(a),h=g.harness&&(g.harness.aliases||{})[b]||b,i=f?function(b){var d=new f;t._pt=0,d.init(a,c?b+c:b,t,0,[a]),d.render(1,d),t._pt&&b3(1,t)}:g.set(a,h);return f?i:function(b){return i(a,h,c?b+c:b,g,1)}},quickTo:function(a,b,c){var d,e=cm.to(a,ax(((d={})[b]="+=0.1",d.paused=!0,d.stagger=0,d),c||{})),f=function(a,c,d){return e.resetTo(b,a,c,d)};return f.tween=e,f},isTweening:function(a){return n.getTweensOf(a,!0).length>0},defaults:function(a){return a&&a.ease&&(a.ease=bB(a.ease,w.ease)),az(w,a||{})},config:function(a){return az(v,a||{})},registerEffect:function(a){var b=a.name,c=a.effect,d=a.plugins,e=a.defaults,f=a.extendTimeline;(d||"").split(",").forEach(function(a){return a&&!af[a]&&!U[a]&&Y(b+" effect requires "+a+" plugin.")}),ag[b]=function(a,b,d){return c(a4(a),ax(b||{},e),d)},f&&(bI.prototype[b]=function(a,c,d){return this.add(ag[b](a,H(c)?c:(d=c)&&{},this),d)})},registerEase:function(a,b){bt[a]=bB(b)},parseEase:function(a,b){return arguments.length?bB(a,b):bt},getById:function(a){return n.getById(a)},exportRoot:function(a,b){void 0===a&&(a={});var c,d,e=new bI(a);for(e.smoothChildTiming=I(a.smoothChildTiming),n.remove(e),e._dp=0,e._time=e._tTime=n._time,c=n._first;c;)d=c._next,(b||!(!c._dur&&c instanceof bW&&c.vars.onComplete===c._targets[0]))&&aP(e,c,c._start-c._delay),c=d;return aP(n,e,0),e},context:function(a,b){return a?new cg(a,b):m},matchMedia:function(a){return new ch(a)},matchMediaRefresh:function(){return b9.forEach(function(a){var b,c,d=a.conditions;for(c in d)d[c]&&(d[c]=!1,b=1);b&&a.revert()})||cf()},addEventListener:function(a,b){var c=ca[a]||(ca[a]=[]);~c.indexOf(b)||c.push(b)},removeEventListener:function(a,b){var c=ca[a],d=c&&c.indexOf(b);d>=0&&c.splice(d,1)},utils:{wrap:function a(b,c,d){var e=c-b;return M(b)?bb(b,a(0,b.length),c):a_(d,function(a){return(e+(a-b)%e)%e+b})},wrapYoyo:function a(b,c,d){var e=c-b,f=2*e;return M(b)?bb(b,a(0,b.length-1),c):a_(d,function(a){return a=(f+(a-b)%f)%f||0,b+(a>e?f-a:a)})},distribute:a7,random:ba,snap:a9,normalize:function(a,b,c){return bd(a,b,0,1,c)},getUnit:a1,clamp:function(a,b,c){return a_(c,function(c){return a0(a,b,c)})},splitColor:bl,toArray:a4,selector:a5,mapRange:bd,pipe:function(){for(var a=arguments.length,b=Array(a),c=0;c<a;c++)b[c]=arguments[c];return function(a){return b.reduce(function(a,b){return b(a)},a)}},unitize:function(a,b){return function(c){return a(parseFloat(c))+(b||a1(c))}},interpolate:function a(b,c,d,e){var f=isNaN(b+c)?0:function(a){return(1-a)*b+a*c};if(!f){var g,h,i,j,k,l=D(b),m={};if(!0===d&&(e=1)&&(d=null),l)b={p:b},c={p:c};else if(M(b)&&!M(c)){for(h=1,i=[],k=(j=b.length)-2;h<j;h++)i.push(a(b[h-1],b[h]));j--,f=function(a){var b=Math.min(k,~~(a*=j));return i[b](a-b)},d=c}else e||(b=ay(M(b)?[]:{},b));if(!i){for(g in c)bM.call(m,b,g,"get",c[g]);f=function(a){return b3(a,m)||(l?b.p:b)}}}return a_(d,f)},shuffle:a6},install:W,effects:ag,ticker:br,updateRoot:bI.updateRoot,plugins:af,globalTimeline:n,core:{PropTween:b8,globals:Z,Tween:bW,Timeline:bI,Animation:bH,getCache:al,_removeLinkedListItem:aE,reverting:function(){return l},context:function(a){return a&&m&&(m.data.push(a),a._ctx=m),m},suppressOverwrites:function(a){return k=a}}};an("to,from,fromTo,delayedCall,set,killTweensOf",function(a){return ci[a]=bW[a]}),br.add(bI.updateRoot),t=ci.to({},{duration:0});var cj=function(a,b){for(var c=a._pt;c&&c.p!==b&&c.op!==b&&c.fp!==b;)c=c._next;return c},ck=function(a,b){var c,d,e,f=a._targets;for(c in b)for(d=f.length;d--;)(e=a._ptLookup[d][c])&&(e=e.d)&&(e._pt&&(e=cj(e,c)),e&&e.modifier&&e.modifier(b[c],a,f[d],c))},cl=function(a,b){return{name:a,headless:1,rawVars:1,init:function(a,c,d){d._onInit=function(a){var d,e;if(D(c)&&(d={},an(c,function(a){return d[a]=1}),c=d),b){for(e in d={},c)d[e]=b(c[e]);c=d}ck(a,c)}}}},cm=ci.registerPlugin({name:"attr",init:function(a,b,c,d,e){var f,g,h;for(f in this.tween=c,b)h=a.getAttribute(f)||"",(g=this.add(a,"setAttribute",(h||0)+"",b[f],d,e,0,0,f)).op=f,g.b=h,this._props.push(f)},render:function(a,b){for(var c=b._pt;c;)l?c.set(c.t,c.p,c.b,c):c.r(a,c.d),c=c._next}},{name:"endArray",headless:1,init:function(a,b){for(var c=b.length;c--;)this.add(a,c,a[c]||0,b[c],0,0,0,0,0,1)}},cl("roundProps",a8),cl("modifiers"),cl("snap",a9))||ci;bW.version=bI.version=cm.version="3.13.0",r=1,J()&&bs(),bt.Power0,bt.Power1,bt.Power2,bt.Power3,bt.Power4,bt.Linear,bt.Quad,bt.Cubic,bt.Quart,bt.Quint,bt.Strong,bt.Elastic,bt.Back,bt.SteppedEase,bt.Bounce,bt.Sine,bt.Expo,bt.Circ;var cn,co,cp,cq,cr,cs,ct,cu={},cv=180/Math.PI,cw=Math.PI/180,cx=Math.atan2,cy=/([A-Z])/g,cz=/(left|right|width|margin|padding|x)/i,cA=/[\s,\(]\S/,cB={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},cC=function(a,b){return b.set(b.t,b.p,Math.round((b.s+b.c*a)*1e4)/1e4+b.u,b)},cD=function(a,b){return b.set(b.t,b.p,1===a?b.e:Math.round((b.s+b.c*a)*1e4)/1e4+b.u,b)},cE=function(a,b){return b.set(b.t,b.p,a?Math.round((b.s+b.c*a)*1e4)/1e4+b.u:b.b,b)},cF=function(a,b){var c=b.s+b.c*a;b.set(b.t,b.p,~~(c+(c<0?-.5:.5))+b.u,b)},cG=function(a,b){return b.set(b.t,b.p,a?b.e:b.b,b)},cH=function(a,b){return b.set(b.t,b.p,1!==a?b.b:b.e,b)},cI=function(a,b,c){return a.style[b]=c},cJ=function(a,b,c){return a.style.setProperty(b,c)},cK=function(a,b,c){return a._gsap[b]=c},cL=function(a,b,c){return a._gsap.scaleX=a._gsap.scaleY=c},cM=function(a,b,c,d,e){var f=a._gsap;f.scaleX=f.scaleY=c,f.renderTransform(e,f)},cN=function(a,b,c,d,e){var f=a._gsap;f[b]=c,f.renderTransform(e,f)},cO="transform",cP=cO+"Origin",cQ=function a(b,c){var d=this,e=this.target,f=e.style,g=e._gsap;if(b in cu&&f){if(this.tfm=this.tfm||{},"transform"===b)return cB.transform.split(",").forEach(function(b){return a.call(d,b,c)});if(~(b=cB[b]||b).indexOf(",")?b.split(",").forEach(function(a){return d.tfm[a]=c6(e,a)}):this.tfm[b]=g.x?g[b]:c6(e,b),b===cP&&(this.tfm.zOrigin=g.zOrigin),this.props.indexOf(cO)>=0)return;g.svg&&(this.svgo=e.getAttribute("data-svg-origin"),this.props.push(cP,c,"")),b=cO}(f||c)&&this.props.push(b,c,f[b])},cR=function(a){a.translate&&(a.removeProperty("translate"),a.removeProperty("scale"),a.removeProperty("rotate"))},cS=function(){var a,b,c=this.props,d=this.target,e=d.style,f=d._gsap;for(a=0;a<c.length;a+=3)c[a+1]?2===c[a+1]?d[c[a]](c[a+2]):d[c[a]]=c[a+2]:c[a+2]?e[c[a]]=c[a+2]:e.removeProperty("--"===c[a].substr(0,2)?c[a]:c[a].replace(cy,"-$1").toLowerCase());if(this.tfm){for(b in this.tfm)f[b]=this.tfm[b];f.svg&&(f.renderTransform(),d.setAttribute("data-svg-origin",this.svgo||"")),(a=cs())&&a.isStart||e[cO]||(cR(e),f.zOrigin&&e[cP]&&(e[cP]+=" "+f.zOrigin+"px",f.zOrigin=0,f.renderTransform()),f.uncache=1)}},cT=function(a,b){var c={target:a,props:[],revert:cS,save:cQ};return a._gsap||cm.core.getCache(a),b&&a.style&&a.nodeType&&b.split(",").forEach(function(a){return c.save(a)}),c},cU=function(a,b){var c=cn.createElementNS?cn.createElementNS((b||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),a):cn.createElement(a);return c&&c.style?c:cn.createElement(a)},cV=function a(b,c,d){var e=getComputedStyle(b);return e[c]||e.getPropertyValue(c.replace(cy,"-$1").toLowerCase())||e.getPropertyValue(c)||!d&&a(b,cX(c)||c,1)||""},cW="O,Moz,ms,Ms,Webkit".split(","),cX=function(a,b,c){var d=(b||cq).style,e=5;if(a in d&&!c)return a;for(a=a.charAt(0).toUpperCase()+a.substr(1);e--&&!(cW[e]+a in d););return e<0?null:(3===e?"ms":e>=0?cW[e]:"")+a},cY=function(){"undefined"!=typeof window&&window.document&&(co=(cn=window.document).documentElement,cq=cU("div")||{style:{}},cU("div"),cP=(cO=cX(cO))+"Origin",cq.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",ct=!!cX("perspective"),cs=cm.core.reverting,cp=1)},cZ=function(a){var b,c=a.ownerSVGElement,d=cU("svg",c&&c.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),e=a.cloneNode(!0);e.style.display="block",d.appendChild(e),co.appendChild(d);try{b=e.getBBox()}catch(a){}return d.removeChild(e),co.removeChild(d),b},c$=function(a,b){for(var c=b.length;c--;)if(a.hasAttribute(b[c]))return a.getAttribute(b[c])},c_=function(a){var b,c;try{b=a.getBBox()}catch(d){b=cZ(a),c=1}return b&&(b.width||b.height)||c||(b=cZ(a)),!b||b.width||b.x||b.y?b:{x:+c$(a,["x","cx","x1"])||0,y:+c$(a,["y","cy","y1"])||0,width:0,height:0}},c0=function(a){return!!(a.getCTM&&(!a.parentNode||a.ownerSVGElement)&&c_(a))},c1=function(a,b){if(b){var c,d=a.style;b in cu&&b!==cP&&(b=cO),d.removeProperty?(("ms"===(c=b.substr(0,2))||"webkit"===b.substr(0,6))&&(b="-"+b),d.removeProperty("--"===c?b:b.replace(cy,"-$1").toLowerCase())):d.removeAttribute(b)}},c2=function(a,b,c,d,e,f){var g=new b8(a._pt,b,c,0,1,f?cH:cG);return a._pt=g,g.b=d,g.e=e,a._props.push(c),g},c3={deg:1,rad:1,turn:1},c4={grid:1,flex:1},c5=function a(b,c,d,e){var f,g,h,i,j=parseFloat(d)||0,k=(d+"").trim().substr((j+"").length)||"px",l=cq.style,m=cz.test(c),n="svg"===b.tagName.toLowerCase(),o=(n?"client":"offset")+(m?"Width":"Height"),p="px"===e,q="%"===e;if(e===k||!j||c3[e]||c3[k])return j;if("px"===k||p||(j=a(b,c,d,"px")),i=b.getCTM&&c0(b),(q||"%"===k)&&(cu[c]||~c.indexOf("adius")))return f=i?b.getBBox()[m?"width":"height"]:b[o],ao(q?j/f*100:j/100*f);if(l[m?"width":"height"]=100+(p?k:e),g="rem"!==e&&~c.indexOf("adius")||"em"===e&&b.appendChild&&!n?b:b.parentNode,i&&(g=(b.ownerSVGElement||{}).parentNode),g&&g!==cn&&g.appendChild||(g=cn.body),(h=g._gsap)&&q&&h.width&&m&&h.time===br.time&&!h.uncache)return ao(j/h.width*100);if(q&&("height"===c||"width"===c)){var r=b.style[c];b.style[c]=100+e,f=b[o],r?b.style[c]=r:c1(b,c)}else(q||"%"===k)&&!c4[cV(g,"display")]&&(l.position=cV(b,"position")),g===b&&(l.position="static"),g.appendChild(cq),f=cq[o],g.removeChild(cq),l.position="absolute";return m&&q&&((h=al(g)).time=br.time,h.width=g[o]),ao(p?f*j/100:f&&j?100/f*j:0)},c6=function(a,b,c,d){var e;return cp||cY(),b in cB&&"transform"!==b&&~(b=cB[b]).indexOf(",")&&(b=b.split(",")[0]),cu[b]&&"transform"!==b?(e=di(a,d),e="transformOrigin"!==b?e[b]:e.svg?e.origin:dj(cV(a,cP))+" "+e.zOrigin+"px"):(!(e=a.style[b])||"auto"===e||d||~(e+"").indexOf("calc("))&&(e=db[b]&&db[b](a,b,c)||cV(a,b)||am(a,b)||+("opacity"===b)),c&&!~(e+"").trim().indexOf(" ")?c5(a,b,e,c)+c:e},c7=function(a,b,c,d){if(!c||"none"===c){var e=cX(b,a,1),f=e&&cV(a,e,1);f&&f!==c?(b=e,c=f):"borderColor"===b&&(c=cV(a,"borderTopColor"))}var g,h,i,j,k,l,m,n,o,p,q,r=new b8(this._pt,a.style,b,0,1,b2),s=0,t=0;if(r.b=c,r.e=d,c+="","var(--"===(d+="").substring(0,6)&&(d=cV(a,d.substring(4,d.indexOf(")")))),"auto"===d&&(l=a.style[b],a.style[b]=d,d=cV(a,b)||d,l?a.style[b]=l:c1(a,b)),bq(g=[c,d]),c=g[0],d=g[1],i=c.match(P)||[],(d.match(P)||[]).length){for(;h=P.exec(d);)m=h[0],o=d.substring(s,h.index),k?k=(k+1)%5:("rgba("===o.substr(-5)||"hsla("===o.substr(-5))&&(k=1),m!==(l=i[t++]||"")&&(j=parseFloat(l)||0,q=l.substr((j+"").length),"="===m.charAt(1)&&(m=aq(j,m)+q),n=parseFloat(m),p=m.substr((n+"").length),s=P.lastIndex-p.length,p||(p=p||v.units[b]||q,s===d.length&&(d+=p,r.e+=p)),q!==p&&(j=c5(a,b,l,p)||0),r._pt={_next:r._pt,p:o||1===t?o:",",s:j,c:n-j,m:k&&k<4||"zIndex"===b?Math.round:0});r.c=s<d.length?d.substring(s,d.length):""}else r.r="display"===b&&"none"===d?cH:cG;return R.test(d)&&(r.e=0),this._pt=r,r},c8={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},c9=function(a){var b=a.split(" "),c=b[0],d=b[1]||"50%";return("top"===c||"bottom"===c||"left"===d||"right"===d)&&(a=c,c=d,d=a),b[0]=c8[c]||c,b[1]=c8[d]||d,b.join(" ")},da=function(a,b){if(b.tween&&b.tween._time===b.tween._dur){var c,d,e,f=b.t,g=f.style,h=b.u,i=f._gsap;if("all"===h||!0===h)g.cssText="",d=1;else for(e=(h=h.split(",")).length;--e>-1;)cu[c=h[e]]&&(d=1,c="transformOrigin"===c?cP:cO),c1(f,c);d&&(c1(f,cO),i&&(i.svg&&f.removeAttribute("transform"),g.scale=g.rotate=g.translate="none",di(f,1),i.uncache=1,cR(g)))}},db={clearProps:function(a,b,c,d,e){if("isFromStart"!==e.data){var f=a._pt=new b8(a._pt,b,c,0,0,da);return f.u=d,f.pr=-10,f.tween=e,a._props.push(c),1}}},dc=[1,0,0,1,0,0],dd={},de=function(a){return"matrix(1, 0, 0, 1, 0, 0)"===a||"none"===a||!a},df=function(a){var b=cV(a,cO);return de(b)?dc:b.substr(7).match(O).map(ao)},dg=function(a,b){var c,d,e,f,g=a._gsap||al(a),h=a.style,i=df(a);return g.svg&&a.getAttribute("transform")?"1,0,0,1,0,0"===(i=[(e=a.transform.baseVal.consolidate().matrix).a,e.b,e.c,e.d,e.e,e.f]).join(",")?dc:i:(i!==dc||a.offsetParent||a===co||g.svg||(e=h.display,h.display="block",(c=a.parentNode)&&(a.offsetParent||a.getBoundingClientRect().width)||(f=1,d=a.nextElementSibling,co.appendChild(a)),i=df(a),e?h.display=e:c1(a,"display"),f&&(d?c.insertBefore(a,d):c?c.appendChild(a):co.removeChild(a))),b&&i.length>6?[i[0],i[1],i[4],i[5],i[12],i[13]]:i)},dh=function(a,b,c,d,e,f){var g,h,i,j,k=a._gsap,l=e||dg(a,!0),m=k.xOrigin||0,n=k.yOrigin||0,o=k.xOffset||0,p=k.yOffset||0,q=l[0],r=l[1],s=l[2],t=l[3],u=l[4],v=l[5],w=b.split(" "),x=parseFloat(w[0])||0,y=parseFloat(w[1])||0;c?l!==dc&&(h=q*t-r*s)&&(i=t/h*x+-s/h*y+(s*v-t*u)/h,j=-r/h*x+q/h*y-(q*v-r*u)/h,x=i,y=j):(x=(g=c_(a)).x+(~w[0].indexOf("%")?x/100*g.width:x),y=g.y+(~(w[1]||w[0]).indexOf("%")?y/100*g.height:y)),d||!1!==d&&k.smooth?(k.xOffset=o+((u=x-m)*q+(v=y-n)*s)-u,k.yOffset=p+(u*r+v*t)-v):k.xOffset=k.yOffset=0,k.xOrigin=x,k.yOrigin=y,k.smooth=!!d,k.origin=b,k.originIsAbsolute=!!c,a.style[cP]="0px 0px",f&&(c2(f,k,"xOrigin",m,x),c2(f,k,"yOrigin",n,y),c2(f,k,"xOffset",o,k.xOffset),c2(f,k,"yOffset",p,k.yOffset)),a.setAttribute("data-svg-origin",x+" "+y)},di=function(a,b){var c=a._gsap||new bG(a);if("x"in c&&!b&&!c.uncache)return c;var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K=a.style,L=c.scaleX<0,M=getComputedStyle(a),N=cV(a,cP)||"0";return d=e=f=i=j=k=l=m=n=0,g=h=1,c.svg=!!(a.getCTM&&c0(a)),M.translate&&(("none"!==M.translate||"none"!==M.scale||"none"!==M.rotate)&&(K[cO]=("none"!==M.translate?"translate3d("+(M.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+("none"!==M.rotate?"rotate("+M.rotate+") ":"")+("none"!==M.scale?"scale("+M.scale.split(" ").join(",")+") ":"")+("none"!==M[cO]?M[cO]:"")),K.scale=K.rotate=K.translate="none"),q=dg(a,c.svg),c.svg&&(c.uncache?(C=a.getBBox(),N=c.xOrigin-C.x+"px "+(c.yOrigin-C.y)+"px",B=""):B=!b&&a.getAttribute("data-svg-origin"),dh(a,B||N,!!B||c.originIsAbsolute,!1!==c.smooth,q)),o=c.xOrigin||0,p=c.yOrigin||0,q!==dc&&(u=q[0],w=q[1],x=q[2],y=q[3],d=z=q[4],e=A=q[5],6===q.length?(g=Math.sqrt(u*u+w*w),h=Math.sqrt(y*y+x*x),i=u||w?cx(w,u)*cv:0,(l=x||y?cx(x,y)*cv+i:0)&&(h*=Math.abs(Math.cos(l*cw))),c.svg&&(d-=o-(o*u+p*x),e-=p-(o*w+p*y))):(J=q[6],H=q[7],E=q[8],F=q[9],G=q[10],I=q[11],d=q[12],e=q[13],f=q[14],j=(r=cx(J,G))*cv,r&&(B=z*(s=Math.cos(-r))+E*(t=Math.sin(-r)),C=A*s+F*t,D=J*s+G*t,E=-(z*t)+E*s,F=-(A*t)+F*s,G=-(J*t)+G*s,I=-(H*t)+I*s,z=B,A=C,J=D),k=(r=cx(-x,G))*cv,r&&(B=u*(s=Math.cos(-r))-E*(t=Math.sin(-r)),C=w*s-F*t,D=x*s-G*t,I=y*t+I*s,u=B,w=C,x=D),i=(r=cx(w,u))*cv,r&&(B=u*(s=Math.cos(r))+w*(t=Math.sin(r)),C=z*s+A*t,w=w*s-u*t,A=A*s-z*t,u=B,z=C),j&&Math.abs(j)+Math.abs(i)>359.9&&(j=i=0,k=180-k),g=ao(Math.sqrt(u*u+w*w+x*x)),h=ao(Math.sqrt(A*A+J*J)),l=Math.abs(r=cx(z,A))>2e-4?r*cv:0,n=I?1/(I<0?-I:I):0),c.svg&&(B=a.getAttribute("transform"),c.forceCSS=a.setAttribute("transform","")||!de(cV(a,cO)),B&&a.setAttribute("transform",B))),Math.abs(l)>90&&270>Math.abs(l)&&(L?(g*=-1,l+=i<=0?180:-180,i+=i<=0?180:-180):(h*=-1,l+=l<=0?180:-180)),b=b||c.uncache,c.x=d-((c.xPercent=d&&(!b&&c.xPercent||(Math.round(a.offsetWidth/2)===Math.round(-d)?-50:0)))?a.offsetWidth*c.xPercent/100:0)+"px",c.y=e-((c.yPercent=e&&(!b&&c.yPercent||(Math.round(a.offsetHeight/2)===Math.round(-e)?-50:0)))?a.offsetHeight*c.yPercent/100:0)+"px",c.z=f+"px",c.scaleX=ao(g),c.scaleY=ao(h),c.rotation=ao(i)+"deg",c.rotationX=ao(j)+"deg",c.rotationY=ao(k)+"deg",c.skewX=l+"deg",c.skewY=m+"deg",c.transformPerspective=n+"px",(c.zOrigin=parseFloat(N.split(" ")[2])||!b&&c.zOrigin||0)&&(K[cP]=dj(N)),c.xOffset=c.yOffset=0,c.force3D=v.force3D,c.renderTransform=c.svg?dp:ct?dn:dl,c.uncache=0,c},dj=function(a){return(a=a.split(" "))[0]+" "+a[1]},dk=function(a,b,c){var d=a1(b);return ao(parseFloat(b)+parseFloat(c5(a,"x",c+"px",d)))+d},dl=function(a,b){b.z="0px",b.rotationY=b.rotationX="0deg",b.force3D=0,dn(a,b)},dm="0deg",dn=function(a,b){var c=b||this,d=c.xPercent,e=c.yPercent,f=c.x,g=c.y,h=c.z,i=c.rotation,j=c.rotationY,k=c.rotationX,l=c.skewX,m=c.skewY,n=c.scaleX,o=c.scaleY,p=c.transformPerspective,q=c.force3D,r=c.target,s=c.zOrigin,t="",u="auto"===q&&a&&1!==a||!0===q;if(s&&(k!==dm||j!==dm)){var v,w=parseFloat(j)*cw,x=Math.sin(w),y=Math.cos(w);f=dk(r,f,-(x*(v=Math.cos(w=parseFloat(k)*cw))*s)),g=dk(r,g,-(-Math.sin(w)*s)),h=dk(r,h,-(y*v*s)+s)}"0px"!==p&&(t+="perspective("+p+") "),(d||e)&&(t+="translate("+d+"%, "+e+"%) "),(u||"0px"!==f||"0px"!==g||"0px"!==h)&&(t+="0px"!==h||u?"translate3d("+f+", "+g+", "+h+") ":"translate("+f+", "+g+") "),i!==dm&&(t+="rotate("+i+") "),j!==dm&&(t+="rotateY("+j+") "),k!==dm&&(t+="rotateX("+k+") "),(l!==dm||m!==dm)&&(t+="skew("+l+", "+m+") "),(1!==n||1!==o)&&(t+="scale("+n+", "+o+") "),r.style[cO]=t||"translate(0, 0)"},dp=function(a,b){var c,d,e,f,g,h=b||this,i=h.xPercent,j=h.yPercent,k=h.x,l=h.y,m=h.rotation,n=h.skewX,o=h.skewY,p=h.scaleX,q=h.scaleY,r=h.target,s=h.xOrigin,t=h.yOrigin,u=h.xOffset,v=h.yOffset,w=h.forceCSS,x=parseFloat(k),y=parseFloat(l);m=parseFloat(m),n=parseFloat(n),(o=parseFloat(o))&&(n+=o=parseFloat(o),m+=o),m||n?(m*=cw,n*=cw,c=Math.cos(m)*p,d=Math.sin(m)*p,e=-(Math.sin(m-n)*q),f=Math.cos(m-n)*q,n&&(o*=cw,e*=g=Math.sqrt(1+(g=Math.tan(n-o))*g),f*=g,o&&(c*=g=Math.sqrt(1+(g=Math.tan(o))*g),d*=g)),c=ao(c),d=ao(d),e=ao(e),f=ao(f)):(c=p,f=q,d=e=0),(x&&!~(k+"").indexOf("px")||y&&!~(l+"").indexOf("px"))&&(x=c5(r,"x",k,"px"),y=c5(r,"y",l,"px")),(s||t||u||v)&&(x=ao(x+s-(s*c+t*e)+u),y=ao(y+t-(s*d+t*f)+v)),(i||j)&&(x=ao(x+i/100*(g=r.getBBox()).width),y=ao(y+j/100*g.height)),g="matrix("+c+","+d+","+e+","+f+","+x+","+y+")",r.setAttribute("transform",g),w&&(r.style[cO]=g)},dq=function(a,b,c,d,e){var f,g,h=D(e),i=parseFloat(e)*(h&&~e.indexOf("rad")?cv:1)-d,j=d+i+"deg";return h&&("short"===(f=e.split("_")[1])&&(i%=360)!=i%180&&(i+=i<0?360:-360),"cw"===f&&i<0?i=(i+36e9)%360-360*~~(i/360):"ccw"===f&&i>0&&(i=(i-36e9)%360-360*~~(i/360))),a._pt=g=new b8(a._pt,b,c,d,i,cD),g.e=j,g.u="deg",a._props.push(c),g},dr=function(a,b){for(var c in b)a[c]=b[c];return a},ds=function(a,b,c){var d,e,f,g,h,i,j,k=dr({},c._gsap),l=c.style;for(e in k.svg?(f=c.getAttribute("transform"),c.setAttribute("transform",""),l[cO]=b,d=di(c,1),c1(c,cO),c.setAttribute("transform",f)):(f=getComputedStyle(c)[cO],l[cO]=b,d=di(c,1),l[cO]=f),cu)(f=k[e])!==(g=d[e])&&0>"perspective,force3D,transformOrigin,svgOrigin".indexOf(e)&&(h=a1(f)!==(j=a1(g))?c5(c,e,f,j):parseFloat(f),i=parseFloat(g),a._pt=new b8(a._pt,d,e,h,i-h,cC),a._pt.u=j||0,a._props.push(e));dr(d,k)};an("padding,margin,Width,Radius",function(a,b){var c="Right",d="Bottom",e="Left",f=(b<3?["Top",c,d,e]:["Top"+e,"Top"+c,d+c,d+e]).map(function(c){return b<2?a+c:"border"+c+a});db[b>1?"border"+a:a]=function(a,b,c,d,e){var g,h;if(arguments.length<4)return 5===(h=(g=f.map(function(b){return c6(a,b,c)})).join(" ")).split(g[0]).length?g[0]:h;g=(d+"").split(" "),h={},f.forEach(function(a,b){return h[a]=g[b]=g[b]||g[(b-1)/2|0]}),a.init(b,h,e)}});var dt={name:"css",register:cY,targetTest:function(a){return a.style&&a.nodeType},init:function(a,b,c,d,e){var f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,w=this._props,x=a.style,y=c.vars.startAt;for(l in cp||cY(),this.styles=this.styles||cT(a),u=this.styles.props,this.tween=c,b)if("autoRound"!==l&&(g=b[l],!(af[l]&&bO(l,b,c,d,a,e)))){if(j=typeof g,k=db[l],"function"===j&&(j=typeof(g=g.call(c,d,a,e))),"string"===j&&~g.indexOf("random(")&&(g=bc(g)),k)k(this,a,l,g,c)&&(t=1);else if("--"===l.substr(0,2))f=(getComputedStyle(a).getPropertyValue(l)+"").trim(),g+="",bo.lastIndex=0,bo.test(f)||(m=a1(f),n=a1(g)),n?m!==n&&(f=c5(a,l,f,n)+n):m&&(g+=m),this.add(x,"setProperty",f,g,d,e,0,0,l),w.push(l),u.push(l,0,x[l]);else if("undefined"!==j){if(y&&l in y?(D(f="function"==typeof y[l]?y[l].call(c,d,a,e):y[l])&&~f.indexOf("random(")&&(f=bc(f)),a1(f+"")||"auto"===f||(f+=v.units[l]||a1(c6(a,l))||""),"="===(f+"").charAt(1)&&(f=c6(a,l))):f=c6(a,l),i=parseFloat(f),(o="string"===j&&"="===g.charAt(1)&&g.substr(0,2))&&(g=g.substr(2)),h=parseFloat(g),l in cB&&("autoAlpha"===l&&(1===i&&"hidden"===c6(a,"visibility")&&h&&(i=0),u.push("visibility",0,x.visibility),c2(this,x,"visibility",i?"inherit":"hidden",h?"inherit":"hidden",!h)),"scale"!==l&&"transform"!==l&&~(l=cB[l]).indexOf(",")&&(l=l.split(",")[0])),p=l in cu){if(this.styles.save(l),"string"===j&&"var(--"===g.substring(0,6)&&(h=parseFloat(g=cV(a,g.substring(4,g.indexOf(")"))))),q||((r=a._gsap).renderTransform&&!b.parseTransform||di(a,b.parseTransform),s=!1!==b.smoothOrigin&&r.smooth,(q=this._pt=new b8(this._pt,x,cO,0,1,r.renderTransform,r,0,-1)).dep=1),"scale"===l)this._pt=new b8(this._pt,r,"scaleY",r.scaleY,(o?aq(r.scaleY,o+h):h)-r.scaleY||0,cC),this._pt.u=0,w.push("scaleY",l),l+="X";else if("transformOrigin"===l){u.push(cP,0,x[cP]),g=c9(g),r.svg?dh(a,g,0,s,0,this):((n=parseFloat(g.split(" ")[2])||0)!==r.zOrigin&&c2(this,r,"zOrigin",r.zOrigin,n),c2(this,x,l,dj(f),dj(g)));continue}else if("svgOrigin"===l){dh(a,g,1,s,0,this);continue}else if(l in dd){dq(this,r,l,i,o?aq(i,o+g):g);continue}else if("smoothOrigin"===l){c2(this,r,"smooth",r.smooth,g);continue}else if("force3D"===l){r[l]=g;continue}else if("transform"===l){ds(this,g,a);continue}}else l in x||(l=cX(l)||l);if(p||(h||0===h)&&(i||0===i)&&!cA.test(g)&&l in x)m=(f+"").substr((i+"").length),h||(h=0),n=a1(g)||(l in v.units?v.units[l]:m),m!==n&&(i=c5(a,l,f,n)),this._pt=new b8(this._pt,p?r:x,l,i,(o?aq(i,o+h):h)-i,!p&&("px"===n||"zIndex"===l)&&!1!==b.autoRound?cF:cC),this._pt.u=n||0,m!==n&&"%"!==n&&(this._pt.b=f,this._pt.r=cE);else if(l in x)c7.call(this,a,l,f,o?o+g:g);else if(l in a)this.add(a,l,f||a[l],o?o+g:g,d,e);else if("parseTransform"!==l){X(l,g);continue}p||(l in x?u.push(l,0,x[l]):"function"==typeof a[l]?u.push(l,2,a[l]()):u.push(l,1,f||a[l])),w.push(l)}}t&&b7(this)},render:function(a,b){if(b.tween._time||!cs())for(var c=b._pt;c;)c.r(a,c.d),c=c._next;else b.styles.revert()},get:c6,aliases:cB,getSetter:function(a,b,c){var d=cB[b];return d&&0>d.indexOf(",")&&(b=d),b in cu&&b!==cP&&(a._gsap.x||c6(a,"x"))?c&&cr===c?"scale"===b?cL:cK:(cr=c||{},"scale"===b?cM:cN):a.style&&!G(a.style[b])?cI:~b.indexOf("-")?cJ:b_(a,b)},core:{_removeProperty:c1,_getMatrix:dg}};cm.utils.checkPrefix=cX,cm.core.getStyleSaver=cT,function(a,b,c,d){var e=an(a+","+b+","+c,function(a){cu[a]=1});an(b,function(a){v.units[a]="deg",dd[a]=1}),cB[e[13]]=a+","+b,an(d,function(a){var b=a.split(":");cB[b[1]]=e[b[0]]})}("x,y,z,scale,scaleX,scaleY,xPercent,yPercent","rotation,rotationX,rotationY,skewX,skewY","transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective","0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY"),an("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",function(a){v.units[a]="px"}),cm.registerPlugin(dt);var du=cm.registerPlugin(dt)||cm;du.core.Tween;var dv,dw,dx,dy,dz,dA,dB,dC,dD,dE,dF,dG,dH,dI=function(){return dv||"undefined"!=typeof window&&(dv=window.gsap)&&dv.registerPlugin&&dv},dJ=1,dK=[],dL=[],dM=[],dN=Date.now,dO=function(a,b){return b},dP=function(){var a=dD.core,b=a.bridge||{},c=a._scrollers,d=a._proxies;c.push.apply(c,dL),d.push.apply(d,dM),dL=c,dM=d,dO=function(a,c){return b[a](c)}},dQ=function(a,b){return~dM.indexOf(a)&&dM[dM.indexOf(a)+1][b]},dR=function(a){return!!~dE.indexOf(a)},dS=function(a,b,c,d,e){return a.addEventListener(b,c,{passive:!1!==d,capture:!!e})},dT=function(a,b,c,d){return a.removeEventListener(b,c,!!d)},dU="scrollLeft",dV="scrollTop",dW=function(){return dF&&dF.isPressed||dL.cache++},dX=function(a,b){var c=function c(d){if(d||0===d){dJ&&(dx.history.scrollRestoration="manual");var e=dF&&dF.isPressed;a(d=c.v=Math.round(d)||(dF&&dF.iOS?1:0)),c.cacheID=dL.cache,e&&dO("ss",d)}else(b||dL.cache!==c.cacheID||dO("ref"))&&(c.cacheID=dL.cache,c.v=a());return c.v+c.offset};return c.offset=0,a&&c},dY={s:dU,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:dX(function(a){return arguments.length?dx.scrollTo(a,dZ.sc()):dx.pageXOffset||dy[dU]||dz[dU]||dA[dU]||0})},dZ={s:dV,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:dY,sc:dX(function(a){return arguments.length?dx.scrollTo(dY.sc(),a):dx.pageYOffset||dy[dV]||dz[dV]||dA[dV]||0})},d$=function(a,b){return(b&&b._ctx&&b._ctx.selector||dv.utils.toArray)(a)[0]||("string"==typeof a&&!1!==dv.config().nullTargetWarn?console.warn("Element not found:",a):null)},d_=function(a,b){for(var c=b.length;c--;)if(b[c]===a||b[c].contains(a))return!0;return!1},d0=function(a,b){var c=b.s,d=b.sc;dR(a)&&(a=dy.scrollingElement||dz);var e=dL.indexOf(a),f=d===dZ.sc?1:2;~e||(e=dL.push(a)-1),dL[e+f]||dS(a,"scroll",dW);var g=dL[e+f],h=g||(dL[e+f]=dX(dQ(a,c),!0)||(dR(a)?d:dX(function(b){return arguments.length?a[c]=b:a[c]})));return h.target=a,g||(h.smooth="smooth"===dv.getProperty(a,"scrollBehavior")),h},d1=function(a,b,c){var d=a,e=a,f=dN(),g=f,h=b||50,i=Math.max(500,3*h),j=function(a,b){var i=dN();b||i-f>h?(e=d,d=a,g=f,f=i):c?d+=a:d=e+(a-e)/(i-g)*(f-g)};return{update:j,reset:function(){e=d=c?0:d,g=f=0},getVelocity:function(a){var b=g,h=e,k=dN();return(a||0===a)&&a!==d&&j(a),f===g||k-g>i?0:(d+(c?h:-h))/((c?k:f)-b)*1e3}}},d2=function(a,b){return b&&!a._gsapAllow&&a.preventDefault(),a.changedTouches?a.changedTouches[0]:a},d3=function(a){var b=Math.max.apply(Math,a),c=Math.min.apply(Math,a);return Math.abs(b)>=Math.abs(c)?b:c},d4=function(){(dD=dv.core.globals().ScrollTrigger)&&dD.core&&dP()},d5=function(a){return dv=a||dI(),!dw&&dv&&"undefined"!=typeof document&&document.body&&(dx=window,dz=(dy=document).documentElement,dA=dy.body,dE=[dx,dy,dz,dA],dv.utils.clamp,dH=dv.core.context||function(){},dC="onpointerenter"in dA?"pointer":"mouse",dB=d6.isTouch=dx.matchMedia&&dx.matchMedia("(hover: none), (pointer: coarse)").matches?1:2*("ontouchstart"in dx||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0),dG=d6.eventTypes=("ontouchstart"in dz?"touchstart,touchmove,touchcancel,touchend":!("onpointerdown"in dz)?"mousedown,mousemove,mouseup,mouseup":"pointerdown,pointermove,pointercancel,pointerup").split(","),setTimeout(function(){return dJ=0},500),d4(),dw=1),dw};dY.op=dZ,dL.cache=0;var d6=function(){var a;function b(a){this.init(a)}return b.prototype.init=function(a){dw||d5(dv)||console.warn("Please gsap.registerPlugin(Observer)"),dD||d4();var b=a.tolerance,c=a.dragMinimum,d=a.type,e=a.target,f=a.lineHeight,g=a.debounce,h=a.preventDefault,i=a.onStop,j=a.onStopDelay,k=a.ignore,l=a.wheelSpeed,m=a.event,n=a.onDragStart,o=a.onDragEnd,p=a.onDrag,q=a.onPress,r=a.onRelease,s=a.onRight,t=a.onLeft,u=a.onUp,v=a.onDown,w=a.onChangeX,x=a.onChangeY,y=a.onChange,z=a.onToggleX,A=a.onToggleY,B=a.onHover,C=a.onHoverEnd,D=a.onMove,E=a.ignoreCheck,F=a.isNormalizer,G=a.onGestureStart,H=a.onGestureEnd,I=a.onWheel,J=a.onEnable,K=a.onDisable,L=a.onClick,M=a.scrollSpeed,N=a.capture,O=a.allowClicks,P=a.lockAxis,Q=a.onLockAxis;this.target=e=d$(e)||dz,this.vars=a,k&&(k=dv.utils.toArray(k)),b=b||1e-9,c=c||0,l=l||1,M=M||1,d=d||"wheel,touch,pointer",g=!1!==g,f||(f=parseFloat(dx.getComputedStyle(dA).lineHeight)||22);var R,S,T,U,V,W,X,Y=this,Z=0,$=0,_=a.passive||!h&&!1!==a.passive,aa=d0(e,dY),ab=d0(e,dZ),ac=aa(),ad=ab(),ae=~d.indexOf("touch")&&!~d.indexOf("pointer")&&"pointerdown"===dG[0],af=dR(e),ag=e.ownerDocument||dy,ah=[0,0,0],ai=[0,0,0],aj=0,ak=function(){return aj=dN()},al=function(a,b){return(Y.event=a)&&k&&d_(a.target,k)||b&&ae&&"touch"!==a.pointerType||E&&E(a,b)},am=function(){var a=Y.deltaX=d3(ah),c=Y.deltaY=d3(ai),d=Math.abs(a)>=b,e=Math.abs(c)>=b;y&&(d||e)&&y(Y,a,c,ah,ai),d&&(s&&Y.deltaX>0&&s(Y),t&&Y.deltaX<0&&t(Y),w&&w(Y),z&&Y.deltaX<0!=Z<0&&z(Y),Z=Y.deltaX,ah[0]=ah[1]=ah[2]=0),e&&(v&&Y.deltaY>0&&v(Y),u&&Y.deltaY<0&&u(Y),x&&x(Y),A&&Y.deltaY<0!=$<0&&A(Y),$=Y.deltaY,ai[0]=ai[1]=ai[2]=0),(U||T)&&(D&&D(Y),T&&(n&&1===T&&n(Y),p&&p(Y),T=0),U=!1),W&&(W=!1,1)&&Q&&Q(Y),V&&(I(Y),V=!1),R=0},an=function(a,b,c){ah[c]+=a,ai[c]+=b,Y._vx.update(a),Y._vy.update(b),g?R||(R=requestAnimationFrame(am)):am()},ao=function(a,b){P&&!X&&(Y.axis=X=Math.abs(a)>Math.abs(b)?"x":"y",W=!0),"y"!==X&&(ah[2]+=a,Y._vx.update(a,!0)),"x"!==X&&(ai[2]+=b,Y._vy.update(b,!0)),g?R||(R=requestAnimationFrame(am)):am()},ap=function(a){if(!al(a,1)){var b=(a=d2(a,h)).clientX,d=a.clientY,e=b-Y.x,f=d-Y.y,g=Y.isDragging;Y.x=b,Y.y=d,(g||(e||f)&&(Math.abs(Y.startX-b)>=c||Math.abs(Y.startY-d)>=c))&&(T=g?2:1,g||(Y.isDragging=!0),ao(e,f))}},aq=Y.onPress=function(a){al(a,1)||a&&a.button||(Y.axis=X=null,S.pause(),Y.isPressed=!0,a=d2(a),Z=$=0,Y.startX=Y.x=a.clientX,Y.startY=Y.y=a.clientY,Y._vx.reset(),Y._vy.reset(),dS(F?e:ag,dG[1],ap,_,!0),Y.deltaX=Y.deltaY=0,q&&q(Y))},ar=Y.onRelease=function(a){if(!al(a,1)){dT(F?e:ag,dG[1],ap,!0);var b=!isNaN(Y.y-Y.startY),c=Y.isDragging,d=c&&(Math.abs(Y.x-Y.startX)>3||Math.abs(Y.y-Y.startY)>3),f=d2(a);!d&&b&&(Y._vx.reset(),Y._vy.reset(),h&&O&&dv.delayedCall(.08,function(){if(dN()-aj>300&&!a.defaultPrevented){if(a.target.click)a.target.click();else if(ag.createEvent){var b=ag.createEvent("MouseEvents");b.initMouseEvent("click",!0,!0,dx,1,f.screenX,f.screenY,f.clientX,f.clientY,!1,!1,!1,!1,0,null),a.target.dispatchEvent(b)}}})),Y.isDragging=Y.isGesturing=Y.isPressed=!1,i&&c&&!F&&S.restart(!0),T&&am(),o&&c&&o(Y),r&&r(Y,d)}},as=function(a){return a.touches&&a.touches.length>1&&(Y.isGesturing=!0)&&G(a,Y.isDragging)},at=function(){return Y.isGesturing=!1,H(Y)},au=function(a){if(!al(a)){var b=aa(),c=ab();an((b-ac)*M,(c-ad)*M,1),ac=b,ad=c,i&&S.restart(!0)}},av=function(a){if(!al(a)){a=d2(a,h),I&&(V=!0);var b=(1===a.deltaMode?f:2===a.deltaMode?dx.innerHeight:1)*l;an(a.deltaX*b,a.deltaY*b,0),i&&!F&&S.restart(!0)}},aw=function(a){if(!al(a)){var b=a.clientX,c=a.clientY,d=b-Y.x,e=c-Y.y;Y.x=b,Y.y=c,U=!0,i&&S.restart(!0),(d||e)&&ao(d,e)}},ax=function(a){Y.event=a,B(Y)},ay=function(a){Y.event=a,C(Y)},az=function(a){return al(a)||d2(a,h)&&L(Y)};S=Y._dc=dv.delayedCall(j||.25,function(){Y._vx.reset(),Y._vy.reset(),S.pause(),i&&i(Y)}).pause(),Y.deltaX=Y.deltaY=0,Y._vx=d1(0,50,!0),Y._vy=d1(0,50,!0),Y.scrollX=aa,Y.scrollY=ab,Y.isDragging=Y.isGesturing=Y.isPressed=!1,dH(this),Y.enable=function(a){return!Y.isEnabled&&(dS(af?ag:e,"scroll",dW),d.indexOf("scroll")>=0&&dS(af?ag:e,"scroll",au,_,N),d.indexOf("wheel")>=0&&dS(e,"wheel",av,_,N),(d.indexOf("touch")>=0&&dB||d.indexOf("pointer")>=0)&&(dS(e,dG[0],aq,_,N),dS(ag,dG[2],ar),dS(ag,dG[3],ar),O&&dS(e,"click",ak,!0,!0),L&&dS(e,"click",az),G&&dS(ag,"gesturestart",as),H&&dS(ag,"gestureend",at),B&&dS(e,dC+"enter",ax),C&&dS(e,dC+"leave",ay),D&&dS(e,dC+"move",aw)),Y.isEnabled=!0,Y.isDragging=Y.isGesturing=Y.isPressed=U=T=!1,Y._vx.reset(),Y._vy.reset(),ac=aa(),ad=ab(),a&&a.type&&aq(a),J&&J(Y)),Y},Y.disable=function(){Y.isEnabled&&(dK.filter(function(a){return a!==Y&&dR(a.target)}).length||dT(af?ag:e,"scroll",dW),Y.isPressed&&(Y._vx.reset(),Y._vy.reset(),dT(F?e:ag,dG[1],ap,!0)),dT(af?ag:e,"scroll",au,N),dT(e,"wheel",av,N),dT(e,dG[0],aq,N),dT(ag,dG[2],ar),dT(ag,dG[3],ar),dT(e,"click",ak,!0),dT(e,"click",az),dT(ag,"gesturestart",as),dT(ag,"gestureend",at),dT(e,dC+"enter",ax),dT(e,dC+"leave",ay),dT(e,dC+"move",aw),Y.isEnabled=Y.isPressed=Y.isDragging=!1,K&&K(Y))},Y.kill=Y.revert=function(){Y.disable();var a=dK.indexOf(Y);a>=0&&dK.splice(a,1),dF===Y&&(dF=0)},dK.push(Y),F&&dR(e)&&(dF=Y),Y.enable(m)},a=[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}],function(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(a,d.key,d)}}(b.prototype,a),b}();d6.version="3.13.0",d6.create=function(a){return new d6(a)},d6.register=d5,d6.getAll=function(){return dK.slice()},d6.getById=function(a){return dK.filter(function(b){return b.vars.id===a})[0]},dI()&&dv.registerPlugin(d6);var d7,d8,d9,ea,eb,ec,ed,ee,ef,eg,eh,ei,ej,ek,el,em,en,eo,ep,eq,er,es,et,eu,ev,ew,ex,ey,ez,eA,eB,eC,eD,eE,eF,eG,eH,eI,eJ=1,eK=Date.now,eL=eK(),eM=0,eN=0,eO=function(a,b,c){var d=e0(a)&&("clamp("===a.substr(0,6)||a.indexOf("max")>-1);return c["_"+b+"Clamp"]=d,d?a.substr(6,a.length-7):a},eP=function(a,b){return b&&(!e0(a)||"clamp("!==a.substr(0,6))?"clamp("+a+")":a},eQ=function(){return ek=1},eR=function(){return ek=0},eS=function(a){return a},eT=function(a){return Math.round(1e5*a)/1e5||0},eU=function(){return"undefined"!=typeof window},eV=function(){return d7||eU()&&(d7=window.gsap)&&d7.registerPlugin&&d7},eW=function(a){return!!~ed.indexOf(a)},eX=function(a){return("Height"===a?eB:d9["inner"+a])||eb["client"+a]||ec["client"+a]},eY=function(a){return dQ(a,"getBoundingClientRect")||(eW(a)?function(){return f2.width=d9.innerWidth,f2.height=eB,f2}:function(){return fm(a)})},eZ=function(a,b,c){var d=c.d,e=c.d2,f=c.a;return(f=dQ(a,"getBoundingClientRect"))?function(){return f()[d]}:function(){return(b?eX(e):a["client"+e])||0}},e$=function(a,b){var c=b.s,d=b.d2,e=b.d,f=b.a;return Math.max(0,(f=dQ(a,c="scroll"+d))?f()-eY(a)()[e]:eW(a)?(eb[c]||ec[c])-eX(d):a[c]-a["offset"+d])},e_=function(a,b){for(var c=0;c<ep.length;c+=3)(!b||~b.indexOf(ep[c+1]))&&a(ep[c],ep[c+1],ep[c+2])},e0=function(a){return"string"==typeof a},e1=function(a){return"function"==typeof a},e2=function(a){return"number"==typeof a},e3=function(a){return"object"==typeof a},e4=function(a,b,c){return a&&a.progress(+!b)&&c&&a.pause()},e5=function(a,b){if(a.enabled){var c=a._ctx?a._ctx.add(function(){return b(a)}):b(a);c&&c.totalTime&&(a.callbackAnimation=c)}},e6=Math.abs,e7="left",e8="right",e9="bottom",fa="width",fb="height",fc="Right",fd="Left",fe="Bottom",ff="padding",fg="margin",fh="Width",fi="Height",fj=function(a){return d9.getComputedStyle(a)},fk=function(a){var b=fj(a).position;a.style.position="absolute"===b||"fixed"===b?b:"relative"},fl=function(a,b){for(var c in b)c in a||(a[c]=b[c]);return a},fm=function(a,b){var c=b&&"matrix(1, 0, 0, 1, 0, 0)"!==fj(a)[el]&&d7.to(a,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),d=a.getBoundingClientRect();return c&&c.progress(0).kill(),d},fn=function(a,b){var c=b.d2;return a["offset"+c]||a["client"+c]||0},fo=function(a){var b,c=[],d=a.labels,e=a.duration();for(b in d)c.push(d[b]/e);return c},fp=function(a){var b=d7.utils.snap(a),c=Array.isArray(a)&&a.slice(0).sort(function(a,b){return a-b});return c?function(a,d,e){var f;if(void 0===e&&(e=.001),!d)return b(a);if(d>0){for(a-=e,f=0;f<c.length;f++)if(c[f]>=a)return c[f];return c[f-1]}for(f=c.length,a+=e;f--;)if(c[f]<=a)return c[f];return c[0]}:function(c,d,e){void 0===e&&(e=.001);var f=b(c);return!d||Math.abs(f-c)<e||f-c<0==d<0?f:b(d<0?c-a:c+a)}},fq=function(a,b,c,d){return c.split(",").forEach(function(c){return a(b,c,d)})},fr=function(a,b,c,d,e){return a.addEventListener(b,c,{passive:!d,capture:!!e})},fs=function(a,b,c,d){return a.removeEventListener(b,c,!!d)},ft=function(a,b,c){(c=c&&c.wheelHandler)&&(a(b,"wheel",c),a(b,"touchmove",c))},fu={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},fv={toggleActions:"play",anticipatePin:0},fw={top:0,left:0,center:.5,bottom:1,right:1},fx=function(a,b){if(e0(a)){var c=a.indexOf("="),d=~c?(a.charAt(c-1)+1)*parseFloat(a.substr(c+1)):0;~c&&(a.indexOf("%")>c&&(d*=b/100),a=a.substr(0,c-1)),a=d+(a in fw?fw[a]*b:~a.indexOf("%")?parseFloat(a)*b/100:parseFloat(a)||0)}return a},fy=function(a,b,c,d,e,f,g,h){var i=e.startColor,j=e.endColor,k=e.fontSize,l=e.indent,m=e.fontWeight,n=ea.createElement("div"),o=eW(c)||"fixed"===dQ(c,"pinType"),p=-1!==a.indexOf("scroller"),q=o?ec:c,r=-1!==a.indexOf("start"),s=r?i:j,t="border-color:"+s+";font-size:"+k+";color:"+s+";font-weight:"+m+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return t+="position:"+((p||h)&&o?"fixed;":"absolute;"),(p||h||!o)&&(t+=(d===dZ?e8:e9)+":"+(f+parseFloat(l))+"px;"),g&&(t+="box-sizing:border-box;text-align:left;width:"+g.offsetWidth+"px;"),n._isStart=r,n.setAttribute("class","gsap-marker-"+a+(b?" marker-"+b:"")),n.style.cssText=t,n.innerText=b||0===b?a+"-"+b:a,q.children[0]?q.insertBefore(n,q.children[0]):q.appendChild(n),n._offset=n["offset"+d.op.d2],fz(n,0,d,r),n},fz=function(a,b,c,d){var e={display:"block"},f=c[d?"os2":"p2"],g=c[d?"p2":"os2"];a._isFlipped=d,e[c.a+"Percent"]=d?-100:0,e[c.a]=d?"1px":0,e["border"+f+fh]=1,e["border"+g+fh]=0,e[c.p]=b+"px",d7.set(a,e)},fA=[],fB={},fC=function(){return eK()-eM>34&&(eF||(eF=requestAnimationFrame(fV)))},fD=function(){et&&et.isPressed&&!(et.startX>ec.clientWidth)||(dL.cache++,et?eF||(eF=requestAnimationFrame(fV)):fV(),eM||fJ("scrollStart"),eM=eK())},fE=function(){ew=d9.innerWidth,ev=d9.innerHeight},fF=function(a){dL.cache++,(!0===a||!ej&&!es&&!ea.fullscreenElement&&!ea.webkitFullscreenElement&&(!eu||ew!==d9.innerWidth||Math.abs(d9.innerHeight-ev)>.25*d9.innerHeight))&&ee.restart(!0)},fG={},fH=[],fI=function a(){return fs(f9,"scrollEnd",a)||fS(!0)},fJ=function(a){return fG[a]&&fG[a].map(function(a){return a()})||fH},fK=[],fL=function(a){for(var b=0;b<fK.length;b+=5)(!a||fK[b+4]&&fK[b+4].query===a)&&(fK[b].style.cssText=fK[b+1],fK[b].getBBox&&fK[b].setAttribute("transform",fK[b+2]||""),fK[b+3].uncache=1)},fM=function(a,b){var c;for(em=0;em<fA.length;em++)(c=fA[em])&&(!b||c._ctx===b)&&(a?c.kill(1):c.revert(!0,!0));eC=!0,b&&fL(b),b||fJ("revert")},fN=function(a,b){dL.cache++,(b||!eG)&&dL.forEach(function(a){return e1(a)&&a.cacheID++&&(a.rec=0)}),e0(a)&&(d9.history.scrollRestoration=ez=a)},fO=0,fP=function(){if(eH!==fO){var a=eH=fO;requestAnimationFrame(function(){return a===fO&&fS(!0)})}},fQ=function(){ec.appendChild(eA),eB=!et&&eA.offsetHeight||d9.innerHeight,ec.removeChild(eA)},fR=function(a){return ef(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(b){return b.style.display=a?"none":"block"})},fS=function(a,b){if(eb=ea.documentElement,ec=ea.body,ed=[d9,ea,eb,ec],eM&&!a&&!eC)return void fr(f9,"scrollEnd",fI);fQ(),eG=f9.isRefreshing=!0,dL.forEach(function(a){return e1(a)&&++a.cacheID&&(a.rec=a())});var c=fJ("refreshInit");eq&&f9.sort(),b||fM(),dL.forEach(function(a){e1(a)&&(a.smooth&&(a.target.style.scrollBehavior="auto"),a(0))}),fA.slice(0).forEach(function(a){return a.refresh()}),eC=!1,fA.forEach(function(a){if(a._subPinOffset&&a.pin){var b=a.vars.horizontal?"offsetWidth":"offsetHeight",c=a.pin[b];a.revert(!0,1),a.adjustPinSpacing(a.pin[b]-c),a.refresh()}}),eD=1,fR(!0),fA.forEach(function(a){var b=e$(a.scroller,a._dir),c="max"===a.vars.end||a._endClamp&&a.end>b,d=a._startClamp&&a.start>=b;(c||d)&&a.setPositions(d?b-1:a.start,c?Math.max(d?b:a.start+1,b):a.end,!0)}),fR(!1),eD=0,c.forEach(function(a){return a&&a.render&&a.render(-1)}),dL.forEach(function(a){e1(a)&&(a.smooth&&requestAnimationFrame(function(){return a.target.style.scrollBehavior="smooth"}),a.rec&&a(a.rec))}),fN(ez,1),ee.pause(),fO++,eG=2,fV(2),fA.forEach(function(a){return e1(a.vars.onRefresh)&&a.vars.onRefresh(a)}),eG=f9.isRefreshing=!1,fJ("refresh")},fT=0,fU=1,fV=function(a){if(2===a||!eG&&!eC){f9.isUpdating=!0,eI&&eI.update(0);var b=fA.length,c=eK(),d=c-eL>=50,e=b&&fA[0].scroll();if(fU=fT>e?-1:1,eG||(fT=e),d&&(eM&&!ek&&c-eM>200&&(eM=0,fJ("scrollEnd")),eh=eL,eL=c),fU<0){for(em=b;em-- >0;)fA[em]&&fA[em].update(0,d);fU=1}else for(em=0;em<b;em++)fA[em]&&fA[em].update(0,d);f9.isUpdating=!1}eF=0},fW=[e7,"top",e9,e8,fg+fe,fg+fc,fg+"Top",fg+fd,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],fX=fW.concat([fa,fb,"boxSizing","max"+fh,"max"+fi,"position",fg,ff,ff+"Top",ff+fc,ff+fe,ff+fd]),fY=function(a,b,c){f_(c);var d=a._gsap;if(d.spacerIsNative)f_(d.spacerState);else if(a._gsap.swappedIn){var e=b.parentNode;e&&(e.insertBefore(a,b),e.removeChild(b))}a._gsap.swappedIn=!1},fZ=function(a,b,c,d){if(!a._gsap.swappedIn){for(var e,f=fW.length,g=b.style,h=a.style;f--;)g[e=fW[f]]=c[e];g.position="absolute"===c.position?"absolute":"relative","inline"===c.display&&(g.display="inline-block"),h[e9]=h[e8]="auto",g.flexBasis=c.flexBasis||"auto",g.overflow="visible",g.boxSizing="border-box",g[fa]=fn(a,dY)+"px",g[fb]=fn(a,dZ)+"px",g[ff]=h[fg]=h.top=h[e7]="0",f_(d),h[fa]=h["max"+fh]=c[fa],h[fb]=h["max"+fi]=c[fb],h[ff]=c[ff],a.parentNode!==b&&(a.parentNode.insertBefore(b,a),b.appendChild(a)),a._gsap.swappedIn=!0}},f$=/([A-Z])/g,f_=function(a){if(a){var b,c,d=a.t.style,e=a.length,f=0;for((a.t._gsap||d7.core.getCache(a.t)).uncache=1;f<e;f+=2)c=a[f+1],b=a[f],c?d[b]=c:d[b]&&d.removeProperty(b.replace(f$,"-$1").toLowerCase())}},f0=function(a){for(var b=fX.length,c=a.style,d=[],e=0;e<b;e++)d.push(fX[e],c[fX[e]]);return d.t=a,d},f1=function(a,b,c){for(var d,e=[],f=a.length,g=8*!!c;g<f;g+=2)d=a[g],e.push(d,d in b?b[d]:a[g+1]);return e.t=a.t,e},f2={left:0,top:0},f3=function(a,b,c,d,e,f,g,h,i,j,k,l,m,n){e1(a)&&(a=a(h)),e0(a)&&"max"===a.substr(0,3)&&(a=l+("="===a.charAt(4)?fx("0"+a.substr(3),c):0));var o,p,q,r=m?m.time():0;if(m&&m.seek(0),isNaN(a)||(a*=1),e2(a))m&&(a=d7.utils.mapRange(m.scrollTrigger.start,m.scrollTrigger.end,0,l,a)),g&&fz(g,c,d,!0);else{e1(b)&&(b=b(h));var s,t,u,v,w=(a||"0").split(" ");(s=fm(q=d$(b,h)||ec)||{}).left||s.top||"none"!==fj(q).display||(v=q.style.display,q.style.display="block",s=fm(q),v?q.style.display=v:q.style.removeProperty("display")),t=fx(w[0],s[d.d]),u=fx(w[1]||"0",c),a=s[d.p]-i[d.p]-j+t+e-u,g&&fz(g,u,d,c-u<20||g._isStart&&u>20),c-=c-u}if(n&&(h[n]=a||-.001,a<0&&(a=0)),f){var x=a+c,y=f._isStart;o="scroll"+d.d2,fz(f,x,d,y&&x>20||!y&&(k?Math.max(ec[o],eb[o]):f.parentNode[o])<=x+1),k&&(i=fm(g),k&&(f.style[d.op.p]=i[d.op.p]-d.op.m-f._offset+"px"))}return m&&q&&(o=fm(q),m.seek(l),p=fm(q),m._caScrollDist=o[d.p]-p[d.p],a=a/m._caScrollDist*l),m&&m.seek(r),m?a:Math.round(a)},f4=/(webkit|moz|length|cssText|inset)/i,f5=function(a,b,c,d){if(a.parentNode!==b){var e,f,g=a.style;if(b===ec){for(e in a._stOrig=g.cssText,f=fj(a))+e||f4.test(e)||!f[e]||"string"!=typeof g[e]||"0"===e||(g[e]=f[e]);g.top=c,g.left=d}else g.cssText=a._stOrig;d7.core.getCache(a).uncache=1,b.appendChild(a)}},f6=function(a,b,c){var d=b,e=d;return function(b){var f=Math.round(a());return f!==d&&f!==e&&Math.abs(f-d)>3&&Math.abs(f-e)>3&&(b=f,c&&c()),e=d,d=Math.round(b)}},f7=function(a,b,c){var d={};d[b.p]="+="+c,d7.set(a,d)},f8=function(a,b){var c=d0(a,b),d="_scroll"+b.p2,e=function b(e,f,g,h,i){var j=b.tween,k=f.onComplete,l={};g=g||c();var m=f6(c,g,function(){j.kill(),b.tween=0});return i=h&&i||0,h=h||e-g,j&&j.kill(),f[d]=e,f.inherit=!1,f.modifiers=l,l[d]=function(){return m(g+h*j.ratio+i*j.ratio*j.ratio)},f.onUpdate=function(){dL.cache++,b.tween&&fV()},f.onComplete=function(){b.tween=0,k&&k.call(j)},j=b.tween=d7.to(a,f)};return a[d]=c,c.wheelHandler=function(){return e.tween&&e.tween.kill()&&(e.tween=0)},fr(a,"wheel",c.wheelHandler),f9.isTouch&&fr(a,"touchmove",c.wheelHandler),e},f9=function(){function a(b,c){d8||a.register(d7)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),ey(this),this.init(b,c)}return a.prototype.init=function(b,c){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),!eN){this.update=this.refresh=this.kill=eS;return}var d,e,f,g,h,i,j,k,l,m,n,o,p,q,r,s,t,u,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M,N,O,P,Q,R,S=b=fl(e0(b)||e2(b)||b.nodeType?{trigger:b}:b,fv),T=S.onUpdate,U=S.toggleClass,V=S.id,W=S.onToggle,X=S.onRefresh,Y=S.scrub,Z=S.trigger,$=S.pin,_=S.pinSpacing,aa=S.invalidateOnRefresh,ab=S.anticipatePin,ac=S.onScrubComplete,ad=S.onSnapComplete,ae=S.once,af=S.snap,ag=S.pinReparent,ah=S.pinSpacer,ai=S.containerAnimation,aj=S.fastScrollEnd,ak=S.preventOverlaps,al=b.horizontal||b.containerAnimation&&!1!==b.horizontal?dY:dZ,am=!Y&&0!==Y,an=d$(b.scroller||d9),ao=d7.core.getCache(an),ap=eW(an),aq=("pinType"in b?b.pinType:dQ(an,"pinType")||ap&&"fixed")==="fixed",ar=[b.onEnter,b.onLeave,b.onEnterBack,b.onLeaveBack],as=am&&b.toggleActions.split(" "),at="markers"in b?b.markers:fv.markers,au=ap?0:parseFloat(fj(an)["border"+al.p2+fh])||0,av=this,aw=b.onRefreshInit&&function(){return b.onRefreshInit(av)},ax=eZ(an,ap,al),ay=!ap||~dM.indexOf(an)?eY(an):function(){return f2},az=0,aA=0,aB=0,aC=d0(an,al);if(av._startClamp=av._endClamp=!1,av._dir=al,ab*=45,av.scroller=an,av.scroll=ai?ai.time.bind(ai):aC,i=aC(),av.vars=b,c=c||b.animation,"refreshPriority"in b&&(eq=1,-9999===b.refreshPriority&&(eI=av)),ao.tweenScroll=ao.tweenScroll||{top:f8(an,dZ),left:f8(an,dY)},av.tweenTo=f=ao.tweenScroll[al.p],av.scrubDuration=function(a){(L=e2(a)&&a)?K?K.duration(a):K=d7.to(c,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:L,paused:!0,onComplete:function(){return ac&&ac(av)}}):(K&&K.progress(1).kill(),K=0)},c&&(c.vars.lazy=!1,c._initted&&!av.isReverted||!1!==c.vars.immediateRender&&!1!==b.immediateRender&&c.duration()&&c.render(0,!0,!0),av.animation=c.pause(),c.scrollTrigger=av,av.scrubDuration(Y),I=0,V||(V=c.vars.id)),af&&((!e3(af)||af.push)&&(af={snapTo:af}),"scrollBehavior"in ec.style&&d7.set(ap?[ec,eb]:an,{scrollBehavior:"auto"}),dL.forEach(function(a){return e1(a)&&a.target===(ap?ea.scrollingElement||eb:an)&&(a.smooth=!1)}),h=e1(af.snapTo)?af.snapTo:"labels"===af.snapTo?(d=c,function(a){return d7.utils.snap(fo(d),a)}):"labelsDirectional"===af.snapTo?(e=c,function(a,b){return fp(fo(e))(a,b.direction)}):!1!==af.directional?function(a,b){return fp(af.snapTo)(a,eK()-aA<500?0:b.direction)}:d7.utils.snap(af.snapTo),M=e3(M=af.duration||{min:.1,max:2})?eg(M.min,M.max):eg(M,M),N=d7.delayedCall(af.delay||L/2||.1,function(){var a=aC(),b=eK()-aA<500,d=f.tween;if((b||10>Math.abs(av.getVelocity()))&&!d&&!ek&&az!==a){var e,g,i=(a-k)/s,j=c&&!am?c.totalProgress():i,m=b?0:(j-J)/(eK()-eh)*1e3||0,n=d7.utils.clamp(-i,1-i,e6(m/2)*m/.185),o=i+(!1===af.inertia?0:n),p=af,q=p.onStart,r=p.onInterrupt,t=p.onComplete;if(e2(e=h(o,av))||(e=o),g=Math.max(0,Math.round(k+e*s)),a<=l&&a>=k&&g!==a){if(d&&!d._initted&&d.data<=e6(g-a))return;!1===af.inertia&&(n=e-i),f(g,{duration:M(e6(.185*Math.max(e6(o-j),e6(e-j))/m/.05||0)),ease:af.ease||"power3",data:e6(g-a),onInterrupt:function(){return N.restart(!0)&&r&&r(av)},onComplete:function(){av.update(),az=aC(),c&&!am&&(K?K.resetTo("totalProgress",e,c._tTime/c._tDur):c.progress(e)),I=J=c&&!am?c.totalProgress():av.progress,ad&&ad(av),t&&t(av)}},a,n*s,g-a-n*s),q&&q(av,f.tween)}}else av.isActive&&az!==a&&N.restart(!0)}).pause()),V&&(fB[V]=av),(R=(Z=av.trigger=d$(Z||!0!==$&&$))&&Z._gsap&&Z._gsap.stRevert)&&(R=R(av)),$=!0===$?Z:d$($),e0(U)&&(U={targets:Z,className:U}),$&&(!1===_||_===fg||(_=(!!_||!$.parentNode||!$.parentNode.style||"flex"!==fj($.parentNode).display)&&ff),av.pin=$,(g=d7.core.getCache($)).spacer?t=g.pinState:(ah&&((ah=d$(ah))&&!ah.nodeType&&(ah=ah.current||ah.nativeElement),g.spacerIsNative=!!ah,ah&&(g.spacerState=f0(ah))),g.spacer=w=ah||ea.createElement("div"),w.classList.add("pin-spacer"),V&&w.classList.add("pin-spacer-"+V),g.pinState=t=f0($)),!1!==b.force3D&&d7.set($,{force3D:!0}),av.spacer=w=g.spacer,C=(H=fj($))[_+al.os2],y=d7.getProperty($),z=d7.quickSetter($,al.a,"px"),fZ($,w,H),v=f0($)),at){q=e3(at)?fl(at,fu):fu,o=fy("scroller-start",V,an,al,q,0),p=fy("scroller-end",V,an,al,q,0,o),x=o["offset"+al.op.d2];var aD=d$(dQ(an,"content")||an);m=this.markerStart=fy("start",V,aD,al,q,x,0,ai),n=this.markerEnd=fy("end",V,aD,al,q,x,0,ai),ai&&(Q=d7.quickSetter([m,n],al.a,"px")),aq||dM.length&&!0===dQ(an,"fixedMarkers")||(fk(ap?ec:an),d7.set([o,p],{force3D:!0}),E=d7.quickSetter(o,al.a,"px"),G=d7.quickSetter(p,al.a,"px"))}if(ai){var aE=ai.vars.onUpdate,aF=ai.vars.onUpdateParams;ai.eventCallback("onUpdate",function(){av.update(0,0,1),aE&&aE.apply(ai,aF||[])})}if(av.previous=function(){return fA[fA.indexOf(av)-1]},av.next=function(){return fA[fA.indexOf(av)+1]},av.revert=function(a,b){if(!b)return av.kill(!0);var d=!1!==a||!av.enabled,e=ej;d!==av.isReverted&&(d&&(O=Math.max(aC(),av.scroll.rec||0),aB=av.progress,P=c&&c.progress()),m&&[m,n,o,p].forEach(function(a){return a.style.display=d?"none":"block"}),d&&(ej=av,av.update(d)),!$||ag&&av.isActive||(d?fY($,w,t):fZ($,w,fj($),D)),d||av.update(d),ej=e,av.isReverted=d)},av.refresh=function(d,e,g,h){if(!ej&&av.enabled||e){if($&&d&&eM)return void fr(a,"scrollEnd",fI);!eG&&aw&&aw(av),ej=av,f.tween&&!g&&(f.tween.kill(),f.tween=0),K&&K.pause(),aa&&c&&(c.revert({kill:!1}).invalidate(),c.getChildren&&c.getChildren(!0,!0,!1).forEach(function(a){return a.vars.immediateRender&&a.render(0,!0,!0)})),av.isReverted||av.revert(!0,!0),av._subPinOffset=!1;var q,x,z,C,E,G,H,I,J,L,M,Q,R,S=ax(),T=ay(),U=ai?ai.duration():e$(an,al),V=s<=.01||!s,W=0,Y=h||0,ab=e3(g)?g.end:b.end,ac=b.endTrigger||Z,ad=e3(g)?g.start:b.start||(0!==b.start&&Z?$?"0 0":"0 100%":0),ae=av.pinnedContainer=b.pinnedContainer&&d$(b.pinnedContainer,av),af=Z&&Math.max(0,fA.indexOf(av))||0,ah=af;for(at&&e3(g)&&(Q=d7.getProperty(o,al.p),R=d7.getProperty(p,al.p));ah-- >0;)(G=fA[ah]).end||G.refresh(0,1)||(ej=av),(H=G.pin)&&(H===Z||H===$||H===ae)&&!G.isReverted&&(L||(L=[]),L.unshift(G),G.revert(!0,!0)),G!==fA[ah]&&(af--,ah--);for(e1(ad)&&(ad=ad(av)),k=f3(ad=eO(ad,"start",av),Z,S,al,aC(),m,o,av,T,au,aq,U,ai,av._startClamp&&"_startClamp")||($?-.001:0),e1(ab)&&(ab=ab(av)),e0(ab)&&!ab.indexOf("+=")&&(~ab.indexOf(" ")?ab=(e0(ad)?ad.split(" ")[0]:"")+ab:(W=fx(ab.substr(2),S),ab=e0(ad)?ad:(ai?d7.utils.mapRange(0,ai.duration(),ai.scrollTrigger.start,ai.scrollTrigger.end,k):k)+W,ac=Z)),ab=eO(ab,"end",av),l=Math.max(k,f3(ab||(ac?"100% 0":U),ac,S,al,aC()+W,n,p,av,T,au,aq,U,ai,av._endClamp&&"_endClamp"))||-.001,W=0,ah=af;ah--;)(H=(G=fA[ah]).pin)&&G.start-G._pinPush<=k&&!ai&&G.end>0&&(q=G.end-(av._startClamp?Math.max(0,G.start):G.start),(H===Z&&G.start-G._pinPush<k||H===ae)&&isNaN(ad)&&(W+=q*(1-G.progress)),H===$&&(Y+=q));if(k+=W,l+=W,av._startClamp&&(av._startClamp+=W),av._endClamp&&!eG&&(av._endClamp=l||-.001,l=Math.min(l,e$(an,al))),s=l-k||(k-=.01)&&.001,V&&(aB=d7.utils.clamp(0,1,d7.utils.normalize(k,l,O))),av._pinPush=Y,m&&W&&((q={})[al.a]="+="+W,ae&&(q[al.p]="-="+aC()),d7.set([m,n],q)),$&&!(eD&&av.end>=e$(an,al)))q=fj($),C=al===dZ,z=aC(),A=parseFloat(y(al.a))+Y,!U&&l>1&&(M={style:M=(ap?ea.scrollingElement||eb:an).style,value:M["overflow"+al.a.toUpperCase()]},ap&&"scroll"!==fj(ec)["overflow"+al.a.toUpperCase()]&&(M.style["overflow"+al.a.toUpperCase()]="scroll")),fZ($,w,q),v=f0($),x=fm($,!0),I=aq&&d0(an,C?dY:dZ)(),_?((D=[_+al.os2,s+Y+"px"]).t=w,(ah=_===ff?fn($,al)+s+Y:0)&&(D.push(al.d,ah+"px"),"auto"!==w.style.flexBasis&&(w.style.flexBasis=ah+"px")),f_(D),ae&&fA.forEach(function(a){a.pin===ae&&!1!==a.vars.pinSpacing&&(a._subPinOffset=!0)}),aq&&aC(O)):(ah=fn($,al))&&"auto"!==w.style.flexBasis&&(w.style.flexBasis=ah+"px"),aq&&((E={top:x.top+(C?z-k:I)+"px",left:x.left+(C?I:z-k)+"px",boxSizing:"border-box",position:"fixed"})[fa]=E["max"+fh]=Math.ceil(x.width)+"px",E[fb]=E["max"+fi]=Math.ceil(x.height)+"px",E[fg]=E[fg+"Top"]=E[fg+fc]=E[fg+fe]=E[fg+fd]="0",E[ff]=q[ff],E[ff+"Top"]=q[ff+"Top"],E[ff+fc]=q[ff+fc],E[ff+fe]=q[ff+fe],E[ff+fd]=q[ff+fd],u=f1(t,E,ag),eG&&aC(0)),c?(J=c._initted,er(1),c.render(c.duration(),!0,!0),B=y(al.a)-A+s+Y,F=Math.abs(s-B)>1,aq&&F&&u.splice(u.length-2,2),c.render(0,!0,!0),J||c.invalidate(!0),c.parent||c.totalTime(c.totalTime()),er(0)):B=s,M&&(M.value?M.style["overflow"+al.a.toUpperCase()]=M.value:M.style.removeProperty("overflow-"+al.a));else if(Z&&aC()&&!ai)for(x=Z.parentNode;x&&x!==ec;)x._pinOffset&&(k-=x._pinOffset,l-=x._pinOffset),x=x.parentNode;L&&L.forEach(function(a){return a.revert(!1,!0)}),av.start=k,av.end=l,i=j=eG?O:aC(),ai||eG||(i<O&&aC(O),av.scroll.rec=0),av.revert(!1,!0),aA=eK(),N&&(az=-1,N.restart(!0)),ej=0,c&&am&&(c._initted||P)&&c.progress()!==P&&c.progress(P||0,!0).render(c.time(),!0,!0),(V||aB!==av.progress||ai||aa||c&&!c._initted)&&(c&&!am&&(c._initted||aB||!1!==c.vars.immediateRender)&&c.totalProgress(ai&&k<-.001&&!aB?d7.utils.normalize(k,l,0):aB,!0),av.progress=V||(i-k)/s===aB?0:aB),$&&_&&(w._pinOffset=Math.round(av.progress*B)),K&&K.invalidate(),isNaN(Q)||(Q-=d7.getProperty(o,al.p),R-=d7.getProperty(p,al.p),f7(o,al,Q),f7(m,al,Q-(h||0)),f7(p,al,R),f7(n,al,R-(h||0))),V&&!eG&&av.update(),!X||eG||r||(r=!0,X(av),r=!1)}},av.getVelocity=function(){return(aC()-j)/(eK()-eh)*1e3||0},av.endAnimation=function(){e4(av.callbackAnimation),c&&(K?K.progress(1):c.paused()?am||e4(c,av.direction<0,1):e4(c,c.reversed()))},av.labelToScroll=function(a){return c&&c.labels&&(k||av.refresh()||k)+c.labels[a]/c.duration()*s||0},av.getTrailing=function(a){var b=fA.indexOf(av),c=av.direction>0?fA.slice(0,b).reverse():fA.slice(b+1);return(e0(a)?c.filter(function(b){return b.vars.preventOverlaps===a}):c).filter(function(a){return av.direction>0?a.end<=k:a.start>=l})},av.update=function(a,b,d){if(!ai||d||a){var e,g,h,m,n,p,q,r=!0===eG?O:av.scroll(),t=a?0:(r-k)/s,x=t<0?0:t>1?1:t||0,y=av.progress;if(b&&(j=i,i=ai?aC():r,af&&(J=I,I=c&&!am?c.totalProgress():x)),ab&&$&&!ej&&!eJ&&eM&&(!x&&k<r+(r-j)/(eK()-eh)*ab?x=1e-4:1===x&&l>r+(r-j)/(eK()-eh)*ab&&(x=.9999)),x!==y&&av.enabled){if(m=(n=(e=av.isActive=!!x&&x<1)!=(!!y&&y<1))||!!x!=!!y,av.direction=x>y?1:-1,av.progress=x,m&&!ej&&(g=x&&!y?0:1===x?1:1===y?2:3,am&&(h=!n&&"none"!==as[g+1]&&as[g+1]||as[g],q=c&&("complete"===h||"reset"===h||h in c))),ak&&(n||q)&&(q||Y||!c)&&(e1(ak)?ak(av):av.getTrailing(ak).forEach(function(a){return a.endAnimation()})),!am&&(!K||ej||eJ?c&&c.totalProgress(x,!!(ej&&(aA||a))):(K._dp._time-K._start!==K._time&&K.render(K._dp._time-K._start),K.resetTo?K.resetTo("totalProgress",x,c._tTime/c._tDur):(K.vars.totalProgress=x,K.invalidate().restart()))),$)if(a&&_&&(w.style[_+al.os2]=C),aq){if(m){if(p=!a&&x>y&&l+1>r&&r+1>=e$(an,al),ag)if(!a&&(e||p)){var D=fm($,!0),H=r-k;f5($,ec,D.top+(al===dZ?H:0)+"px",D.left+(al===dZ?0:H)+"px")}else f5($,w);f_(e||p?u:v),F&&x<1&&e||z(A+(1!==x||p?0:B))}}else z(eT(A+B*x));!af||f.tween||ej||eJ||N.restart(!0),U&&(n||ae&&x&&(x<1||!eE))&&ef(U.targets).forEach(function(a){return a.classList[e||ae?"add":"remove"](U.className)}),!T||am||a||T(av),m&&!ej?(am&&(q&&("complete"===h?c.pause().totalProgress(1):"reset"===h?c.restart(!0).pause():"restart"===h?c.restart(!0):c[h]()),T&&T(av)),(n||!eE)&&(W&&n&&e5(av,W),ar[g]&&e5(av,ar[g]),ae&&(1===x?av.kill(!1,1):ar[g]=0),!n&&ar[g=1===x?1:3]&&e5(av,ar[g])),aj&&!e&&Math.abs(av.getVelocity())>(e2(aj)?aj:2500)&&(e4(av.callbackAnimation),K?K.progress(1):e4(c,"reverse"===h?1:!x,1))):am&&T&&!ej&&T(av)}if(G){var L=ai?r/ai.duration()*(ai._caScrollDist||0):r;E(L+ +!!o._isFlipped),G(L)}Q&&Q(-r/ai.duration()*(ai._caScrollDist||0))}},av.enable=function(b,c){av.enabled||(av.enabled=!0,fr(an,"resize",fF),ap||fr(an,"scroll",fD),aw&&fr(a,"refreshInit",aw),!1!==b&&(av.progress=aB=0,i=j=az=aC()),!1!==c&&av.refresh())},av.getTween=function(a){return a&&f?f.tween:K},av.setPositions=function(a,b,c,d){if(ai){var e=ai.scrollTrigger,f=ai.duration(),g=e.end-e.start;a=e.start+g*a/f,b=e.start+g*b/f}av.refresh(!1,!1,{start:eP(a,c&&!!av._startClamp),end:eP(b,c&&!!av._endClamp)},d),av.update()},av.adjustPinSpacing=function(a){if(D&&a){var b=D.indexOf(al.d)+1;D[b]=parseFloat(D[b])+a+"px",D[1]=parseFloat(D[1])+a+"px",f_(D)}},av.disable=function(b,c){if(av.enabled&&(!1!==b&&av.revert(!0,!0),av.enabled=av.isActive=!1,c||K&&K.pause(),O=0,g&&(g.uncache=1),aw&&fs(a,"refreshInit",aw),N&&(N.pause(),f.tween&&f.tween.kill()&&(f.tween=0)),!ap)){for(var d=fA.length;d--;)if(fA[d].scroller===an&&fA[d]!==av)return;fs(an,"resize",fF),ap||fs(an,"scroll",fD)}},av.kill=function(a,d){av.disable(a,d),K&&!d&&K.kill(),V&&delete fB[V];var e=fA.indexOf(av);e>=0&&fA.splice(e,1),e===em&&fU>0&&em--,e=0,fA.forEach(function(a){return a.scroller===av.scroller&&(e=1)}),e||eG||(av.scroll.rec=0),c&&(c.scrollTrigger=null,a&&c.revert({kill:!1}),d||c.kill()),m&&[m,n,o,p].forEach(function(a){return a.parentNode&&a.parentNode.removeChild(a)}),eI===av&&(eI=0),$&&(g&&(g.uncache=1),e=0,fA.forEach(function(a){return a.pin===$&&e++}),e||(g.spacer=0)),b.onKill&&b.onKill(av)},fA.push(av),av.enable(!1,!1),R&&R(av),c&&c.add&&!s){var aG=av.update;av.update=function(){av.update=aG,dL.cache++,k||l||av.refresh()},d7.delayedCall(.01,av.update),s=.01,k=l=0}else av.refresh();$&&fP()},a.register=function(b){return d8||(d7=b||eV(),eU()&&window.document&&a.enable(),d8=eN),d8},a.defaults=function(a){if(a)for(var b in a)fv[b]=a[b];return fv},a.disable=function(a,b){eN=0,fA.forEach(function(c){return c[b?"kill":"disable"](a)}),fs(d9,"wheel",fD),fs(ea,"scroll",fD),clearInterval(ei),fs(ea,"touchcancel",eS),fs(ec,"touchstart",eS),fq(fs,ea,"pointerdown,touchstart,mousedown",eQ),fq(fs,ea,"pointerup,touchend,mouseup",eR),ee.kill(),e_(fs);for(var c=0;c<dL.length;c+=3)ft(fs,dL[c],dL[c+1]),ft(fs,dL[c],dL[c+2])},a.enable=function(){if(d9=window,eb=(ea=document).documentElement,ec=ea.body,d7&&(ef=d7.utils.toArray,eg=d7.utils.clamp,ey=d7.core.context||eS,er=d7.core.suppressOverwrites||eS,ez=d9.history.scrollRestoration||"auto",fT=d9.pageYOffset||0,d7.core.globals("ScrollTrigger",a),ec)){eN=1,(eA=document.createElement("div")).style.height="100vh",eA.style.position="absolute",fQ(),function a(){return eN&&requestAnimationFrame(a)}(),d6.register(d7),a.isTouch=d6.isTouch,ex=d6.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),eu=1===d6.isTouch,fr(d9,"wheel",fD),ed=[d9,ea,eb,ec],d7.matchMedia?(a.matchMedia=function(a){var b,c=d7.matchMedia();for(b in a)c.add(b,a[b]);return c},d7.addEventListener("matchMediaInit",function(){return fM()}),d7.addEventListener("matchMediaRevert",function(){return fL()}),d7.addEventListener("matchMedia",function(){fS(0,1),fJ("matchMedia")}),d7.matchMedia().add("(orientation: portrait)",function(){return fE(),fE})):console.warn("Requires GSAP 3.11.0 or later"),fE(),fr(ea,"scroll",fD);var b,c,d=ec.hasAttribute("style"),e=ec.style,f=e.borderTopStyle,g=d7.core.Animation.prototype;for(g.revert||Object.defineProperty(g,"revert",{value:function(){return this.time(-.01,!0)}}),e.borderTopStyle="solid",dZ.m=Math.round((b=fm(ec)).top+dZ.sc())||0,dY.m=Math.round(b.left+dY.sc())||0,f?e.borderTopStyle=f:e.removeProperty("border-top-style"),d||(ec.setAttribute("style",""),ec.removeAttribute("style")),ei=setInterval(fC,250),d7.delayedCall(.5,function(){return eJ=0}),fr(ea,"touchcancel",eS),fr(ec,"touchstart",eS),fq(fr,ea,"pointerdown,touchstart,mousedown",eQ),fq(fr,ea,"pointerup,touchend,mouseup",eR),el=d7.utils.checkPrefix("transform"),fX.push(el),d8=eK(),ee=d7.delayedCall(.2,fS).pause(),ep=[ea,"visibilitychange",function(){var a=d9.innerWidth,b=d9.innerHeight;ea.hidden?(en=a,eo=b):(en!==a||eo!==b)&&fF()},ea,"DOMContentLoaded",fS,d9,"load",fS,d9,"resize",fF],e_(fr),fA.forEach(function(a){return a.enable(0,1)}),c=0;c<dL.length;c+=3)ft(fs,dL[c],dL[c+1]),ft(fs,dL[c],dL[c+2])}},a.config=function(b){"limitCallbacks"in b&&(eE=!!b.limitCallbacks);var c=b.syncInterval;c&&clearInterval(ei)||(ei=c)&&setInterval(fC,c),"ignoreMobileResize"in b&&(eu=1===a.isTouch&&b.ignoreMobileResize),"autoRefreshEvents"in b&&(e_(fs)||e_(fr,b.autoRefreshEvents||"none"),es=-1===(b.autoRefreshEvents+"").indexOf("resize"))},a.scrollerProxy=function(a,b){var c=d$(a),d=dL.indexOf(c),e=eW(c);~d&&dL.splice(d,e?6:2),b&&(e?dM.unshift(d9,b,ec,b,eb,b):dM.unshift(c,b))},a.clearMatchMedia=function(a){fA.forEach(function(b){return b._ctx&&b._ctx.query===a&&b._ctx.kill(!0,!0)})},a.isInViewport=function(a,b,c){var d=(e0(a)?d$(a):a).getBoundingClientRect(),e=d[c?fa:fb]*b||0;return c?d.right-e>0&&d.left+e<d9.innerWidth:d.bottom-e>0&&d.top+e<d9.innerHeight},a.positionInViewport=function(a,b,c){e0(a)&&(a=d$(a));var d=a.getBoundingClientRect(),e=d[c?fa:fb],f=null==b?e/2:b in fw?fw[b]*e:~b.indexOf("%")?parseFloat(b)*e/100:parseFloat(b)||0;return c?(d.left+f)/d9.innerWidth:(d.top+f)/d9.innerHeight},a.killAll=function(a){if(fA.slice(0).forEach(function(a){return"ScrollSmoother"!==a.vars.id&&a.kill()}),!0!==a){var b=fG.killAll||[];fG={},b.forEach(function(a){return a()})}},a}();f9.version="3.13.0",f9.saveStyles=function(a){return a?ef(a).forEach(function(a){if(a&&a.style){var b=fK.indexOf(a);b>=0&&fK.splice(b,5),fK.push(a,a.style.cssText,a.getBBox&&a.getAttribute("transform"),d7.core.getCache(a),ey())}}):fK},f9.revert=function(a,b){return fM(!a,b)},f9.create=function(a,b){return new f9(a,b)},f9.refresh=function(a){return a?fF(!0):(d8||f9.register())&&fS(!0)},f9.update=function(a){return++dL.cache&&fV(2*(!0===a))},f9.clearScrollMemory=fN,f9.maxScroll=function(a,b){return e$(a,b?dY:dZ)},f9.getScrollFunc=function(a,b){return d0(d$(a),b?dY:dZ)},f9.getById=function(a){return fB[a]},f9.getAll=function(){return fA.filter(function(a){return"ScrollSmoother"!==a.vars.id})},f9.isScrolling=function(){return!!eM},f9.snapDirectional=fp,f9.addEventListener=function(a,b){var c=fG[a]||(fG[a]=[]);~c.indexOf(b)||c.push(b)},f9.removeEventListener=function(a,b){var c=fG[a],d=c&&c.indexOf(b);d>=0&&c.splice(d,1)},f9.batch=function(a,b){var c,d=[],e={},f=b.interval||.016,g=b.batchMax||1e9,h=function(a,b){var c=[],d=[],e=d7.delayedCall(f,function(){b(c,d),c=[],d=[]}).pause();return function(a){c.length||e.restart(!0),c.push(a.trigger),d.push(a),g<=c.length&&e.progress(1)}};for(c in b)e[c]="on"===c.substr(0,2)&&e1(b[c])&&"onRefreshInit"!==c?h(c,b[c]):b[c];return e1(g)&&(g=g(),fr(f9,"refresh",function(){return g=b.batchMax()})),ef(a).forEach(function(a){var b={};for(c in e)b[c]=e[c];b.trigger=a,d.push(f9.create(b))}),d};var ga,gb=function(a,b,c,d){return b>d?a(d):b<0&&a(0),c>d?(d-b)/(c-b):c<0?b/(b-c):1},gc=function a(b,c){!0===c?b.style.removeProperty("touch-action"):b.style.touchAction=!0===c?"auto":c?"pan-"+c+(d6.isTouch?" pinch-zoom":""):"none",b===eb&&a(ec,c)},gd={auto:1,scroll:1},ge=function(a){var b,c=a.event,d=a.target,e=a.axis,f=(c.changedTouches?c.changedTouches[0]:c).target,g=f._gsap||d7.core.getCache(f),h=eK();if(!g._isScrollT||h-g._isScrollT>2e3){for(;f&&f!==ec&&(f.scrollHeight<=f.clientHeight&&f.scrollWidth<=f.clientWidth||!(gd[(b=fj(f)).overflowY]||gd[b.overflowX]));)f=f.parentNode;g._isScroll=f&&f!==d&&!eW(f)&&(gd[(b=fj(f)).overflowY]||gd[b.overflowX]),g._isScrollT=h}(g._isScroll||"x"===e)&&(c.stopPropagation(),c._gsapAllow=!0)},gf=function(a,b,c,d){return d6.create({target:a,capture:!0,debounce:!1,lockAxis:!0,type:b,onWheel:d=d&&ge,onPress:d,onDrag:d,onScroll:d,onEnable:function(){return c&&fr(ea,d6.eventTypes[0],gh,!1,!0)},onDisable:function(){return fs(ea,d6.eventTypes[0],gh,!0)}})},gg=/(input|label|select|textarea)/i,gh=function(a){var b=gg.test(a.target.tagName);(b||ga)&&(a._gsapAllow=!0,ga=b)},gi=function(a){e3(a)||(a={}),a.preventDefault=a.isNormalizer=a.allowClicks=!0,a.type||(a.type="wheel,touch"),a.debounce=!!a.debounce,a.id=a.id||"normalizer";var b,c,d,e,f,g,h,i,j=a,k=j.normalizeScrollX,l=j.momentum,m=j.allowNestedScroll,n=j.onRelease,o=d$(a.target)||eb,p=d7.core.globals().ScrollSmoother,q=p&&p.get(),r=ex&&(a.content&&d$(a.content)||q&&!1!==a.content&&!q.smooth()&&q.content()),s=d0(o,dZ),t=d0(o,dY),u=1,v=(d6.isTouch&&d9.visualViewport?d9.visualViewport.scale*d9.visualViewport.width:d9.outerWidth)/d9.innerWidth,w=0,x=e1(l)?function(){return l(b)}:function(){return l||2.8},y=gf(o,a.type,!0,m),z=function(){return e=!1},A=eS,B=eS,C=function(){c=e$(o,dZ),B=eg(+!!ex,c),k&&(A=eg(0,e$(o,dY))),d=fO},D=function(){r._gsap.y=eT(parseFloat(r._gsap.y)+s.offset)+"px",r.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(r._gsap.y)+", 0, 1)",s.offset=s.cacheID=0},E=function(){if(e){requestAnimationFrame(z);var a=eT(b.deltaY/2),c=B(s.v-a);if(r&&c!==s.v+s.offset){s.offset=c-s.v;var d=eT((parseFloat(r&&r._gsap.y)||0)-s.offset);r.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+d+", 0, 1)",r._gsap.y=d+"px",s.cacheID=dL.cache,fV()}return!0}s.offset&&D(),e=!0},F=function(){C(),f.isActive()&&f.vars.scrollY>c&&(s()>c?f.progress(1)&&s(c):f.resetTo("scrollY",c))};return r&&d7.set(r,{y:"+=0"}),a.ignoreCheck=function(a){return ex&&"touchmove"===a.type&&E(a)||u>1.05&&"touchstart"!==a.type||b.isGesturing||a.touches&&a.touches.length>1},a.onPress=function(){e=!1;var a=u;u=eT((d9.visualViewport&&d9.visualViewport.scale||1)/v),f.pause(),a!==u&&gc(o,u>1.01||!k&&"x"),g=t(),h=s(),C(),d=fO},a.onRelease=a.onGestureStart=function(a,b){if(s.offset&&D(),b){dL.cache++;var d,e,g=x();k&&(e=(d=t())+-(.05*g*a.velocityX)/.227,g*=gb(t,d,e,e$(o,dY)),f.vars.scrollX=A(e)),e=(d=s())+-(.05*g*a.velocityY)/.227,g*=gb(s,d,e,e$(o,dZ)),f.vars.scrollY=B(e),f.invalidate().duration(g).play(.01),(ex&&f.vars.scrollY>=c||d>=c-1)&&d7.to({},{onUpdate:F,duration:g})}else i.restart(!0);n&&n(a)},a.onWheel=function(){f._ts&&f.pause(),eK()-w>1e3&&(d=0,w=eK())},a.onChange=function(a,b,c,e,f){if(fO!==d&&C(),b&&k&&t(A(e[2]===b?g+(a.startX-a.x):t()+b-e[1])),c){s.offset&&D();var i=f[2]===c,j=i?h+a.startY-a.y:s()+c-f[1],l=B(j);i&&j!==l&&(h+=l-j),s(l)}(c||b)&&fV()},a.onEnable=function(){gc(o,!k&&"x"),f9.addEventListener("refresh",F),fr(d9,"resize",F),s.smooth&&(s.target.style.scrollBehavior="auto",s.smooth=t.smooth=!1),y.enable()},a.onDisable=function(){gc(o,!0),fs(d9,"resize",F),f9.removeEventListener("refresh",F),y.kill()},a.lockAxis=!1!==a.lockAxis,(b=new d6(a)).iOS=ex,ex&&!s()&&s(1),ex&&d7.ticker.add(eS),i=b._dc,f=d7.to(b,{ease:"power4",paused:!0,inherit:!1,scrollX:k?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:f6(s,s(),function(){return f.pause()})},onUpdate:fV,onComplete:i.vars.onComplete}),b};f9.sort=function(a){if(e1(a))return fA.sort(a);var b=d9.pageYOffset||0;return f9.getAll().forEach(function(a){return a._sortY=a.trigger?b+a.trigger.getBoundingClientRect().top:a.start+d9.innerHeight}),fA.sort(a||function(a,b){return -1e6*(a.vars.refreshPriority||0)+(a.vars.containerAnimation?1e6:a._sortY)-((b.vars.containerAnimation?1e6:b._sortY)+-1e6*(b.vars.refreshPriority||0))})},f9.observe=function(a){return new d6(a)},f9.normalizeScroll=function(a){if(void 0===a)return et;if(!0===a&&et)return et.enable();if(!1===a){et&&et.kill(),et=a;return}var b=a instanceof d6?a:gi(a);return et&&et.target===b.target&&et.kill(),eW(b.target)&&(et=b),b},f9.core={_getVelocityProp:d1,_inputObserver:gf,_scrollers:dL,_proxies:dM,bridge:{ss:function(){eM||fJ("scrollStart"),eM=eK()},ref:function(){return ej}}},eV()&&d7.registerPlugin(f9);let gj,gk,gl,gm="undefined"!=typeof Intl?new Intl.Segmenter:0,gn=a=>"string"==typeof a?gn(document.querySelectorAll(a)):"length"in a?Array.from(a):[a],go=a=>gn(a).filter(a=>a instanceof HTMLElement),gp=[],gq=function(){},gr=/\s+/g,gs=RegExp("\\p{RI}\\p{RI}|\\p{Emoji}(\\p{EMod}|\\u{FE0F}\\u{20E3}?|[\\u{E0020}-\\u{E007E}]+\\u{E007F})?(\\u{200D}\\p{Emoji}(\\p{EMod}|\\u{FE0F}\\u{20E3}?|[\\u{E0020}-\\u{E007E}]+\\u{E007F})?)*|.","gu"),gt={left:0,top:0,width:0,height:0},gu=(a,b)=>{if(b){let c=new Set(a.join("").match(b)||gp),d=a.length,e,f,g,h;if(c.size){for(;--d>-1;)for(g of(f=a[d],c))if(g.startsWith(f)&&g.length>f.length){for(e=0,h=f;g.startsWith(h+=a[d+ ++e])&&h.length<g.length;);if(e&&h.length===g.length){a[d]=g,a.splice(d+1,e);break}}}}return a},gv=a=>"inline"===window.getComputedStyle(a).display&&(a.style.display="inline-block"),gw=(a,b,c)=>b.insertBefore("string"==typeof a?document.createTextNode(a):a,c),gx=(a,b,c)=>{let d=b[a+"sClass"]||"",{tag:e="div",aria:f="auto",propIndex:g=!1}=b,h="line"===a?"block":"inline-block",i=d.indexOf("++")>-1,j=b=>{let j=document.createElement(e),k=c.length+1;return d&&(j.className=d+(i?" "+d+k:"")),g&&j.style.setProperty("--"+a,k+""),"none"!==f&&j.setAttribute("aria-hidden","true"),"span"!==e&&(j.style.position="relative",j.style.display=h),j.textContent=b,c.push(j),j};return i&&(d=d.replace("++","")),j.collection=c,j},gy=(a,b,c,d,e,f,g,h,i,j)=>{var k;let l=Array.from(a.childNodes),m=0,{wordDelimiter:n,reduceWhiteSpace:o=!0,prepareText:p}=b,q=a.getBoundingClientRect(),r=q,s=!o&&"pre"===window.getComputedStyle(a).whiteSpace.substring(0,3),t=0,u=c.collection,v,w,x,y,z,A,B,C,D,E,F,G,H,I,J,K,L,M;for("object"==typeof n?(x=n.delimiter||n,w=n.replaceWith||""):w=""===n?"":n||" ",v=" "!==w;m<l.length;m++)if(3===(y=l[m]).nodeType){for(J=y.textContent||"",o?J=J.replace(gr," "):s&&(J=J.replace(/\n/g,w+"\n")),p&&(J=p(J,a)),y.textContent=J,L=(z=w||x?J.split(x||w):J.match(h)||gp)[z.length-1],C=v?" "===L.slice(-1):!L,L||z.pop(),r=q,(B=v?" "===z[0].charAt(0):!z[0])&&gw(" ",a,y),z[0]||z.shift(),gu(z,i),f&&j||(y.textContent=""),D=1;D<=z.length;D++)if(K=z[D-1],!o&&s&&"\n"===K.charAt(0)&&(null==(k=y.previousSibling)||k.remove(),gw(document.createElement("br"),a,y),K=K.slice(1)),o||""!==K)if(" "===K)a.insertBefore(document.createTextNode(" "),y);else{if(v&&" "===K.charAt(0)&&gw(" ",a,y),t&&1===D&&!B&&u.indexOf(t.parentNode)>-1?(A=u[u.length-1]).appendChild(document.createTextNode(d?"":K)):(gw(A=c(d?"":K),a,y),t&&1===D&&!B&&A.insertBefore(t,A.firstChild)),d)for(M=0,F=gm?gu([...gm.segment(K)].map(a=>a.segment),i):K.match(h)||gp;M<F.length;M++)A.appendChild(" "===F[M]?document.createTextNode(" "):d(F[M]));if(f&&j){if(J=y.textContent=J.substring(K.length+1,J.length),(E=A.getBoundingClientRect()).top>r.top&&E.left<=r.left){for(G=a.cloneNode(),H=a.childNodes[0];H&&H!==A;)I=H,H=H.nextSibling,G.appendChild(I);a.parentNode.insertBefore(G,a),e&&gv(G)}r=E}(D<z.length||C)&&gw(D>=z.length?" ":v&&" "===K.slice(-1)?" "+w:w,a,y)}else gw(w,a,y);a.removeChild(y),t=0}else 1===y.nodeType&&(g&&g.indexOf(y)>-1?(u.indexOf(y.previousSibling)>-1&&u[u.length-1].appendChild(y),t=y):(gy(y,b,c,d,e,f,g,h,i,!0),t=0),e&&gv(y))},gz=class a{constructor(a,b){this.isSplit=!1,gl||gA.register(window.gsap),this.elements=go(a),this.chars=[],this.words=[],this.lines=[],this.masks=[],this.vars=b,this._split=()=>this.isSplit&&this.split(this.vars);let c=[],d,e=()=>{let a=c.length,b;for(;a--;){let d=(b=c[a]).element.offsetWidth;if(d!==b.width){b.width=d,this._split();return}}};this._data={orig:c,obs:"undefined"!=typeof ResizeObserver&&new ResizeObserver(()=>{clearTimeout(d),d=setTimeout(e,200)})},gq(this),this.split(b)}split(a){this.isSplit&&this.revert(),this.vars=a=a||this.vars||{};let{type:b="chars,words,lines",aria:c="auto",deepSlice:d=!0,smartWrap:e,onSplit:f,autoSplit:g=!1,specialChars:h,mask:i}=this.vars,j=b.indexOf("lines")>-1,k=b.indexOf("chars")>-1,l=b.indexOf("words")>-1,m=k&&!l&&!j,n=h&&("push"in h?RegExp("(?:"+h.join("|")+")","gu"):h),o=n?RegExp(n.source+"|"+gs.source,"gu"):gs,p=!!a.ignore&&go(a.ignore),{orig:q,animTime:r,obs:s}=this._data,t;return(k||l||j)&&(this.elements.forEach((b,f)=>{q[f]={element:b,html:b.innerHTML,ariaL:b.getAttribute("aria-label"),ariaH:b.getAttribute("aria-hidden")},"auto"===c?b.setAttribute("aria-label",(b.textContent||"").trim()):"hidden"===c&&b.setAttribute("aria-hidden","true");let g=[],h=[],i=[],r=k?gx("char",a,g):null,s=gx("word",a,h),t,u,v,w;if(gy(b,a,s,r,m,d&&(j||m),p,o,n,!1),j){let c,d,e=gn(b.childNodes),f=(c=gx("line",a,i),d=window.getComputedStyle(b).textAlign||"left",(a,f)=>{let g=c("");for(g.style.textAlign=d,b.insertBefore(g,e[a]);a<f;a++)g.appendChild(e[a]);g.normalize()}),g,h=[],j=0,k=e.map(a=>1===a.nodeType?a.getBoundingClientRect():gt),l=gt;for(t=0;t<e.length;t++)1===(g=e[t]).nodeType&&("BR"===g.nodeName?(h.push(g),f(j,t+1),l=k[j=t+1]):(t&&k[t].top>l.top&&k[t].left<=l.left&&(f(j,t),j=t),l=k[t]));j<t&&f(j,t),h.forEach(a=>{var b;return null==(b=a.parentNode)?void 0:b.removeChild(a)})}if(!l){for(t=0;t<h.length;t++)if(u=h[t],k||!u.nextSibling||3!==u.nextSibling.nodeType)if(e&&!j){for((v=document.createElement("span")).style.whiteSpace="nowrap";u.firstChild;)v.appendChild(u.firstChild);u.replaceWith(v)}else u.replaceWith(...u.childNodes);else(w=u.nextSibling)&&3===w.nodeType&&(w.textContent=(u.textContent||"")+(w.textContent||""),u.remove());h.length=0,b.normalize()}this.lines.push(...i),this.words.push(...h),this.chars.push(...g)}),i&&this[i]&&this.masks.push(...this[i].map(a=>{let b=a.cloneNode();return a.replaceWith(b),b.appendChild(a),a.className&&(b.className=a.className.replace(/(\b\w+\b)/g,"$1-mask")),b.style.overflow="clip",b}))),this.isSplit=!0,gk&&(g?gk.addEventListener("loadingdone",this._split):"loading"===gk.status&&console.warn("SplitText called before fonts loaded")),(t=f&&f(this))&&t.totalTime&&(this._data.anim=r?t.totalTime(r):t),j&&g&&this.elements.forEach((a,b)=>{q[b].width=a.offsetWidth,s&&s.observe(a)}),this}revert(){var a,b;let{orig:c,anim:d,obs:e}=this._data;return e&&e.disconnect(),c.forEach(({element:a,html:b,ariaL:c,ariaH:d})=>{a.innerHTML=b,c?a.setAttribute("aria-label",c):a.removeAttribute("aria-label"),d?a.setAttribute("aria-hidden",d):a.removeAttribute("aria-hidden")}),this.chars.length=this.words.length=this.lines.length=c.length=this.masks.length=0,this.isSplit=!1,null==gk||gk.removeEventListener("loadingdone",this._split),d&&(this._data.animTime=d.totalTime(),d.revert()),null==(b=(a=this.vars).onRevert)||b.call(a,this),this}static create(b,c){return new a(b,c)}static register(a){(gj=gj||a||window.gsap)&&(gn=gj.utils.toArray,gq=gj.core.context||gq),!gl&&window.innerWidth>0&&(gk=document.fonts,gl=!0)}};gz.version="3.13.0";let gA=gz;du.registerPlugin(f9,gA);let gB=({text:a,className:b="",delay:c=100,duration:d=.6,ease:e="power3.out",splitType:f="chars",from:i={opacity:0,y:40},to:j={opacity:1,y:0},threshold:k=.1,rootMargin:l="-100px",textAlign:m="center",onLetterAnimationComplete:n})=>{let o=(0,h.useRef)(null);return(0,h.useRef)(!1),(0,h.useRef)(null),(0,h.useEffect)(()=>{},[a,c,d,e,f,i,j,k,l,n]),(0,g.jsx)("p",{ref:o,className:`split-parent overflow-hidden inline-block whitespace-normal ${b}`,style:{textAlign:m,wordWrap:"break-word"},children:a})};function gC(a,b){-1===a.indexOf(b)&&a.push(b)}function gD(a,b){let c=a.indexOf(b);c>-1&&a.splice(c,1)}class gE{constructor(){this.subscriptions=[]}add(a){return gC(this.subscriptions,a),()=>gD(this.subscriptions,a)}notify(a,b,c){let d=this.subscriptions.length;if(d)if(1===d)this.subscriptions[0](a,b,c);else for(let e=0;e<d;e++){let d=this.subscriptions[e];d&&d(a,b,c)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function gF(a,b){return b?1e3/b*a:0}let gG={},gH=a=>a,gI=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],gJ={value:null,addProjectionMetrics:null};function gK(a,b){let c=!1,d=!0,e={delta:0,timestamp:0,isProcessing:!1},f=()=>c=!0,g=gI.reduce((a,c)=>(a[c]=function(a,b){let c=new Set,d=new Set,e=!1,f=!1,g=new WeakSet,h={delta:0,timestamp:0,isProcessing:!1},i=0;function j(b){g.has(b)&&(k.schedule(b),a()),i++,b(h)}let k={schedule:(a,b=!1,f=!1)=>{let h=f&&e?c:d;return b&&g.add(a),h.has(a)||h.add(a),a},cancel:a=>{d.delete(a),g.delete(a)},process:a=>{if(h=a,e){f=!0;return}e=!0,[c,d]=[d,c],c.forEach(j),b&&gJ.value&&gJ.value.frameloop[b].push(i),i=0,c.clear(),e=!1,f&&(f=!1,k.process(a))}};return k}(f,b?c:void 0),a),{}),{setup:h,read:i,resolveKeyframes:j,preUpdate:k,update:l,preRender:m,render:n,postRender:o}=g,p=()=>{let f=gG.useManualTiming?e.timestamp:performance.now();c=!1,gG.useManualTiming||(e.delta=d?1e3/60:Math.max(Math.min(f-e.timestamp,40),1)),e.timestamp=f,e.isProcessing=!0,h.process(e),i.process(e),j.process(e),k.process(e),l.process(e),m.process(e),n.process(e),o.process(e),e.isProcessing=!1,c&&b&&(d=!1,a(p))};return{schedule:gI.reduce((b,f)=>{let h=g[f];return b[f]=(b,f=!1,g=!1)=>(!c&&(c=!0,d=!0,e.isProcessing||a(p)),h.schedule(b,f,g)),b},{}),cancel:a=>{for(let b=0;b<gI.length;b++)g[gI[b]].cancel(a)},state:e,steps:g}}let{schedule:gL,cancel:gM,state:gN,steps:gO}=gK("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:gH,!0);function gP(){d=void 0}let gQ={now:()=>(void 0===d&&gQ.set(gN.isProcessing||gG.useManualTiming?gN.timestamp:performance.now()),d),set:a=>{d=a,queueMicrotask(gP)}},gR={current:void 0};class gS{constructor(a,b={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=a=>{let b=gQ.now();if(this.updatedAt!==b&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(a),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let a of this.dependents)a.dirty()},this.hasAnimated=!1,this.setCurrent(a),this.owner=b.owner}setCurrent(a){this.current=a,this.updatedAt=gQ.now(),null===this.canTrackVelocity&&void 0!==a&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(a=this.current){this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt}onChange(a){return this.on("change",a)}on(a,b){this.events[a]||(this.events[a]=new gE);let c=this.events[a].add(b);return"change"===a?()=>{c(),gL.read(()=>{this.events.change.getSize()||this.stop()})}:c}clearListeners(){for(let a in this.events)this.events[a].clear()}attach(a,b){this.passiveEffect=a,this.stopPassiveEffect=b}set(a){this.passiveEffect?this.passiveEffect(a,this.updateAndNotify):this.updateAndNotify(a)}setWithVelocity(a,b,c){this.set(b),this.prev=void 0,this.prevFrameValue=a,this.prevUpdatedAt=this.updatedAt-c}jump(a,b=!0){this.updateAndNotify(a),this.prev=a,this.prevUpdatedAt=this.prevFrameValue=void 0,b&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(a){this.dependents||(this.dependents=new Set),this.dependents.add(a)}removeDependent(a){this.dependents&&this.dependents.delete(a)}get(){return gR.current&&gR.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let a=gQ.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||a-this.updatedAt>30)return 0;let b=Math.min(this.updatedAt-this.prevUpdatedAt,30);return gF(parseFloat(this.current)-parseFloat(this.prevFrameValue),b)}start(a){return this.stop(),new Promise(b=>{this.hasAnimated=!0,this.animation=a(b),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function gT(a,b){return new gS(a,b)}let gU=(0,h.createContext)({transformPagePoint:a=>a,isStatic:!1,reducedMotion:"never"});function gV(a){let b=(0,h.useRef)(null);return null===b.current&&(b.current=a()),b.current}function gW(a){let b=gV(()=>gT(a)),{isStatic:c}=(0,h.useContext)(gU);if(c){let[,c]=(0,h.useState)(a);(0,h.useEffect)(()=>b.on("change",c),[])}return b}let gX=()=>{},gY=()=>{};function gZ(a,b){let c,d=()=>{let{currentTime:d}=b,e=(null===d?0:d.value)/100;c!==e&&a(e),c=e};return gL.preUpdate(d,!0),()=>gM(d)}function g$(a){let b;return()=>(void 0===b&&(b=a()),b)}let g_=g$(()=>void 0!==window.ScrollTimeline);function g0(a){return"object"==typeof a&&null!==a}function g1(a){return g0(a)&&"ownerSVGElement"in a}function g2(a,b,c){if(a instanceof EventTarget)return[a];if("string"==typeof a){let d=document;b&&(d=b.current);let e=c?.[a]??d.querySelectorAll(a);return e?Array.from(e):[]}return Array.from(a)}let g3=new WeakMap,g4=(a,b,c)=>(d,e)=>e&&e[0]?e[0][a+"Size"]:g1(d)&&"getBBox"in d?d.getBBox()[b]:d[c],g5=g4("inline","width","offsetWidth"),g6=g4("block","height","offsetHeight");function g7({target:a,borderBoxSize:b}){g3.get(a)?.forEach(c=>{c(a,{get width(){return g5(a,b)},get height(){return g6(a,b)}})})}function g8(a){a.forEach(g7)}let g9=new Set,ha=(a,b,c)=>{let d=b-a;return 0===d?1:(c-a)/d},hb=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),hc={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function hd(a,b,c,d){let e=c[b],{length:f,position:g}=hc[b],h=e.current,i=c.time;e.current=a[`scroll${g}`],e.scrollLength=a[`scroll${f}`]-a[`client${f}`],e.offset.length=0,e.offset[0]=0,e.offset[1]=e.scrollLength,e.progress=ha(0,e.scrollLength,e.current);let j=d-i;e.velocity=j>50?0:gF(e.current-h,j)}let he=(a,b)=>c=>b(a(c)),hf=(...a)=>a.reduce(he),hg=(a,b,c)=>c>b?b:c<a?a:c,hh=a=>b=>"string"==typeof b&&b.startsWith(a),hi=hh("--"),hj=hh("var(--"),hk=a=>!!hj(a)&&hl.test(a.split("/*")[0].trim()),hl=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,hm={test:a=>"number"==typeof a,parse:parseFloat,transform:a=>a},hn={...hm,transform:a=>hg(0,1,a)},ho={...hm,default:1},hp=a=>Math.round(1e5*a)/1e5,hq=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,hr=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,hs=(a,b)=>c=>!!("string"==typeof c&&hr.test(c)&&c.startsWith(a)||b&&null!=c&&Object.prototype.hasOwnProperty.call(c,b)),ht=(a,b,c)=>d=>{if("string"!=typeof d)return d;let[e,f,g,h]=d.match(hq);return{[a]:parseFloat(e),[b]:parseFloat(f),[c]:parseFloat(g),alpha:void 0!==h?parseFloat(h):1}},hu={...hm,transform:a=>Math.round(hg(0,255,a))},hv={test:hs("rgb","red"),parse:ht("red","green","blue"),transform:({red:a,green:b,blue:c,alpha:d=1})=>"rgba("+hu.transform(a)+", "+hu.transform(b)+", "+hu.transform(c)+", "+hp(hn.transform(d))+")"},hw={test:hs("#"),parse:function(a){let b="",c="",d="",e="";return a.length>5?(b=a.substring(1,3),c=a.substring(3,5),d=a.substring(5,7),e=a.substring(7,9)):(b=a.substring(1,2),c=a.substring(2,3),d=a.substring(3,4),e=a.substring(4,5),b+=b,c+=c,d+=d,e+=e),{red:parseInt(b,16),green:parseInt(c,16),blue:parseInt(d,16),alpha:e?parseInt(e,16)/255:1}},transform:hv.transform},hx=a=>({test:b=>"string"==typeof b&&b.endsWith(a)&&1===b.split(" ").length,parse:parseFloat,transform:b=>`${b}${a}`}),hy=hx("deg"),hz=hx("%"),hA=hx("px"),hB=hx("vh"),hC=hx("vw"),hD={...hz,parse:a=>hz.parse(a)/100,transform:a=>hz.transform(100*a)},hE={test:hs("hsl","hue"),parse:ht("hue","saturation","lightness"),transform:({hue:a,saturation:b,lightness:c,alpha:d=1})=>"hsla("+Math.round(a)+", "+hz.transform(hp(b))+", "+hz.transform(hp(c))+", "+hp(hn.transform(d))+")"},hF={test:a=>hv.test(a)||hw.test(a)||hE.test(a),parse:a=>hv.test(a)?hv.parse(a):hE.test(a)?hE.parse(a):hw.parse(a),transform:a=>"string"==typeof a?a:a.hasOwnProperty("red")?hv.transform(a):hE.transform(a),getAnimatableNone:a=>{let b=hF.parse(a);return b.alpha=0,hF.transform(b)}},hG=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,hH="number",hI="color",hJ=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function hK(a){let b=a.toString(),c=[],d={color:[],number:[],var:[]},e=[],f=0,g=b.replace(hJ,a=>(hF.test(a)?(d.color.push(f),e.push(hI),c.push(hF.parse(a))):a.startsWith("var(")?(d.var.push(f),e.push("var"),c.push(a)):(d.number.push(f),e.push(hH),c.push(parseFloat(a))),++f,"${}")).split("${}");return{values:c,split:g,indexes:d,types:e}}function hL(a){return hK(a).values}function hM(a){let{split:b,types:c}=hK(a),d=b.length;return a=>{let e="";for(let f=0;f<d;f++)if(e+=b[f],void 0!==a[f]){let b=c[f];b===hH?e+=hp(a[f]):b===hI?e+=hF.transform(a[f]):e+=a[f]}return e}}let hN=a=>"number"==typeof a?0:hF.test(a)?hF.getAnimatableNone(a):a,hO={test:function(a){return isNaN(a)&&"string"==typeof a&&(a.match(hq)?.length||0)+(a.match(hG)?.length||0)>0},parse:hL,createTransformer:hM,getAnimatableNone:function(a){let b=hL(a);return hM(a)(b.map(hN))}};function hP(a,b,c){return(c<0&&(c+=1),c>1&&(c-=1),c<1/6)?a+(b-a)*6*c:c<.5?b:c<2/3?a+(b-a)*(2/3-c)*6:a}function hQ(a,b){return c=>c>0?b:a}let hR=(a,b,c)=>a+(b-a)*c,hS=(a,b,c)=>{let d=a*a,e=c*(b*b-d)+d;return e<0?0:Math.sqrt(e)},hT=[hw,hv,hE];function hU(a){let b=hT.find(b=>b.test(a));if(gX(!!b,`'${a}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!b)return!1;let c=b.parse(a);return b===hE&&(c=function({hue:a,saturation:b,lightness:c,alpha:d}){a/=360,c/=100;let e=0,f=0,g=0;if(b/=100){let d=c<.5?c*(1+b):c+b-c*b,h=2*c-d;e=hP(h,d,a+1/3),f=hP(h,d,a),g=hP(h,d,a-1/3)}else e=f=g=c;return{red:Math.round(255*e),green:Math.round(255*f),blue:Math.round(255*g),alpha:d}}(c)),c}let hV=(a,b)=>{let c=hU(a),d=hU(b);if(!c||!d)return hQ(a,b);let e={...c};return a=>(e.red=hS(c.red,d.red,a),e.green=hS(c.green,d.green,a),e.blue=hS(c.blue,d.blue,a),e.alpha=hR(c.alpha,d.alpha,a),hv.transform(e))},hW=new Set(["none","hidden"]);function hX(a,b){return c=>hR(a,b,c)}function hY(a){return"number"==typeof a?hX:"string"==typeof a?hk(a)?hQ:hF.test(a)?hV:h_:Array.isArray(a)?hZ:"object"==typeof a?hF.test(a)?hV:h$:hQ}function hZ(a,b){let c=[...a],d=c.length,e=a.map((a,c)=>hY(a)(a,b[c]));return a=>{for(let b=0;b<d;b++)c[b]=e[b](a);return c}}function h$(a,b){let c={...a,...b},d={};for(let e in c)void 0!==a[e]&&void 0!==b[e]&&(d[e]=hY(a[e])(a[e],b[e]));return a=>{for(let b in d)c[b]=d[b](a);return c}}let h_=(a,b)=>{let c=hO.createTransformer(b),d=hK(a),e=hK(b);return d.indexes.var.length===e.indexes.var.length&&d.indexes.color.length===e.indexes.color.length&&d.indexes.number.length>=e.indexes.number.length?hW.has(a)&&!e.values.length||hW.has(b)&&!d.values.length?function(a,b){return hW.has(a)?c=>c<=0?a:b:c=>c>=1?b:a}(a,b):hf(hZ(function(a,b){let c=[],d={color:0,var:0,number:0};for(let e=0;e<b.values.length;e++){let f=b.types[e],g=a.indexes[f][d[f]],h=a.values[g]??0;c[e]=h,d[f]++}return c}(d,e),e.values),c):(gX(!0,`Complex values '${a}' and '${b}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),hQ(a,b))};function h0(a,b,c){return"number"==typeof a&&"number"==typeof b&&"number"==typeof c?hR(a,b,c):hY(a)(a,b)}function h1(a,b,{clamp:c=!0,ease:d,mixer:e}={}){let f=a.length;if(gY(f===b.length,"Both input and output ranges must be the same length","range-length"),1===f)return()=>b[0];if(2===f&&b[0]===b[1])return()=>b[1];let g=a[0]===a[1];a[0]>a[f-1]&&(a=[...a].reverse(),b=[...b].reverse());let h=function(a,b,c){let d=[],e=c||gG.mix||h0,f=a.length-1;for(let c=0;c<f;c++){let f=e(a[c],a[c+1]);b&&(f=hf(Array.isArray(b)?b[c]||gH:b,f)),d.push(f)}return d}(b,d,e),i=h.length,j=c=>{if(g&&c<a[0])return b[0];let d=0;if(i>1)for(;d<a.length-2&&!(c<a[d+1]);d++);let e=ha(a[d],a[d+1],c);return h[d](e)};return c?b=>j(hg(a[0],a[f-1],b)):j}function h2(a){let b=[0];return!function(a,b){let c=a[a.length-1];for(let d=1;d<=b;d++){let e=ha(0,b,d);a.push(hR(c,1,e))}}(b,a.length-1),b}function h3(a){return g0(a)&&"offsetHeight"in a}let h4={start:0,center:.5,end:1};function h5(a,b,c=0){let d=0;if(a in h4&&(a=h4[a]),"string"==typeof a){let b=parseFloat(a);a.endsWith("px")?d=b:a.endsWith("%")?a=b/100:a.endsWith("vw")?d=b/100*document.documentElement.clientWidth:a.endsWith("vh")?d=b/100*document.documentElement.clientHeight:a=b}return"number"==typeof a&&(d=b*a),c+d}let h6=[0,0],h7={All:[[0,0],[1,1]]},h8={x:0,y:0},h9=new WeakMap,ia=new WeakMap,ib=new WeakMap,ic=a=>a===document.scrollingElement?window:a;function id(a,{container:b=document.scrollingElement,...c}={}){if(!b)return gH;let d=ib.get(b);d||(d=new Set,ib.set(b,d));let g=function(a,b,c,d={}){return{measure:b=>{!function(a,b=a,c){if(c.x.targetOffset=0,c.y.targetOffset=0,b!==a){let d=b;for(;d&&d!==a;)c.x.targetOffset+=d.offsetLeft,c.y.targetOffset+=d.offsetTop,d=d.offsetParent}c.x.targetLength=b===a?b.scrollWidth:b.clientWidth,c.y.targetLength=b===a?b.scrollHeight:b.clientHeight,c.x.containerLength=a.clientWidth,c.y.containerLength=a.clientHeight}(a,d.target,c),hd(a,"x",c,b),hd(a,"y",c,b),c.time=b,(d.offset||d.target)&&function(a,b,c){let{offset:d=h7.All}=c,{target:e=a,axis:f="y"}=c,g="y"===f?"height":"width",h=e!==a?function(a,b){let c={x:0,y:0},d=a;for(;d&&d!==b;)if(h3(d))c.x+=d.offsetLeft,c.y+=d.offsetTop,d=d.offsetParent;else if("svg"===d.tagName){let a=d.getBoundingClientRect(),b=(d=d.parentElement).getBoundingClientRect();c.x+=a.left-b.left,c.y+=a.top-b.top}else if(d instanceof SVGGraphicsElement){let{x:a,y:b}=d.getBBox();c.x+=a,c.y+=b;let e=null,f=d.parentNode;for(;!e;)"svg"===f.tagName&&(e=f),f=d.parentNode;d=e}else break;return c}(e,a):h8,i=e===a?{width:a.scrollWidth,height:a.scrollHeight}:"getBBox"in e&&"svg"!==e.tagName?e.getBBox():{width:e.clientWidth,height:e.clientHeight},j={width:a.clientWidth,height:a.clientHeight};b[f].offset.length=0;let k=!b[f].interpolate,l=d.length;for(let a=0;a<l;a++){let c=function(a,b,c,d){let e=Array.isArray(a)?a:h6,f=0;return"number"==typeof a?e=[a,a]:"string"==typeof a&&(e=(a=a.trim()).includes(" ")?a.split(" "):[a,h4[a]?a:"0"]),(f=h5(e[0],c,d))-h5(e[1],b)}(d[a],j[g],i[g],h[f]);k||c===b[f].interpolatorOffsets[a]||(k=!0),b[f].offset[a]=c}k&&(b[f].interpolate=h1(b[f].offset,h2(d),{clamp:!1}),b[f].interpolatorOffsets=[...b[f].offset]),b[f].progress=hg(0,1,b[f].interpolate(b[f].current))}(a,c,d)},notify:()=>b(c)}}(b,a,{time:0,x:hb(),y:hb()},c);if(d.add(g),!h9.has(b)){let a=()=>{for(let a of d)a.measure(gN.timestamp);gL.preUpdate(c)},c=()=>{for(let a of d)a.notify()},g=()=>gL.read(a);h9.set(b,g);let h=ic(b);window.addEventListener("resize",g,{passive:!0}),b!==document.documentElement&&ia.set(b,"function"==typeof b?(g9.add(b),f||(f=()=>{let a={get width(){return window.innerWidth},get height(){return window.innerHeight}};g9.forEach(b=>b(a))},window.addEventListener("resize",f)),()=>{g9.delete(b),g9.size||"function"!=typeof f||(window.removeEventListener("resize",f),f=void 0)}):function(a,b){e||"undefined"!=typeof ResizeObserver&&(e=new ResizeObserver(g8));let c=g2(a);return c.forEach(a=>{let c=g3.get(a);c||(c=new Set,g3.set(a,c)),c.add(b),e?.observe(a)}),()=>{c.forEach(a=>{let c=g3.get(a);c?.delete(b),c?.size||e?.unobserve(a)})}}(b,g)),h.addEventListener("scroll",g,{passive:!0}),g()}let h=h9.get(b);return gL.read(h,!1,!0),()=>{gM(h);let a=ib.get(b);if(!a||(a.delete(g),a.size))return;let c=h9.get(b);h9.delete(b),c&&(ic(b).removeEventListener("scroll",c),ia.get(b)?.(),window.removeEventListener("resize",c))}}let ie=new Map;function ig({source:a,container:b,...c}){let{axis:d}=c;a&&(b=a);let e=ie.get(b)??new Map;ie.set(b,e);let f=c.target??"self",g=e.get(f)??{},h=d+(c.offset??[]).join(",");return g[h]||(g[h]=!c.target&&g_()?new ScrollTimeline({source:b,axis:d}):function(a){let b={value:0},c=id(c=>{b.value=100*c[a.axis].progress},a);return{currentTime:b,cancel:c}}({container:b,...c})),g[h]}let ih="undefined"!=typeof window,ii=ih?h.useLayoutEffect:h.useEffect,ij=()=>({scrollX:gT(0),scrollY:gT(0),scrollXProgress:gT(0),scrollYProgress:gT(0)}),ik=a=>!!a&&!a.current,il=a=>!!(a&&a.getVelocity),im=a=>1e3*a,io={layout:0,mainThread:0,waapi:0},ip=a=>{let b=({timestamp:b})=>a(b);return{start:(a=!0)=>gL.update(b,a),stop:()=>gM(b),now:()=>gN.isProcessing?gN.timestamp:gQ.now()}},iq=(a,b,c=10)=>{let d="",e=Math.max(Math.round(b/c),2);for(let b=0;b<e;b++)d+=Math.round(1e4*a(b/(e-1)))/1e4+", ";return`linear(${d.substring(0,d.length-2)})`};function ir(a){let b=0,c=a.next(b);for(;!c.done&&b<2e4;)b+=50,c=a.next(b);return b>=2e4?1/0:b}function is(a,b,c){let d=Math.max(b-5,0);return gF(c-a(d),b-d)}let it={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function iu(a,b){return a*Math.sqrt(1-b*b)}let iv=["duration","bounce"],iw=["stiffness","damping","mass"];function ix(a,b){return b.some(b=>void 0!==a[b])}function iy(a=it.visualDuration,b=it.bounce){let c,d="object"!=typeof a?{visualDuration:a,keyframes:[0,1],bounce:b}:a,{restSpeed:e,restDelta:f}=d,g=d.keyframes[0],h=d.keyframes[d.keyframes.length-1],i={done:!1,value:g},{stiffness:j,damping:k,mass:l,duration:m,velocity:n,isResolvedFromDuration:o}=function(a){let b={velocity:it.velocity,stiffness:it.stiffness,damping:it.damping,mass:it.mass,isResolvedFromDuration:!1,...a};if(!ix(a,iw)&&ix(a,iv))if(a.visualDuration){let c=2*Math.PI/(1.2*a.visualDuration),d=c*c,e=2*hg(.05,1,1-(a.bounce||0))*Math.sqrt(d);b={...b,mass:it.mass,stiffness:d,damping:e}}else{let c=function({duration:a=it.duration,bounce:b=it.bounce,velocity:c=it.velocity,mass:d=it.mass}){let e,f;gX(a<=im(it.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let g=1-b;g=hg(it.minDamping,it.maxDamping,g),a=hg(it.minDuration,it.maxDuration,a/1e3),g<1?(e=b=>{let d=b*g,e=d*a;return .001-(d-c)/iu(b,g)*Math.exp(-e)},f=b=>{let d=b*g*a,f=Math.pow(g,2)*Math.pow(b,2)*a,h=Math.exp(-d),i=iu(Math.pow(b,2),g);return(d*c+c-f)*h*(-e(b)+.001>0?-1:1)/i}):(e=b=>-.001+Math.exp(-b*a)*((b-c)*a+1),f=b=>a*a*(c-b)*Math.exp(-b*a));let h=function(a,b,c){let d=c;for(let c=1;c<12;c++)d-=a(d)/b(d);return d}(e,f,5/a);if(a=im(a),isNaN(h))return{stiffness:it.stiffness,damping:it.damping,duration:a};{let b=Math.pow(h,2)*d;return{stiffness:b,damping:2*g*Math.sqrt(d*b),duration:a}}}(a);(b={...b,...c,mass:it.mass}).isResolvedFromDuration=!0}return b}({...d,velocity:-((d.velocity||0)/1e3)}),p=n||0,q=k/(2*Math.sqrt(j*l)),r=h-g,s=Math.sqrt(j/l)/1e3,t=5>Math.abs(r);if(e||(e=t?it.restSpeed.granular:it.restSpeed.default),f||(f=t?it.restDelta.granular:it.restDelta.default),q<1){let a=iu(s,q);c=b=>h-Math.exp(-q*s*b)*((p+q*s*r)/a*Math.sin(a*b)+r*Math.cos(a*b))}else if(1===q)c=a=>h-Math.exp(-s*a)*(r+(p+s*r)*a);else{let a=s*Math.sqrt(q*q-1);c=b=>{let c=Math.exp(-q*s*b),d=Math.min(a*b,300);return h-c*((p+q*s*r)*Math.sinh(d)+a*r*Math.cosh(d))/a}}let u={calculatedDuration:o&&m||null,next:a=>{let b=c(a);if(o)i.done=a>=m;else{let d=0===a?p:0;q<1&&(d=0===a?im(p):is(c,a,b));let g=Math.abs(h-b)<=f;i.done=Math.abs(d)<=e&&g}return i.value=i.done?h:b,i},toString:()=>{let a=Math.min(ir(u),2e4),b=iq(b=>u.next(a*b).value,a,30);return a+"ms "+b},toTransition:()=>{}};return u}function iz({keyframes:a,velocity:b=0,power:c=.8,timeConstant:d=325,bounceDamping:e=10,bounceStiffness:f=500,modifyTarget:g,min:h,max:i,restDelta:j=.5,restSpeed:k}){let l,m,n=a[0],o={done:!1,value:n},p=c*b,q=n+p,r=void 0===g?q:g(q);r!==q&&(p=r-n);let s=a=>-p*Math.exp(-a/d),t=a=>r+s(a),u=a=>{let b=s(a),c=t(a);o.done=Math.abs(b)<=j,o.value=o.done?r:c},v=a=>{let b;if(b=o.value,void 0!==h&&b<h||void 0!==i&&b>i){var c;l=a,m=iy({keyframes:[o.value,(c=o.value,void 0===h?i:void 0===i||Math.abs(h-c)<Math.abs(i-c)?h:i)],velocity:is(t,a,o.value),damping:e,stiffness:f,restDelta:j,restSpeed:k})}};return v(0),{calculatedDuration:null,next:a=>{let b=!1;return(m||void 0!==l||(b=!0,u(a),v(a)),void 0!==l&&a>=l)?m.next(a-l):(b||u(a),o)}}}iy.applyToOptions=a=>{let b=function(a,b=100,c){let d=c({...a,keyframes:[0,b]}),e=Math.min(ir(d),2e4);return{type:"keyframes",ease:a=>d.next(e*a).value/b,duration:e/1e3}}(a,100,iy);return a.ease=b.ease,a.duration=im(b.duration),a.type="keyframes",a};let iA=(a,b,c)=>(((1-3*c+3*b)*a+(3*c-6*b))*a+3*b)*a;function iB(a,b,c,d){return a===b&&c===d?gH:e=>0===e||1===e?e:iA(function(a,b,c,d,e){let f,g,h=0;do(f=iA(g=b+(c-b)/2,d,e)-a)>0?c=g:b=g;while(Math.abs(f)>1e-7&&++h<12);return g}(e,0,1,a,c),b,d)}let iC=iB(.42,0,1,1),iD=iB(0,0,.58,1),iE=iB(.42,0,.58,1),iF=a=>b=>b<=.5?a(2*b)/2:(2-a(2*(1-b)))/2,iG=a=>b=>1-a(1-b),iH=iB(.33,1.53,.69,.99),iI=iG(iH),iJ=iF(iI),iK=a=>(a*=2)<1?.5*iI(a):.5*(2-Math.pow(2,-10*(a-1))),iL=a=>1-Math.sin(Math.acos(a)),iM=iG(iL),iN=iF(iL),iO=a=>Array.isArray(a)&&"number"==typeof a[0],iP={linear:gH,easeIn:iC,easeInOut:iE,easeOut:iD,circIn:iL,circInOut:iN,circOut:iM,backIn:iI,backInOut:iJ,backOut:iH,anticipate:iK},iQ=a=>{if(iO(a)){gY(4===a.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[b,c,d,e]=a;return iB(b,c,d,e)}return"string"==typeof a?(gY(void 0!==iP[a],`Invalid easing type '${a}'`,"invalid-easing-type"),iP[a]):a};function iR({duration:a=300,keyframes:b,times:c,ease:d="easeInOut"}){var e;let f=Array.isArray(d)&&"number"!=typeof d[0]?d.map(iQ):iQ(d),g={done:!1,value:b[0]},h=h1((e=c&&c.length===b.length?c:h2(b),e.map(b=>b*a)),b,{ease:Array.isArray(f)?f:b.map(()=>f||iE).splice(0,b.length-1)});return{calculatedDuration:a,next:b=>(g.value=h(b),g.done=b>=a,g)}}let iS=a=>null!==a;function iT(a,{repeat:b,repeatType:c="loop"},d,e=1){let f=a.filter(iS),g=e<0||b&&"loop"!==c&&b%2==1?0:f.length-1;return g&&void 0!==d?d:f[g]}let iU={decay:iz,inertia:iz,tween:iR,keyframes:iR,spring:iy};function iV(a){"string"==typeof a.type&&(a.type=iU[a.type])}class iW{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(a=>{this.resolve=a})}notifyFinished(){this.resolve()}then(a,b){return this.finished.then(a,b)}}let iX=a=>a/100;class iY extends iW{constructor(a){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:a}=this.options;a&&a.updatedAt!==gQ.now()&&this.tick(gQ.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},io.mainThread++,this.options=a,this.initAnimation(),this.play(),!1===a.autoplay&&this.pause()}initAnimation(){let{options:a}=this;iV(a);let{type:b=iR,repeat:c=0,repeatDelay:d=0,repeatType:e,velocity:f=0}=a,{keyframes:g}=a,h=b||iR;h!==iR&&"number"!=typeof g[0]&&(this.mixKeyframes=hf(iX,h0(g[0],g[1])),g=[0,100]);let i=h({...a,keyframes:g});"mirror"===e&&(this.mirroredGenerator=h({...a,keyframes:[...g].reverse(),velocity:-f})),null===i.calculatedDuration&&(i.calculatedDuration=ir(i));let{calculatedDuration:j}=i;this.calculatedDuration=j,this.resolvedDuration=j+d,this.totalDuration=this.resolvedDuration*(c+1)-d,this.generator=i}updateTime(a){let b=Math.round(a-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=b}tick(a,b=!1){let{generator:c,totalDuration:d,mixKeyframes:e,mirroredGenerator:f,resolvedDuration:g,calculatedDuration:h}=this;if(null===this.startTime)return c.next(0);let{delay:i=0,keyframes:j,repeat:k,repeatType:l,repeatDelay:m,type:n,onUpdate:o,finalKeyframe:p}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,a):this.speed<0&&(this.startTime=Math.min(a-d/this.speed,this.startTime)),b?this.currentTime=a:this.updateTime(a);let q=this.currentTime-i*(this.playbackSpeed>=0?1:-1),r=this.playbackSpeed>=0?q<0:q>d;this.currentTime=Math.max(q,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=d);let s=this.currentTime,t=c;if(k){let a=Math.min(this.currentTime,d)/g,b=Math.floor(a),c=a%1;!c&&a>=1&&(c=1),1===c&&b--,(b=Math.min(b,k+1))%2&&("reverse"===l?(c=1-c,m&&(c-=m/g)):"mirror"===l&&(t=f)),s=hg(0,1,c)*g}let u=r?{done:!1,value:j[0]}:t.next(s);e&&(u.value=e(u.value));let{done:v}=u;r||null===h||(v=this.playbackSpeed>=0?this.currentTime>=d:this.currentTime<=0);let w=null===this.holdTime&&("finished"===this.state||"running"===this.state&&v);return w&&n!==iz&&(u.value=iT(j,this.options,p,this.speed)),o&&o(u.value),w&&this.finish(),u}then(a,b){return this.finished.then(a,b)}get duration(){return this.calculatedDuration/1e3}get time(){return this.currentTime/1e3}set time(a){a=im(a),this.currentTime=a,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=a:this.driver&&(this.startTime=this.driver.now()-a/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(a){this.updateTime(gQ.now());let b=this.playbackSpeed!==a;this.playbackSpeed=a,b&&(this.time=this.currentTime/1e3)}play(){if(this.isStopped)return;let{driver:a=ip,startTime:b}=this.options;this.driver||(this.driver=a(a=>this.tick(a))),this.options.onPlay?.();let c=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=c):null!==this.holdTime?this.startTime=c-this.holdTime:this.startTime||(this.startTime=b??c),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(gQ.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,io.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(a){return this.startTime=0,this.tick(a,!0)}attachTimeline(a){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),a.observe(this)}}function iZ(a){return"number"==typeof a?a:parseFloat(a)}function i$(a,b){let c=gW(b()),d=()=>c.set(b());return d(),ii(()=>{let b=()=>gL.preRender(d,!1,!0),c=a.map(a=>a.on("change",b));return()=>{c.forEach(a=>a()),gM(d)}}),c}function i_(a,b,c,d){if("function"==typeof a){gR.current=[],a();let b=i$(gR.current,a);return gR.current=void 0,b}let e="function"==typeof b?b:function(...a){let b=!Array.isArray(a[0]),c=b?0:-1,d=a[0+c],e=a[1+c],f=h1(e,a[2+c],a[3+c]);return b?f(d):f}(b,c,d);return Array.isArray(a)?i0(a,e):i0([a],([a])=>e(a))}function i0(a,b){let c=gV(()=>[]);return i$(a,()=>{c.length=0;let d=a.length;for(let b=0;b<d;b++)c[b]=a[b].get();return b(c)})}function i1(a,b={}){let{isStatic:c}=(0,h.useContext)(gU),d=()=>il(a)?a.get():a;if(c)return i_(d);let e=gW(d());return(0,h.useInsertionEffect)(()=>(function(a,b,c){let d,e=a.get(),f=null,g=e,h="string"==typeof e?e.replace(/[\d.-]/g,""):void 0,i=()=>{f&&(f.stop(),f=null)},j=()=>{i(),f=new iY({keyframes:[iZ(a.get()),iZ(g)],velocity:a.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...c,onUpdate:d})};if(a.attach((b,c)=>(g=b,d=a=>{var b,d;return c((b=a,(d=h)?b+d:b))},gL.postRender(j),a.get()),i),il(b)){let c=b.on("change",b=>{var c,d;return a.set((c=b,(d=h)?c+d:c))}),d=a.on("destroy",c);return()=>{c(),d()}}return i})(e,a,b),[e,JSON.stringify(b)]),e}let i2=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],i3=new Set(i2),i4=a=>180*a/Math.PI,i5=a=>i7(i4(Math.atan2(a[1],a[0]))),i6={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:a=>(Math.abs(a[0])+Math.abs(a[3]))/2,rotate:i5,rotateZ:i5,skewX:a=>i4(Math.atan(a[1])),skewY:a=>i4(Math.atan(a[2])),skew:a=>(Math.abs(a[1])+Math.abs(a[2]))/2},i7=a=>((a%=360)<0&&(a+=360),a),i8=a=>Math.sqrt(a[0]*a[0]+a[1]*a[1]),i9=a=>Math.sqrt(a[4]*a[4]+a[5]*a[5]),ja={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:i8,scaleY:i9,scale:a=>(i8(a)+i9(a))/2,rotateX:a=>i7(i4(Math.atan2(a[6],a[5]))),rotateY:a=>i7(i4(Math.atan2(-a[2],a[0]))),rotateZ:i5,rotate:i5,skewX:a=>i4(Math.atan(a[4])),skewY:a=>i4(Math.atan(a[1])),skew:a=>(Math.abs(a[1])+Math.abs(a[4]))/2};function jb(a){return+!!a.includes("scale")}function jc(a,b){let c,d;if(!a||"none"===a)return jb(b);let e=a.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(e)c=ja,d=e;else{let b=a.match(/^matrix\(([-\d.e\s,]+)\)$/u);c=i6,d=b}if(!d)return jb(b);let f=c[b],g=d[1].split(",").map(jd);return"function"==typeof f?f(g):g[f]}function jd(a){return parseFloat(a.trim())}function je({top:a,left:b,right:c,bottom:d}){return{x:{min:b,max:c},y:{min:a,max:d}}}function jf(a){return void 0===a||1===a}function jg({scale:a,scaleX:b,scaleY:c}){return!jf(a)||!jf(b)||!jf(c)}function jh(a){return jg(a)||ji(a)||a.z||a.rotate||a.rotateX||a.rotateY||a.skewX||a.skewY}function ji(a){var b,c;return(b=a.x)&&"0%"!==b||(c=a.y)&&"0%"!==c}function jj(a,b,c,d,e){return void 0!==e&&(a=d+e*(a-d)),d+c*(a-d)+b}function jk(a,b=0,c=1,d,e){a.min=jj(a.min,b,c,d,e),a.max=jj(a.max,b,c,d,e)}function jl(a,{x:b,y:c}){jk(a.x,b.translate,b.scale,b.originPoint),jk(a.y,c.translate,c.scale,c.originPoint)}function jm(a,b){a.min=a.min+b,a.max=a.max+b}function jn(a,b,c,d,e=.5){let f=hR(a.min,a.max,e);jk(a,b,c,f,d)}function jo(a,b){jn(a.x,b.x,b.scaleX,b.scale,b.originX),jn(a.y,b.y,b.scaleY,b.scale,b.originY)}function jp(a,b){return je(function(a,b){if(!b)return a;let c=b({x:a.left,y:a.top}),d=b({x:a.right,y:a.bottom});return{top:c.y,left:c.x,bottom:d.y,right:d.x}}(a.getBoundingClientRect(),b))}let jq=new Set(["width","height","top","left","right","bottom",...i2]),jr=a=>b=>b.test(a),js=[hm,hA,hz,hy,hC,hB,{test:a=>"auto"===a,parse:a=>a}],jt=a=>js.find(jr(a)),ju=a=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(a),jv=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,jw=a=>a===hm||a===hA,jx=new Set(["x","y","z"]),jy=i2.filter(a=>!jx.has(a)),jz={width:({x:a},{paddingLeft:b="0",paddingRight:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),height:({y:a},{paddingTop:b="0",paddingBottom:c="0"})=>a.max-a.min-parseFloat(b)-parseFloat(c),top:(a,{top:b})=>parseFloat(b),left:(a,{left:b})=>parseFloat(b),bottom:({y:a},{top:b})=>parseFloat(b)+(a.max-a.min),right:({x:a},{left:b})=>parseFloat(b)+(a.max-a.min),x:(a,{transform:b})=>jc(b,"x"),y:(a,{transform:b})=>jc(b,"y")};jz.translateX=jz.x,jz.translateY=jz.y;let jA=new Set,jB=!1,jC=!1,jD=!1;function jE(){if(jC){let a=Array.from(jA).filter(a=>a.needsMeasurement),b=new Set(a.map(a=>a.element)),c=new Map;b.forEach(a=>{let b=function(a){let b=[];return jy.forEach(c=>{let d=a.getValue(c);void 0!==d&&(b.push([c,d.get()]),d.set(+!!c.startsWith("scale")))}),b}(a);b.length&&(c.set(a,b),a.render())}),a.forEach(a=>a.measureInitialState()),b.forEach(a=>{a.render();let b=c.get(a);b&&b.forEach(([b,c])=>{a.getValue(b)?.set(c)})}),a.forEach(a=>a.measureEndState()),a.forEach(a=>{void 0!==a.suspendedScrollY&&window.scrollTo(0,a.suspendedScrollY)})}jC=!1,jB=!1,jA.forEach(a=>a.complete(jD)),jA.clear()}function jF(){jA.forEach(a=>{a.readKeyframes(),a.needsMeasurement&&(jC=!0)})}class jG{constructor(a,b,c,d,e,f=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...a],this.onComplete=b,this.name=c,this.motionValue=d,this.element=e,this.isAsync=f}scheduleResolve(){this.state="scheduled",this.isAsync?(jA.add(this),jB||(jB=!0,gL.read(jF),gL.resolveKeyframes(jE))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:a,name:b,element:c,motionValue:d}=this;if(null===a[0]){let e=d?.get(),f=a[a.length-1];if(void 0!==e)a[0]=e;else if(c&&b){let d=c.readValue(b,f);null!=d&&(a[0]=d)}void 0===a[0]&&(a[0]=f),d&&void 0===e&&d.set(a[0])}for(let b=1;b<a.length;b++)a[b]??(a[b]=a[b-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(a=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,a),jA.delete(this)}cancel(){"scheduled"===this.state&&(jA.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let jH=a=>/^0[^.\s]+$/u.test(a),jI=new Set(["brightness","contrast","saturate","opacity"]);function jJ(a){let[b,c]=a.slice(0,-1).split("(");if("drop-shadow"===b)return a;let[d]=c.match(hq)||[];if(!d)return a;let e=c.replace(d,""),f=+!!jI.has(b);return d!==c&&(f*=100),b+"("+f+e+")"}let jK=/\b([a-z-]*)\(.*?\)/gu,jL={...hO,getAnimatableNone:a=>{let b=a.match(jK);return b?b.map(jJ).join(" "):a}},jM={...hm,transform:Math.round},jN={borderWidth:hA,borderTopWidth:hA,borderRightWidth:hA,borderBottomWidth:hA,borderLeftWidth:hA,borderRadius:hA,radius:hA,borderTopLeftRadius:hA,borderTopRightRadius:hA,borderBottomRightRadius:hA,borderBottomLeftRadius:hA,width:hA,maxWidth:hA,height:hA,maxHeight:hA,top:hA,right:hA,bottom:hA,left:hA,padding:hA,paddingTop:hA,paddingRight:hA,paddingBottom:hA,paddingLeft:hA,margin:hA,marginTop:hA,marginRight:hA,marginBottom:hA,marginLeft:hA,backgroundPositionX:hA,backgroundPositionY:hA,rotate:hy,rotateX:hy,rotateY:hy,rotateZ:hy,scale:ho,scaleX:ho,scaleY:ho,scaleZ:ho,skew:hy,skewX:hy,skewY:hy,distance:hA,translateX:hA,translateY:hA,translateZ:hA,x:hA,y:hA,z:hA,perspective:hA,transformPerspective:hA,opacity:hn,originX:hD,originY:hD,originZ:hA,zIndex:jM,fillOpacity:hn,strokeOpacity:hn,numOctaves:jM},jO={...jN,color:hF,backgroundColor:hF,outlineColor:hF,fill:hF,stroke:hF,borderColor:hF,borderTopColor:hF,borderRightColor:hF,borderBottomColor:hF,borderLeftColor:hF,filter:jL,WebkitFilter:jL},jP=a=>jO[a];function jQ(a,b){let c=jP(a);return c!==jL&&(c=hO),c.getAnimatableNone?c.getAnimatableNone(b):void 0}let jR=new Set(["auto","none","0"]);class jS extends jG{constructor(a,b,c,d,e){super(a,b,c,d,e,!0)}readKeyframes(){let{unresolvedKeyframes:a,element:b,name:c}=this;if(!b||!b.current)return;super.readKeyframes();for(let c=0;c<a.length;c++){let d=a[c];if("string"==typeof d&&hk(d=d.trim())){let e=function a(b,c,d=1){gY(d<=4,`Max CSS variable fallback depth detected in property "${b}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[e,f]=function(a){let b=jv.exec(a);if(!b)return[,];let[,c,d,e]=b;return[`--${c??d}`,e]}(b);if(!e)return;let g=window.getComputedStyle(c).getPropertyValue(e);if(g){let a=g.trim();return ju(a)?parseFloat(a):a}return hk(f)?a(f,c,d+1):f}(d,b.current);void 0!==e&&(a[c]=e),c===a.length-1&&(this.finalKeyframe=d)}}if(this.resolveNoneKeyframes(),!jq.has(c)||2!==a.length)return;let[d,e]=a,f=jt(d),g=jt(e);if(f!==g)if(jw(f)&&jw(g))for(let b=0;b<a.length;b++){let c=a[b];"string"==typeof c&&(a[b]=parseFloat(c))}else jz[c]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:a,name:b}=this,c=[];for(let b=0;b<a.length;b++){var d;(null===a[b]||("number"==typeof(d=a[b])?0===d:null===d||"none"===d||"0"===d||jH(d)))&&c.push(b)}c.length&&function(a,b,c){let d,e=0;for(;e<a.length&&!d;){let b=a[e];"string"==typeof b&&!jR.has(b)&&hK(b).values.length&&(d=a[e]),e++}if(d&&c)for(let e of b)a[e]=jQ(c,d)}(a,c,b)}measureInitialState(){let{element:a,unresolvedKeyframes:b,name:c}=this;if(!a||!a.current)return;"height"===c&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=jz[c](a.measureViewportBox(),window.getComputedStyle(a.current)),b[0]=this.measuredOrigin;let d=b[b.length-1];void 0!==d&&a.getValue(c,d).jump(d,!1)}measureEndState(){let{element:a,name:b,unresolvedKeyframes:c}=this;if(!a||!a.current)return;let d=a.getValue(b);d&&d.jump(this.measuredOrigin,!1);let e=c.length-1,f=c[e];c[e]=jz[b](a.measureViewportBox(),window.getComputedStyle(a.current)),null!==f&&void 0===this.finalKeyframe&&(this.finalKeyframe=f),this.removedTransforms?.length&&this.removedTransforms.forEach(([b,c])=>{a.getValue(b).set(c)}),this.resolveNoneKeyframes()}}let jT=[...js,hF,hO],{schedule:jU}=gK(queueMicrotask,!1),jV={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},jW={};for(let a in jV)jW[a]={isEnabled:b=>jV[a].some(a=>!!b[a])};let jX=()=>({translate:0,scale:1,origin:0,originPoint:0}),jY=()=>({x:jX(),y:jX()}),jZ=()=>({min:0,max:0}),j$=()=>({x:jZ(),y:jZ()}),j_={current:null},j0={current:!1},j1=new WeakMap;function j2(a){return null!==a&&"object"==typeof a&&"function"==typeof a.start}function j3(a){return"string"==typeof a||Array.isArray(a)}let j4=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],j5=["initial",...j4];function j6(a){return j2(a.animate)||j5.some(b=>j3(a[b]))}function j7(a){return!!(j6(a)||a.variants)}function j8(a){let b=[{},{}];return a?.values.forEach((a,c)=>{b[0][c]=a.get(),b[1][c]=a.getVelocity()}),b}function j9(a,b,c,d){if("function"==typeof b){let[e,f]=j8(d);b=b(void 0!==c?c:a.custom,e,f)}if("string"==typeof b&&(b=a.variants&&a.variants[b]),"function"==typeof b){let[e,f]=j8(d);b=b(void 0!==c?c:a.custom,e,f)}return b}let ka=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class kb{scrapeMotionValuesFromProps(a,b,c){return{}}constructor({parent:a,props:b,presenceContext:c,reducedMotionConfig:d,blockInitialAnimation:e,visualState:f},g={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=jG,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let a=gQ.now();this.renderScheduledAt<a&&(this.renderScheduledAt=a,gL.render(this.render,!1,!0))};let{latestValues:h,renderState:i}=f;this.latestValues=h,this.baseTarget={...h},this.initialValues=b.initial?{...h}:{},this.renderState=i,this.parent=a,this.props=b,this.presenceContext=c,this.depth=a?a.depth+1:0,this.reducedMotionConfig=d,this.options=g,this.blockInitialAnimation=!!e,this.isControllingVariants=j6(b),this.isVariantNode=j7(b),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(a&&a.current);let{willChange:j,...k}=this.scrapeMotionValuesFromProps(b,{},this);for(let a in k){let b=k[a];void 0!==h[a]&&il(b)&&b.set(h[a])}}mount(a){this.current=a,j1.set(a,this),this.projection&&!this.projection.instance&&this.projection.mount(a),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((a,b)=>this.bindToMotionValue(b,a)),j0.current||function(){if(j0.current=!0,ih)if(window.matchMedia){let a=window.matchMedia("(prefers-reduced-motion)"),b=()=>j_.current=a.matches;a.addEventListener("change",b),b()}else j_.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||j_.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let a in this.projection&&this.projection.unmount(),gM(this.notifyUpdate),gM(this.render),this.valueSubscriptions.forEach(a=>a()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[a].clear();for(let a in this.features){let b=this.features[a];b&&(b.unmount(),b.isMounted=!1)}this.current=null}addChild(a){this.children.add(a),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(a)}removeChild(a){this.children.delete(a),this.enteringChildren&&this.enteringChildren.delete(a)}bindToMotionValue(a,b){let c;this.valueSubscriptions.has(a)&&this.valueSubscriptions.get(a)();let d=i3.has(a);d&&this.onBindTransform&&this.onBindTransform();let e=b.on("change",b=>{this.latestValues[a]=b,this.props.onUpdate&&gL.preRender(this.notifyUpdate),d&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(c=window.MotionCheckAppearSync(this,a,b)),this.valueSubscriptions.set(a,()=>{e(),c&&c(),b.owner&&b.stop()})}sortNodePosition(a){return this.current&&this.sortInstanceNodePosition&&this.type===a.type?this.sortInstanceNodePosition(this.current,a.current):0}updateFeatures(){let a="animation";for(a in jW){let b=jW[a];if(!b)continue;let{isEnabled:c,Feature:d}=b;if(!this.features[a]&&d&&c(this.props)&&(this.features[a]=new d(this)),this.features[a]){let b=this.features[a];b.isMounted?b.update():(b.mount(),b.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):j$()}getStaticValue(a){return this.latestValues[a]}setStaticValue(a,b){this.latestValues[a]=b}update(a,b){(a.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=a,this.prevPresenceContext=this.presenceContext,this.presenceContext=b;for(let b=0;b<ka.length;b++){let c=ka[b];this.propEventSubscriptions[c]&&(this.propEventSubscriptions[c](),delete this.propEventSubscriptions[c]);let d=a["on"+c];d&&(this.propEventSubscriptions[c]=this.on(c,d))}this.prevMotionValues=function(a,b,c){for(let d in b){let e=b[d],f=c[d];if(il(e))a.addValue(d,e);else if(il(f))a.addValue(d,gT(e,{owner:a}));else if(f!==e)if(a.hasValue(d)){let b=a.getValue(d);!0===b.liveStyle?b.jump(e):b.hasAnimated||b.set(e)}else{let b=a.getStaticValue(d);a.addValue(d,gT(void 0!==b?b:e,{owner:a}))}}for(let d in c)void 0===b[d]&&a.removeValue(d);return b}(this,this.scrapeMotionValuesFromProps(a,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(a){return this.props.variants?this.props.variants[a]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(a){let b=this.getClosestVariantNode();if(b)return b.variantChildren&&b.variantChildren.add(a),()=>b.variantChildren.delete(a)}addValue(a,b){let c=this.values.get(a);b!==c&&(c&&this.removeValue(a),this.bindToMotionValue(a,b),this.values.set(a,b),this.latestValues[a]=b.get())}removeValue(a){this.values.delete(a);let b=this.valueSubscriptions.get(a);b&&(b(),this.valueSubscriptions.delete(a)),delete this.latestValues[a],this.removeValueFromRenderState(a,this.renderState)}hasValue(a){return this.values.has(a)}getValue(a,b){if(this.props.values&&this.props.values[a])return this.props.values[a];let c=this.values.get(a);return void 0===c&&void 0!==b&&(c=gT(null===b?void 0:b,{owner:this}),this.addValue(a,c)),c}readValue(a,b){let c=void 0===this.latestValues[a]&&this.current?this.getBaseTargetFromProps(this.props,a)??this.readValueFromInstance(this.current,a,this.options):this.latestValues[a];if(null!=c){if("string"==typeof c&&(ju(c)||jH(c)))c=parseFloat(c);else{let d;d=c,!jT.find(jr(d))&&hO.test(b)&&(c=jQ(a,b))}this.setBaseTarget(a,il(c)?c.get():c)}return il(c)?c.get():c}setBaseTarget(a,b){this.baseTarget[a]=b}getBaseTarget(a){let b,{initial:c}=this.props;if("string"==typeof c||"object"==typeof c){let d=j9(this.props,c,this.presenceContext?.custom);d&&(b=d[a])}if(c&&void 0!==b)return b;let d=this.getBaseTargetFromProps(this.props,a);return void 0===d||il(d)?void 0!==this.initialValues[a]&&void 0===b?void 0:this.baseTarget[a]:d}on(a,b){return this.events[a]||(this.events[a]=new gE),this.events[a].add(b)}notify(a,...b){this.events[a]&&this.events[a].notify(...b)}scheduleRenderMicrotask(){jU.render(this.render)}}class kc extends kb{constructor(){super(...arguments),this.KeyframeResolver=jS}sortInstanceNodePosition(a,b){return 2&a.compareDocumentPosition(b)?1:-1}getBaseTargetFromProps(a,b){return a.style?a.style[b]:void 0}removeValueFromRenderState(a,{vars:b,style:c}){delete b[a],delete c[a]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:a}=this.props;il(a)&&(this.childSubscription=a.on("change",a=>{this.current&&(this.current.textContent=`${a}`)}))}}let kd=(a,b)=>b&&"number"==typeof a?b.transform(a):a,ke={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},kf=i2.length;function kg(a,b,c){let{style:d,vars:e,transformOrigin:f}=a,g=!1,h=!1;for(let a in b){let c=b[a];if(i3.has(a)){g=!0;continue}if(hi(a)){e[a]=c;continue}{let b=kd(c,jN[a]);a.startsWith("origin")?(h=!0,f[a]=b):d[a]=b}}if(!b.transform&&(g||c?d.transform=function(a,b,c){let d="",e=!0;for(let f=0;f<kf;f++){let g=i2[f],h=a[g];if(void 0===h)continue;let i=!0;if(!(i="number"==typeof h?h===+!!g.startsWith("scale"):0===parseFloat(h))||c){let a=kd(h,jN[g]);if(!i){e=!1;let b=ke[g]||g;d+=`${b}(${a}) `}c&&(b[g]=a)}}return d=d.trim(),c?d=c(b,e?"":d):e&&(d="none"),d}(b,a.transform,c):d.transform&&(d.transform="none")),h){let{originX:a="50%",originY:b="50%",originZ:c=0}=f;d.transformOrigin=`${a} ${b} ${c}`}}function kh(a,{style:b,vars:c},d,e){let f,g=a.style;for(f in b)g[f]=b[f];for(f in e?.applyProjectionStyles(g,d),c)g.setProperty(f,c[f])}let ki={};function kj(a,{layout:b,layoutId:c}){return i3.has(a)||a.startsWith("origin")||(b||void 0!==c)&&(!!ki[a]||"opacity"===a)}function kk(a,b,c){let{style:d}=a,e={};for(let f in d)(il(d[f])||b.style&&il(b.style[f])||kj(f,a)||c?.getValue(f)?.liveStyle!==void 0)&&(e[f]=d[f]);return e}class kl extends kc{constructor(){super(...arguments),this.type="html",this.renderInstance=kh}readValueFromInstance(a,b){if(i3.has(b))return this.projection?.isProjecting?jb(b):((a,b)=>{let{transform:c="none"}=getComputedStyle(a);return jc(c,b)})(a,b);{let c=window.getComputedStyle(a),d=(hi(b)?c.getPropertyValue(b):c[b])||0;return"string"==typeof d?d.trim():d}}measureInstanceViewportBox(a,{transformPagePoint:b}){return jp(a,b)}build(a,b,c){kg(a,b,c.transformTemplate)}scrapeMotionValuesFromProps(a,b,c){return kk(a,b,c)}}let km=a=>a.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),kn={offset:"stroke-dashoffset",array:"stroke-dasharray"},ko={offset:"strokeDashoffset",array:"strokeDasharray"};function kp(a,{attrX:b,attrY:c,attrScale:d,pathLength:e,pathSpacing:f=1,pathOffset:g=0,...h},i,j,k){if(kg(a,h,j),i){a.style.viewBox&&(a.attrs.viewBox=a.style.viewBox);return}a.attrs=a.style,a.style={};let{attrs:l,style:m}=a;l.transform&&(m.transform=l.transform,delete l.transform),(m.transform||l.transformOrigin)&&(m.transformOrigin=l.transformOrigin??"50% 50%",delete l.transformOrigin),m.transform&&(m.transformBox=k?.transformBox??"fill-box",delete l.transformBox),void 0!==b&&(l.x=b),void 0!==c&&(l.y=c),void 0!==d&&(l.scale=d),void 0!==e&&function(a,b,c=1,d=0,e=!0){a.pathLength=1;let f=e?kn:ko;a[f.offset]=hA.transform(-d);let g=hA.transform(b),h=hA.transform(c);a[f.array]=`${g} ${h}`}(l,e,f,g,!1)}let kq=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),kr=a=>"string"==typeof a&&"svg"===a.toLowerCase();function ks(a,b,c){let d=kk(a,b,c);for(let c in a)(il(a[c])||il(b[c]))&&(d[-1!==i2.indexOf(c)?"attr"+c.charAt(0).toUpperCase()+c.substring(1):c]=a[c]);return d}class kt extends kc{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=j$}getBaseTargetFromProps(a,b){return a[b]}readValueFromInstance(a,b){if(i3.has(b)){let a=jP(b);return a&&a.default||0}return b=kq.has(b)?b:km(b),a.getAttribute(b)}scrapeMotionValuesFromProps(a,b,c){return ks(a,b,c)}build(a,b,c){kp(a,b,this.isSVGTag,c.transformTemplate,c.style)}renderInstance(a,b,c,d){for(let c in kh(a,b,void 0,d),b.attrs)a.setAttribute(kq.has(c)?c:km(c),b.attrs[c])}mount(a){this.isSVGTag=kr(a.tagName),super.mount(a)}}let ku=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function kv(a){if("string"!=typeof a||a.includes("-"));else if(ku.indexOf(a)>-1||/[A-Z]/u.test(a))return!0;return!1}let kw=(0,h.createContext)({}),kx=(0,h.createContext)({strict:!1}),ky=(0,h.createContext)({});function kz(a){return Array.isArray(a)?a.join(" "):a}let kA=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function kB(a,b,c){for(let d in b)il(b[d])||kj(d,c)||(a[d]=b[d])}let kC=()=>({...kA(),attrs:{}}),kD=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function kE(a){return a.startsWith("while")||a.startsWith("drag")&&"draggable"!==a||a.startsWith("layout")||a.startsWith("onTap")||a.startsWith("onPan")||a.startsWith("onLayout")||kD.has(a)}let kF=a=>!kE(a);try{!function(a){"function"==typeof a&&(kF=b=>b.startsWith("on")?!kE(b):a(b))}(require("@emotion/is-prop-valid").default)}catch{}let kG=(0,h.createContext)(null);function kH(a){return il(a)?a.get():a}let kI=a=>(b,c)=>{let d=(0,h.useContext)(ky),e=(0,h.useContext)(kG),f=()=>(function({scrapeMotionValuesFromProps:a,createRenderState:b},c,d,e){return{latestValues:function(a,b,c,d){let e={},f=d(a,{});for(let a in f)e[a]=kH(f[a]);let{initial:g,animate:h}=a,i=j6(a),j=j7(a);b&&j&&!i&&!1!==a.inherit&&(void 0===g&&(g=b.initial),void 0===h&&(h=b.animate));let k=!!c&&!1===c.initial,l=(k=k||!1===g)?h:g;if(l&&"boolean"!=typeof l&&!j2(l)){let b=Array.isArray(l)?l:[l];for(let c=0;c<b.length;c++){let d=j9(a,b[c]);if(d){let{transitionEnd:a,transition:b,...c}=d;for(let a in c){let b=c[a];if(Array.isArray(b)){let a=k?b.length-1:0;b=b[a]}null!==b&&(e[a]=b)}for(let b in a)e[b]=a[b]}}}return e}(c,d,e,a),renderState:b()}})(a,b,d,e);return c?f():gV(f)},kJ=kI({scrapeMotionValuesFromProps:kk,createRenderState:kA}),kK=kI({scrapeMotionValuesFromProps:ks,createRenderState:kC}),kL=Symbol.for("motionComponentSymbol");function kM(a){return a&&"object"==typeof a&&Object.prototype.hasOwnProperty.call(a,"current")}let kN="data-"+km("framerAppearId"),kO=(0,h.createContext)({});function kP(a,{forwardMotionProps:b=!1}={},c,d){c&&function(a){for(let b in a)jW[b]={...jW[b],...a[b]}}(c);let e=kv(a)?kK:kJ;function f(c,f){var i;let j,k={...(0,h.useContext)(gU),...c,layoutId:function({layoutId:a}){let b=(0,h.useContext)(kw).id;return b&&void 0!==a?b+"-"+a:a}(c)},{isStatic:l}=k,m=function(a){let{initial:b,animate:c}=function(a,b){if(j6(a)){let{initial:b,animate:c}=a;return{initial:!1===b||j3(b)?b:void 0,animate:j3(c)?c:void 0}}return!1!==a.inherit?b:{}}(a,(0,h.useContext)(ky));return(0,h.useMemo)(()=>({initial:b,animate:c}),[kz(b),kz(c)])}(c),n=e(c,l);if(!l&&ih){(0,h.useContext)(kx).strict;let b=function(a){let{drag:b,layout:c}=jW;if(!b&&!c)return{};let d={...b,...c};return{MeasureLayout:b?.isEnabled(a)||c?.isEnabled(a)?d.MeasureLayout:void 0,ProjectionNode:d.ProjectionNode}}(k);j=b.MeasureLayout,m.visualElement=function(a,b,c,d,e){let{visualElement:f}=(0,h.useContext)(ky),g=(0,h.useContext)(kx),i=(0,h.useContext)(kG),j=(0,h.useContext)(gU).reducedMotion,k=(0,h.useRef)(null);d=d||g.renderer,!k.current&&d&&(k.current=d(a,{visualState:b,parent:f,props:c,presenceContext:i,blockInitialAnimation:!!i&&!1===i.initial,reducedMotionConfig:j}));let l=k.current,m=(0,h.useContext)(kO);l&&!l.projection&&e&&("html"===l.type||"svg"===l.type)&&function(a,b,c,d){let{layoutId:e,layout:f,drag:g,dragConstraints:h,layoutScroll:i,layoutRoot:j,layoutCrossfade:k}=b;a.projection=new c(a.latestValues,b["data-framer-portal-id"]?void 0:function a(b){if(b)return!1!==b.options.allowProjection?b.projection:a(b.parent)}(a.parent)),a.projection.setOptions({layoutId:e,layout:f,alwaysMeasureLayout:!!g||h&&kM(h),visualElement:a,animationType:"string"==typeof f?f:"both",initialPromotionConfig:d,crossfade:k,layoutScroll:i,layoutRoot:j})}(k.current,c,e,m);let n=(0,h.useRef)(!1);(0,h.useInsertionEffect)(()=>{l&&n.current&&l.update(c,i)});let o=c[kN],p=(0,h.useRef)(!!o&&!window.MotionHandoffIsComplete?.(o)&&window.MotionHasOptimisedAnimation?.(o));return ii(()=>{l&&(n.current=!0,window.MotionIsMounted=!0,l.updateFeatures(),l.scheduleRenderMicrotask(),p.current&&l.animationState&&l.animationState.animateChanges())}),(0,h.useEffect)(()=>{l&&(!p.current&&l.animationState&&l.animationState.animateChanges(),p.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(o)}),p.current=!1),l.enteringChildren=void 0)}),l}(a,n,k,d,b.ProjectionNode)}return(0,g.jsxs)(ky.Provider,{value:m,children:[j&&m.visualElement?(0,g.jsx)(j,{visualElement:m.visualElement,...k}):null,function(a,b,c,{latestValues:d},e,f=!1){let g=(kv(a)?function(a,b,c,d){let e=(0,h.useMemo)(()=>{let c=kC();return kp(c,b,kr(d),a.transformTemplate,a.style),{...c.attrs,style:{...c.style}}},[b]);if(a.style){let b={};kB(b,a.style,a),e.style={...b,...e.style}}return e}:function(a,b){let c={},d=function(a,b){let c=a.style||{},d={};return kB(d,c,a),Object.assign(d,function({transformTemplate:a},b){return(0,h.useMemo)(()=>{let c=kA();return kg(c,b,a),Object.assign({},c.vars,c.style)},[b])}(a,b)),d}(a,b);return a.drag&&!1!==a.dragListener&&(c.draggable=!1,d.userSelect=d.WebkitUserSelect=d.WebkitTouchCallout="none",d.touchAction=!0===a.drag?"none":`pan-${"x"===a.drag?"y":"x"}`),void 0===a.tabIndex&&(a.onTap||a.onTapStart||a.whileTap)&&(c.tabIndex=0),c.style=d,c})(b,d,e,a),i=function(a,b,c){let d={};for(let e in a)("values"!==e||"object"!=typeof a.values)&&(kF(e)||!0===c&&kE(e)||!b&&!kE(e)||a.draggable&&e.startsWith("onDrag"))&&(d[e]=a[e]);return d}(b,"string"==typeof a,f),j=a!==h.Fragment?{...i,...g,ref:c}:{},{children:k}=b,l=(0,h.useMemo)(()=>il(k)?k.get():k,[k]);return(0,h.createElement)(a,{...j,children:l})}(a,c,(i=m.visualElement,(0,h.useCallback)(a=>{a&&n.onMount&&n.onMount(a),i&&(a?i.mount(a):i.unmount()),f&&("function"==typeof f?f(a):kM(f)&&(f.current=a))},[i])),n,l,b)]})}f.displayName=`motion.${"string"==typeof a?a:`create(${a.displayName??a.name??""})`}`;let i=(0,h.forwardRef)(f);return i[kL]=a,i}function kQ(a,b,c){let d=a.getProps();return j9(d,b,void 0!==c?c:d.custom,a)}function kR(a,b){return a?.[b]??a?.default??a}let kS=a=>Array.isArray(a);function kT(a,b){let{transitionEnd:c={},transition:d={},...e}=kQ(a,b)||{};for(let b in e={...e,...c}){var f;let c=kS(f=e[b])?f[f.length-1]||0:f;a.hasValue(b)?a.getValue(b).set(c):a.addValue(b,gT(c))}}function kU(a,b){let c=a.getValue("willChange");if(il(c)&&c.add)return c.add(b);if(!c&&gG.WillChange){let c=new gG.WillChange("auto");a.addValue("willChange",c),c.add(b)}}function kV(a){a.duration=0,a.type}let kW={},kX=function(a,b){let c=g$(a);return()=>kW[b]??c()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(a){return!1}return!0},"linearEasing"),kY=([a,b,c,d])=>`cubic-bezier(${a}, ${b}, ${c}, ${d})`,kZ={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:kY([0,.65,.55,1]),circOut:kY([.55,0,1,.45]),backIn:kY([.31,.01,.66,-.59]),backOut:kY([.33,1.53,.69,.99])};function k$(a){return"function"==typeof a&&"applyToOptions"in a}class k_ extends iW{constructor(a){if(super(),this.finishedTime=null,this.isStopped=!1,!a)return;let{element:b,name:c,keyframes:d,pseudoElement:e,allowFlatten:f=!1,finalKeyframe:g,onComplete:h}=a;this.isPseudoElement=!!e,this.allowFlatten=f,this.options=a,gY("string"!=typeof a.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let i=function({type:a,...b}){return k$(a)&&kX()?a.applyToOptions(b):(b.duration??(b.duration=300),b.ease??(b.ease="easeOut"),b)}(a);this.animation=function(a,b,c,{delay:d=0,duration:e=300,repeat:f=0,repeatType:g="loop",ease:h="easeOut",times:i}={},j){let k={[b]:c};i&&(k.offset=i);let l=function a(b,c){if(b)return"function"==typeof b?kX()?iq(b,c):"ease-out":iO(b)?kY(b):Array.isArray(b)?b.map(b=>a(b,c)||kZ.easeOut):kZ[b]}(h,e);Array.isArray(l)&&(k.easing=l),gJ.value&&io.waapi++;let m={delay:d,duration:e,easing:Array.isArray(l)?"linear":l,fill:"both",iterations:f+1,direction:"reverse"===g?"alternate":"normal"};j&&(m.pseudoElement=j);let n=a.animate(k,m);return gJ.value&&n.finished.finally(()=>{io.waapi--}),n}(b,c,d,i,e),!1===i.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!e){let a=iT(d,this.options,g,this.speed);this.updateMotionValue?this.updateMotionValue(a):function(a,b,c){b.startsWith("--")?a.style.setProperty(b,c):a.style[b]=c}(b,c,a),this.animation.cancel()}h?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(a){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:a}=this;"idle"!==a&&"finished"!==a&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return Number(this.animation.effect?.getComputedTiming?.().duration||0)/1e3}get time(){return(Number(this.animation.currentTime)||0)/1e3}set time(a){this.finishedTime=null,this.animation.currentTime=im(a)}get speed(){return this.animation.playbackRate}set speed(a){a<0&&(this.finishedTime=null),this.animation.playbackRate=a}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(a){this.animation.startTime=a}attachTimeline({timeline:a,observe:b}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,a&&g_())?(this.animation.timeline=a,gH):b(this)}}let k0={anticipate:iK,backInOut:iJ,circInOut:iN};class k1 extends k_{constructor(a){!function(a){"string"==typeof a.ease&&a.ease in k0&&(a.ease=k0[a.ease])}(a),iV(a),super(a),a.startTime&&(this.startTime=a.startTime),this.options=a}updateMotionValue(a){let{motionValue:b,onUpdate:c,onComplete:d,element:e,...f}=this.options;if(!b)return;if(void 0!==a)return void b.set(a);let g=new iY({...f,autoplay:!1}),h=im(this.finishedTime??this.time);b.setWithVelocity(g.sample(h-10).value,g.sample(h).value,10),g.stop()}}let k2=(a,b)=>"zIndex"!==b&&!!("number"==typeof a||Array.isArray(a)||"string"==typeof a&&(hO.test(a)||"0"===a)&&!a.startsWith("url(")),k3=new Set(["opacity","clipPath","filter","transform"]),k4=g$(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class k5 extends iW{constructor({autoplay:a=!0,delay:b=0,type:c="keyframes",repeat:d=0,repeatDelay:e=0,repeatType:f="loop",keyframes:g,name:h,motionValue:i,element:j,...k}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=gQ.now();let l={autoplay:a,delay:b,type:c,repeat:d,repeatDelay:e,repeatType:f,name:h,motionValue:i,element:j,...k},m=j?.KeyframeResolver||jG;this.keyframeResolver=new m(g,(a,b,c)=>this.onKeyframesResolved(a,b,l,!c),h,i,j),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(a,b,c,d){this.keyframeResolver=void 0;let{name:e,type:f,velocity:g,delay:h,isHandoff:i,onUpdate:j}=c;this.resolvedAt=gQ.now(),!function(a,b,c,d){let e=a[0];if(null===e)return!1;if("display"===b||"visibility"===b)return!0;let f=a[a.length-1],g=k2(e,b),h=k2(f,b);return gX(g===h,`You are trying to animate ${b} from "${e}" to "${f}". "${g?f:e}" is not an animatable value.`,"value-not-animatable"),!!g&&!!h&&(function(a){let b=a[0];if(1===a.length)return!0;for(let c=0;c<a.length;c++)if(a[c]!==b)return!0}(a)||("spring"===c||k$(c))&&d)}(a,e,f,g)&&((gG.instantAnimations||!h)&&j?.(iT(a,c,b)),a[0]=a[a.length-1],kV(c),c.repeat=0);let k={startTime:d?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:b,...c,keyframes:a},l=!i&&function(a){let{motionValue:b,name:c,repeatDelay:d,repeatType:e,damping:f,type:g}=a;if(!(b?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:h,transformTemplate:i}=b.owner.getProps();return k4()&&c&&k3.has(c)&&("transform"!==c||!i)&&!h&&!d&&"mirror"!==e&&0!==f&&"inertia"!==g}(k)?new k1({...k,element:k.motionValue.owner.current}):new iY(k);l.finished.then(()=>this.notifyFinished()).catch(gH),this.pendingTimeline&&(this.stopTimeline=l.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=l}get finished(){return this._animation?this.animation.finished:this._finished}then(a,b){return this.finished.finally(a).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),jD=!0,jF(),jE(),jD=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(a){this.animation.time=a}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(a){this.animation.speed=a}get startTime(){return this.animation.startTime}attachTimeline(a){return this._animation?this.stopTimeline=this.animation.attachTimeline(a):this.pendingTimeline=a,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let k6=a=>null!==a,k7={type:"spring",stiffness:500,damping:25,restSpeed:10},k8={type:"keyframes",duration:.8},k9={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},la=(a,b,c,d={},e,f)=>g=>{let h=kR(d,a)||{},i=h.delay||d.delay||0,{elapsed:j=0}=d;j-=im(i);let k={keyframes:Array.isArray(c)?c:[null,c],ease:"easeOut",velocity:b.getVelocity(),...h,delay:-j,onUpdate:a=>{b.set(a),h.onUpdate&&h.onUpdate(a)},onComplete:()=>{g(),h.onComplete&&h.onComplete()},name:a,motionValue:b,element:f?void 0:e};!function({when:a,delay:b,delayChildren:c,staggerChildren:d,staggerDirection:e,repeat:f,repeatType:g,repeatDelay:h,from:i,elapsed:j,...k}){return!!Object.keys(k).length}(h)&&Object.assign(k,((a,{keyframes:b})=>b.length>2?k8:i3.has(a)?a.startsWith("scale")?{type:"spring",stiffness:550,damping:0===b[1]?2*Math.sqrt(550):30,restSpeed:10}:k7:k9)(a,k)),k.duration&&(k.duration=im(k.duration)),k.repeatDelay&&(k.repeatDelay=im(k.repeatDelay)),void 0!==k.from&&(k.keyframes[0]=k.from);let l=!1;if(!1!==k.type&&(0!==k.duration||k.repeatDelay)||(kV(k),0===k.delay&&(l=!0)),(gG.instantAnimations||gG.skipAnimations)&&(l=!0,kV(k),k.delay=0),k.allowFlatten=!h.type&&!h.ease,l&&!f&&void 0!==b.get()){let a=function(a,{repeat:b,repeatType:c="loop"},d){let e=a.filter(k6),f=b&&"loop"!==c&&b%2==1?0:e.length-1;return e[f]}(k.keyframes,h);if(void 0!==a)return void gL.update(()=>{k.onUpdate(a),k.onComplete()})}return h.isSync?new iY(k):new k5(k)};function lb(a,b,{delay:c=0,transitionOverride:d,type:e}={}){let{transition:f=a.getDefaultTransition(),transitionEnd:g,...h}=b;d&&(f=d);let i=[],j=e&&a.animationState&&a.animationState.getState()[e];for(let b in h){let d=a.getValue(b,a.latestValues[b]??null),e=h[b];if(void 0===e||j&&function({protectedKeys:a,needsAnimating:b},c){let d=a.hasOwnProperty(c)&&!0!==b[c];return b[c]=!1,d}(j,b))continue;let g={delay:c,...kR(f||{},b)},k=d.get();if(void 0!==k&&!d.isAnimating&&!Array.isArray(e)&&e===k&&!g.velocity)continue;let l=!1;if(window.MotionHandoffAnimation){let c=a.props[kN];if(c){let a=window.MotionHandoffAnimation(c,b,gL);null!==a&&(g.startTime=a,l=!0)}}kU(a,b),d.start(la(b,d,e,a.shouldReduceMotion&&jq.has(b)?{type:!1}:g,a,l));let m=d.animation;m&&i.push(m)}return g&&Promise.all(i).then(()=>{gL.update(()=>{g&&kT(a,g)})}),i}function lc(a,b,c,d=0,e=1){let f=Array.from(a).sort((a,b)=>a.sortNodePosition(b)).indexOf(b),g=a.size,h=(g-1)*d;return"function"==typeof c?c(f,g):1===e?f*d:h-f*d}function ld(a,b,c={}){let d=kQ(a,b,"exit"===c.type?a.presenceContext?.custom:void 0),{transition:e=a.getDefaultTransition()||{}}=d||{};c.transitionOverride&&(e=c.transitionOverride);let f=d?()=>Promise.all(lb(a,d,c)):()=>Promise.resolve(),g=a.variantChildren&&a.variantChildren.size?(d=0)=>{let{delayChildren:f=0,staggerChildren:g,staggerDirection:h}=e;return function(a,b,c=0,d=0,e=0,f=1,g){let h=[];for(let i of a.variantChildren)i.notify("AnimationStart",b),h.push(ld(i,b,{...g,delay:c+("function"==typeof d?0:d)+lc(a.variantChildren,i,d,e,f)}).then(()=>i.notify("AnimationComplete",b)));return Promise.all(h)}(a,b,d,f,g,h,c)}:()=>Promise.resolve(),{when:h}=e;if(!h)return Promise.all([f(),g(c.delay)]);{let[a,b]="beforeChildren"===h?[f,g]:[g,f];return a().then(()=>b())}}function le(a,b,c={}){let d;if(a.notify("AnimationStart",b),Array.isArray(b))d=Promise.all(b.map(b=>ld(a,b,c)));else if("string"==typeof b)d=ld(a,b,c);else{let e="function"==typeof b?kQ(a,b,c.custom):b;d=Promise.all(lb(a,e,c))}return d.then(()=>{a.notify("AnimationComplete",b)})}function lf(a,b){if(!Array.isArray(b))return!1;let c=b.length;if(c!==a.length)return!1;for(let d=0;d<c;d++)if(b[d]!==a[d])return!1;return!0}let lg=j5.length,lh=[...j4].reverse(),li=j4.length;function lj(a=!1){return{isActive:a,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function lk(){return{animate:lj(!0),whileInView:lj(),whileHover:lj(),whileTap:lj(),whileDrag:lj(),whileFocus:lj(),exit:lj()}}class ll{constructor(a){this.isMounted=!1,this.node=a}update(){}}class lm extends ll{constructor(a){super(a),a.animationState||(a.animationState=function(a){let b=b=>Promise.all(b.map(({animation:b,options:c})=>le(a,b,c))),c=lk(),d=!0,e=b=>(c,d)=>{let e=kQ(a,d,"exit"===b?a.presenceContext?.custom:void 0);if(e){let{transition:a,transitionEnd:b,...d}=e;c={...c,...d,...b}}return c};function f(f){let{props:g}=a,h=function a(b){if(!b)return;if(!b.isControllingVariants){let c=b.parent&&a(b.parent)||{};return void 0!==b.props.initial&&(c.initial=b.props.initial),c}let c={};for(let a=0;a<lg;a++){let d=j5[a],e=b.props[d];(j3(e)||!1===e)&&(c[d]=e)}return c}(a.parent)||{},i=[],j=new Set,k={},l=1/0;for(let b=0;b<li;b++){var m,n;let o=lh[b],p=c[o],q=void 0!==g[o]?g[o]:h[o],r=j3(q),s=o===f?p.isActive:null;!1===s&&(l=b);let t=q===h[o]&&q!==g[o]&&r;if(t&&d&&a.manuallyAnimateOnMount&&(t=!1),p.protectedKeys={...k},!p.isActive&&null===s||!q&&!p.prevProp||j2(q)||"boolean"==typeof q)continue;let u=(m=p.prevProp,"string"==typeof(n=q)?n!==m:!!Array.isArray(n)&&!lf(n,m)),v=u||o===f&&p.isActive&&!t&&r||b>l&&r,w=!1,x=Array.isArray(q)?q:[q],y=x.reduce(e(o),{});!1===s&&(y={});let{prevResolvedValues:z={}}=p,A={...z,...y},B=b=>{v=!0,j.has(b)&&(w=!0,j.delete(b)),p.needsAnimating[b]=!0;let c=a.getValue(b);c&&(c.liveStyle=!1)};for(let a in A){let b=y[a],c=z[a];if(!k.hasOwnProperty(a))(kS(b)&&kS(c)?lf(b,c):b===c)?void 0!==b&&j.has(a)?B(a):p.protectedKeys[a]=!0:null!=b?B(a):j.add(a)}p.prevProp=q,p.prevResolvedValues=y,p.isActive&&(k={...k,...y}),d&&a.blockInitialAnimation&&(v=!1);let C=t&&u,D=!C||w;v&&D&&i.push(...x.map(b=>{let c={type:o};if("string"==typeof b&&d&&!C&&a.manuallyAnimateOnMount&&a.parent){let{parent:d}=a,e=kQ(d,b);if(d.enteringChildren&&e){let{delayChildren:b}=e.transition||{};c.delay=lc(d.enteringChildren,a,b)}}return{animation:b,options:c}}))}if(j.size){let b={};if("boolean"!=typeof g.initial){let c=kQ(a,Array.isArray(g.initial)?g.initial[0]:g.initial);c&&c.transition&&(b.transition=c.transition)}j.forEach(c=>{let d=a.getBaseTarget(c),e=a.getValue(c);e&&(e.liveStyle=!0),b[c]=d??null}),i.push({animation:b})}let o=!!i.length;return d&&(!1===g.initial||g.initial===g.animate)&&!a.manuallyAnimateOnMount&&(o=!1),d=!1,o?b(i):Promise.resolve()}return{animateChanges:f,setActive:function(b,d){if(c[b].isActive===d)return Promise.resolve();a.variantChildren?.forEach(a=>a.animationState?.setActive(b,d)),c[b].isActive=d;let e=f(b);for(let a in c)c[a].protectedKeys={};return e},setAnimateFunction:function(c){b=c(a)},getState:()=>c,reset:()=>{c=lk(),d=!0}}}(a))}updateAnimationControlsSubscription(){let{animate:a}=this.node.getProps();j2(a)&&(this.unmountControls=a.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:a}=this.node.getProps(),{animate:b}=this.node.prevProps||{};a!==b&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ln=0;class lo extends ll{constructor(){super(...arguments),this.id=ln++}update(){if(!this.node.presenceContext)return;let{isPresent:a,onExitComplete:b}=this.node.presenceContext,{isPresent:c}=this.node.prevPresenceContext||{};if(!this.node.animationState||a===c)return;let d=this.node.animationState.setActive("exit",!a);b&&!a&&d.then(()=>{b(this.id)})}mount(){let{register:a,onExitComplete:b}=this.node.presenceContext||{};b&&b(this.id),a&&(this.unmount=a(this.id))}unmount(){}}let lp={x:!1,y:!1};function lq(a,b,c,d={passive:!0}){return a.addEventListener(b,c,d),()=>a.removeEventListener(b,c)}let lr=a=>"mouse"===a.pointerType?"number"!=typeof a.button||a.button<=0:!1!==a.isPrimary;function ls(a){return{point:{x:a.pageX,y:a.pageY}}}function lt(a,b,c,d){return lq(a,b,a=>lr(a)&&c(a,ls(a)),d)}function lu(a){return a.max-a.min}function lv(a,b,c,d=.5){a.origin=d,a.originPoint=hR(b.min,b.max,a.origin),a.scale=lu(c)/lu(b),a.translate=hR(c.min,c.max,a.origin)-a.originPoint,(a.scale>=.9999&&a.scale<=1.0001||isNaN(a.scale))&&(a.scale=1),(a.translate>=-.01&&a.translate<=.01||isNaN(a.translate))&&(a.translate=0)}function lw(a,b,c,d){lv(a.x,b.x,c.x,d?d.originX:void 0),lv(a.y,b.y,c.y,d?d.originY:void 0)}function lx(a,b,c){a.min=c.min+b.min,a.max=a.min+lu(b)}function ly(a,b,c){a.min=b.min-c.min,a.max=a.min+lu(b)}function lz(a,b,c){ly(a.x,b.x,c.x),ly(a.y,b.y,c.y)}function lA(a){return[a("x"),a("y")]}let lB=({current:a})=>a?a.ownerDocument.defaultView:null,lC=(a,b)=>Math.abs(a-b);class lD{constructor(a,b,{transformPagePoint:c,contextWindow:d=window,dragSnapToOrigin:e=!1,distanceThreshold:f=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let a=lG(this.lastMoveEventInfo,this.history),b=null!==this.startEvent,c=function(a,b){return Math.sqrt(lC(a.x,b.x)**2+lC(a.y,b.y)**2)}(a.offset,{x:0,y:0})>=this.distanceThreshold;if(!b&&!c)return;let{point:d}=a,{timestamp:e}=gN;this.history.push({...d,timestamp:e});let{onStart:f,onMove:g}=this.handlers;b||(f&&f(this.lastMoveEvent,a),this.startEvent=this.lastMoveEvent),g&&g(this.lastMoveEvent,a)},this.handlePointerMove=(a,b)=>{this.lastMoveEvent=a,this.lastMoveEventInfo=lE(b,this.transformPagePoint),gL.update(this.updatePoint,!0)},this.handlePointerUp=(a,b)=>{this.end();let{onEnd:c,onSessionEnd:d,resumeAnimation:e}=this.handlers;if(this.dragSnapToOrigin&&e&&e(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let f=lG("pointercancel"===a.type?this.lastMoveEventInfo:lE(b,this.transformPagePoint),this.history);this.startEvent&&c&&c(a,f),d&&d(a,f)},!lr(a))return;this.dragSnapToOrigin=e,this.handlers=b,this.transformPagePoint=c,this.distanceThreshold=f,this.contextWindow=d||window;let g=lE(ls(a),this.transformPagePoint),{point:h}=g,{timestamp:i}=gN;this.history=[{...h,timestamp:i}];let{onSessionStart:j}=b;j&&j(a,lG(g,this.history)),this.removeListeners=hf(lt(this.contextWindow,"pointermove",this.handlePointerMove),lt(this.contextWindow,"pointerup",this.handlePointerUp),lt(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(a){this.handlers=a}end(){this.removeListeners&&this.removeListeners(),gM(this.updatePoint)}}function lE(a,b){return b?{point:b(a.point)}:a}function lF(a,b){return{x:a.x-b.x,y:a.y-b.y}}function lG({point:a},b){return{point:a,delta:lF(a,lH(b)),offset:lF(a,b[0]),velocity:function(a,b){if(a.length<2)return{x:0,y:0};let c=a.length-1,d=null,e=lH(a);for(;c>=0&&(d=a[c],!(e.timestamp-d.timestamp>im(.1)));)c--;if(!d)return{x:0,y:0};let f=(e.timestamp-d.timestamp)/1e3;if(0===f)return{x:0,y:0};let g={x:(e.x-d.x)/f,y:(e.y-d.y)/f};return g.x===1/0&&(g.x=0),g.y===1/0&&(g.y=0),g}(b,.1)}}function lH(a){return a[a.length-1]}function lI(a,b,c){return{min:void 0!==b?a.min+b:void 0,max:void 0!==c?a.max+c-(a.max-a.min):void 0}}function lJ(a,b){let c=b.min-a.min,d=b.max-a.max;return b.max-b.min<a.max-a.min&&([c,d]=[d,c]),{min:c,max:d}}function lK(a,b,c){return{min:lL(a,b),max:lL(a,c)}}function lL(a,b){return"number"==typeof a?a:a[b]||0}let lM=new WeakMap;class lN{constructor(a){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=j$(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=a}start(a,{snapToCursor:b=!1,distanceThreshold:c}={}){let{presenceContext:d}=this.visualElement;if(d&&!1===d.isPresent)return;let e=a=>{let{dragSnapToOrigin:c}=this.getProps();c?this.pauseAnimation():this.stopAnimation(),b&&this.snapToCursor(ls(a).point)},f=(a,b)=>{let{drag:c,dragPropagation:d,onDragStart:e}=this.getProps();if(c&&!d&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(a){if("x"===a||"y"===a)if(lp[a])return null;else return lp[a]=!0,()=>{lp[a]=!1};return lp.x||lp.y?null:(lp.x=lp.y=!0,()=>{lp.x=lp.y=!1})}(c),!this.openDragLock))return;this.latestPointerEvent=a,this.latestPanInfo=b,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),lA(a=>{let b=this.getAxisMotionValue(a).get()||0;if(hz.test(b)){let{projection:c}=this.visualElement;if(c&&c.layout){let d=c.layout.layoutBox[a];d&&(b=lu(d)*(parseFloat(b)/100))}}this.originPoint[a]=b}),e&&gL.postRender(()=>e(a,b)),kU(this.visualElement,"transform");let{animationState:f}=this.visualElement;f&&f.setActive("whileDrag",!0)},g=(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b;let{dragPropagation:c,dragDirectionLock:d,onDirectionLock:e,onDrag:f}=this.getProps();if(!c&&!this.openDragLock)return;let{offset:g}=b;if(d&&null===this.currentDirection){this.currentDirection=function(a,b=10){let c=null;return Math.abs(a.y)>b?c="y":Math.abs(a.x)>b&&(c="x"),c}(g),null!==this.currentDirection&&e&&e(this.currentDirection);return}this.updateAxis("x",b.point,g),this.updateAxis("y",b.point,g),this.visualElement.render(),f&&f(a,b)},h=(a,b)=>{this.latestPointerEvent=a,this.latestPanInfo=b,this.stop(a,b),this.latestPointerEvent=null,this.latestPanInfo=null},i=()=>lA(a=>"paused"===this.getAnimationState(a)&&this.getAxisMotionValue(a).animation?.play()),{dragSnapToOrigin:j}=this.getProps();this.panSession=new lD(a,{onSessionStart:e,onStart:f,onMove:g,onSessionEnd:h,resumeAnimation:i},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:j,distanceThreshold:c,contextWindow:lB(this.visualElement)})}stop(a,b){let c=a||this.latestPointerEvent,d=b||this.latestPanInfo,e=this.isDragging;if(this.cancel(),!e||!d||!c)return;let{velocity:f}=d;this.startAnimation(f);let{onDragEnd:g}=this.getProps();g&&gL.postRender(()=>g(c,d))}cancel(){this.isDragging=!1;let{projection:a,animationState:b}=this.visualElement;a&&(a.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:c}=this.getProps();!c&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),b&&b.setActive("whileDrag",!1)}updateAxis(a,b,c){let{drag:d}=this.getProps();if(!c||!lO(a,d,this.currentDirection))return;let e=this.getAxisMotionValue(a),f=this.originPoint[a]+c[a];this.constraints&&this.constraints[a]&&(f=function(a,{min:b,max:c},d){return void 0!==b&&a<b?a=d?hR(b,a,d.min):Math.max(a,b):void 0!==c&&a>c&&(a=d?hR(c,a,d.max):Math.min(a,c)),a}(f,this.constraints[a],this.elastic[a])),e.set(f)}resolveConstraints(){let{dragConstraints:a,dragElastic:b}=this.getProps(),c=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,d=this.constraints;a&&kM(a)?this.constraints||(this.constraints=this.resolveRefConstraints()):a&&c?this.constraints=function(a,{top:b,left:c,bottom:d,right:e}){return{x:lI(a.x,c,e),y:lI(a.y,b,d)}}(c.layoutBox,a):this.constraints=!1,this.elastic=function(a=.35){return!1===a?a=0:!0===a&&(a=.35),{x:lK(a,"left","right"),y:lK(a,"top","bottom")}}(b),d!==this.constraints&&c&&this.constraints&&!this.hasMutatedConstraints&&lA(a=>{!1!==this.constraints&&this.getAxisMotionValue(a)&&(this.constraints[a]=function(a,b){let c={};return void 0!==b.min&&(c.min=b.min-a.min),void 0!==b.max&&(c.max=b.max-a.min),c}(c.layoutBox[a],this.constraints[a]))})}resolveRefConstraints(){var a;let{dragConstraints:b,onMeasureDragConstraints:c}=this.getProps();if(!b||!kM(b))return!1;let d=b.current;gY(null!==d,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:e}=this.visualElement;if(!e||!e.layout)return!1;let f=function(a,b,c){let d=jp(a,c),{scroll:e}=b;return e&&(jm(d.x,e.offset.x),jm(d.y,e.offset.y)),d}(d,e.root,this.visualElement.getTransformPagePoint()),g=(a=e.layout.layoutBox,{x:lJ(a.x,f.x),y:lJ(a.y,f.y)});if(c){let a=c(function({x:a,y:b}){return{top:b.min,right:a.max,bottom:b.max,left:a.min}}(g));this.hasMutatedConstraints=!!a,a&&(g=je(a))}return g}startAnimation(a){let{drag:b,dragMomentum:c,dragElastic:d,dragTransition:e,dragSnapToOrigin:f,onDragTransitionEnd:g}=this.getProps(),h=this.constraints||{};return Promise.all(lA(g=>{if(!lO(g,b,this.currentDirection))return;let i=h&&h[g]||{};f&&(i={min:0,max:0});let j={type:"inertia",velocity:c?a[g]:0,bounceStiffness:d?200:1e6,bounceDamping:d?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...e,...i};return this.startAxisValueAnimation(g,j)})).then(g)}startAxisValueAnimation(a,b){let c=this.getAxisMotionValue(a);return kU(this.visualElement,a),c.start(la(a,c,0,b,this.visualElement,!1))}stopAnimation(){lA(a=>this.getAxisMotionValue(a).stop())}pauseAnimation(){lA(a=>this.getAxisMotionValue(a).animation?.pause())}getAnimationState(a){return this.getAxisMotionValue(a).animation?.state}getAxisMotionValue(a){let b=`_drag${a.toUpperCase()}`,c=this.visualElement.getProps();return c[b]||this.visualElement.getValue(a,(c.initial?c.initial[a]:void 0)||0)}snapToCursor(a){lA(b=>{let{drag:c}=this.getProps();if(!lO(b,c,this.currentDirection))return;let{projection:d}=this.visualElement,e=this.getAxisMotionValue(b);if(d&&d.layout){let{min:c,max:f}=d.layout.layoutBox[b];e.set(a[b]-hR(c,f,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:a,dragConstraints:b}=this.getProps(),{projection:c}=this.visualElement;if(!kM(b)||!c||!this.constraints)return;this.stopAnimation();let d={x:0,y:0};lA(a=>{let b=this.getAxisMotionValue(a);if(b&&!1!==this.constraints){let c=b.get();d[a]=function(a,b){let c=.5,d=lu(a),e=lu(b);return e>d?c=ha(b.min,b.max-d,a.min):d>e&&(c=ha(a.min,a.max-e,b.min)),hg(0,1,c)}({min:c,max:c},this.constraints[a])}});let{transformTemplate:e}=this.visualElement.getProps();this.visualElement.current.style.transform=e?e({},""):"none",c.root&&c.root.updateScroll(),c.updateLayout(),this.resolveConstraints(),lA(b=>{if(!lO(b,a,null))return;let c=this.getAxisMotionValue(b),{min:e,max:f}=this.constraints[b];c.set(hR(e,f,d[b]))})}addListeners(){if(!this.visualElement.current)return;lM.set(this.visualElement,this);let a=lt(this.visualElement.current,"pointerdown",a=>{let{drag:b,dragListener:c=!0}=this.getProps();b&&c&&this.start(a)}),b=()=>{let{dragConstraints:a}=this.getProps();kM(a)&&a.current&&(this.constraints=this.resolveRefConstraints())},{projection:c}=this.visualElement,d=c.addEventListener("measure",b);c&&!c.layout&&(c.root&&c.root.updateScroll(),c.updateLayout()),gL.read(b);let e=lq(window,"resize",()=>this.scalePositionWithinConstraints()),f=c.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b})=>{this.isDragging&&b&&(lA(b=>{let c=this.getAxisMotionValue(b);c&&(this.originPoint[b]+=a[b].translate,c.set(c.get()+a[b].translate))}),this.visualElement.render())});return()=>{e(),a(),d(),f&&f()}}getProps(){let a=this.visualElement.getProps(),{drag:b=!1,dragDirectionLock:c=!1,dragPropagation:d=!1,dragConstraints:e=!1,dragElastic:f=.35,dragMomentum:g=!0}=a;return{...a,drag:b,dragDirectionLock:c,dragPropagation:d,dragConstraints:e,dragElastic:f,dragMomentum:g}}}function lO(a,b,c){return(!0===b||b===a)&&(null===c||c===a)}class lP extends ll{constructor(a){super(a),this.removeGroupControls=gH,this.removeListeners=gH,this.controls=new lN(a)}mount(){let{dragControls:a}=this.node.getProps();a&&(this.removeGroupControls=a.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||gH}unmount(){this.removeGroupControls(),this.removeListeners()}}let lQ=a=>(b,c)=>{a&&gL.postRender(()=>a(b,c))};class lR extends ll{constructor(){super(...arguments),this.removePointerDownListener=gH}onPointerDown(a){this.session=new lD(a,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:lB(this.node)})}createPanHandlers(){let{onPanSessionStart:a,onPanStart:b,onPan:c,onPanEnd:d}=this.node.getProps();return{onSessionStart:lQ(a),onStart:lQ(b),onMove:c,onEnd:(a,b)=>{delete this.session,d&&gL.postRender(()=>d(a,b))}}}mount(){this.removePointerDownListener=lt(this.node.current,"pointerdown",a=>this.onPointerDown(a))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let lS={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function lT(a,b){return b.max===b.min?0:a/(b.max-b.min)*100}let lU={correct:(a,b)=>{if(!b.target)return a;if("string"==typeof a)if(!hA.test(a))return a;else a=parseFloat(a);let c=lT(a,b.target.x),d=lT(a,b.target.y);return`${c}% ${d}%`}},lV=!1;class lW extends h.Component{componentDidMount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c,layoutId:d}=this.props,{projection:e}=a;for(let a in lY)ki[a]=lY[a],hi(a)&&(ki[a].isCSSVariable=!0);e&&(b.group&&b.group.add(e),c&&c.register&&d&&c.register(e),lV&&e.root.didUpdate(),e.addEventListener("animationComplete",()=>{this.safeToRemove()}),e.setOptions({...e.options,onExitComplete:()=>this.safeToRemove()})),lS.hasEverUpdated=!0}getSnapshotBeforeUpdate(a){let{layoutDependency:b,visualElement:c,drag:d,isPresent:e}=this.props,{projection:f}=c;return f&&(f.isPresent=e,lV=!0,d||a.layoutDependency!==b||void 0===b||a.isPresent!==e?f.willUpdate():this.safeToRemove(),a.isPresent!==e&&(e?f.promote():f.relegate()||gL.postRender(()=>{let a=f.getStack();a&&a.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:a}=this.props.visualElement;a&&(a.root.didUpdate(),jU.postRender(()=>{!a.currentAnimation&&a.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:a,layoutGroup:b,switchLayoutGroup:c}=this.props,{projection:d}=a;lV=!0,d&&(d.scheduleCheckAfterUnmount(),b&&b.group&&b.group.remove(d),c&&c.deregister&&c.deregister(d))}safeToRemove(){let{safeToRemove:a}=this.props;a&&a()}render(){return null}}function lX(a){let[b,c]=function(a=!0){let b=(0,h.useContext)(kG);if(null===b)return[!0,null];let{isPresent:c,onExitComplete:d,register:e}=b,f=(0,h.useId)();(0,h.useEffect)(()=>{if(a)return e(f)},[a]);let g=(0,h.useCallback)(()=>a&&d&&d(f),[f,d,a]);return!c&&d?[!1,g]:[!0]}(),d=(0,h.useContext)(kw);return(0,g.jsx)(lW,{...a,layoutGroup:d,switchLayoutGroup:(0,h.useContext)(kO),isPresent:b,safeToRemove:c})}let lY={borderRadius:{...lU,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:lU,borderTopRightRadius:lU,borderBottomLeftRadius:lU,borderBottomRightRadius:lU,boxShadow:{correct:(a,{treeScale:b,projectionDelta:c})=>{let d=hO.parse(a);if(d.length>5)return a;let e=hO.createTransformer(a),f=+("number"!=typeof d[0]),g=c.x.scale*b.x,h=c.y.scale*b.y;d[0+f]/=g,d[1+f]/=h;let i=hR(g,h,.5);return"number"==typeof d[2+f]&&(d[2+f]/=i),"number"==typeof d[3+f]&&(d[3+f]/=i),e(d)}}},lZ=(a,b)=>a.depth-b.depth;class l${constructor(){this.children=[],this.isDirty=!1}add(a){gC(this.children,a),this.isDirty=!0}remove(a){gD(this.children,a),this.isDirty=!0}forEach(a){this.isDirty&&this.children.sort(lZ),this.isDirty=!1,this.children.forEach(a)}}let l_=["TopLeft","TopRight","BottomLeft","BottomRight"],l0=l_.length,l1=a=>"string"==typeof a?parseFloat(a):a,l2=a=>"number"==typeof a||hA.test(a);function l3(a,b){return void 0!==a[b]?a[b]:a.borderRadius}let l4=l6(0,.5,iM),l5=l6(.5,.95,gH);function l6(a,b,c){return d=>d<a?0:d>b?1:c(ha(a,b,d))}function l7(a,b){a.min=b.min,a.max=b.max}function l8(a,b){l7(a.x,b.x),l7(a.y,b.y)}function l9(a,b){a.translate=b.translate,a.scale=b.scale,a.originPoint=b.originPoint,a.origin=b.origin}function ma(a,b,c,d,e){return a-=b,a=d+1/c*(a-d),void 0!==e&&(a=d+1/e*(a-d)),a}function mb(a,b,[c,d,e],f,g){!function(a,b=0,c=1,d=.5,e,f=a,g=a){if(hz.test(b)&&(b=parseFloat(b),b=hR(g.min,g.max,b/100)-g.min),"number"!=typeof b)return;let h=hR(f.min,f.max,d);a===f&&(h-=b),a.min=ma(a.min,b,c,h,e),a.max=ma(a.max,b,c,h,e)}(a,b[c],b[d],b[e],b.scale,f,g)}let mc=["x","scaleX","originX"],md=["y","scaleY","originY"];function me(a,b,c,d){mb(a.x,b,mc,c?c.x:void 0,d?d.x:void 0),mb(a.y,b,md,c?c.y:void 0,d?d.y:void 0)}function mf(a){return 0===a.translate&&1===a.scale}function mg(a){return mf(a.x)&&mf(a.y)}function mh(a,b){return a.min===b.min&&a.max===b.max}function mi(a,b){return Math.round(a.min)===Math.round(b.min)&&Math.round(a.max)===Math.round(b.max)}function mj(a,b){return mi(a.x,b.x)&&mi(a.y,b.y)}function mk(a){return lu(a.x)/lu(a.y)}function ml(a,b){return a.translate===b.translate&&a.scale===b.scale&&a.originPoint===b.originPoint}class mm{constructor(){this.members=[]}add(a){gC(this.members,a),a.scheduleRender()}remove(a){if(gD(this.members,a),a===this.prevLead&&(this.prevLead=void 0),a===this.lead){let a=this.members[this.members.length-1];a&&this.promote(a)}}relegate(a){let b,c=this.members.findIndex(b=>a===b);if(0===c)return!1;for(let a=c;a>=0;a--){let c=this.members[a];if(!1!==c.isPresent){b=c;break}}return!!b&&(this.promote(b),!0)}promote(a,b){let c=this.lead;if(a!==c&&(this.prevLead=c,this.lead=a,a.show(),c)){c.instance&&c.scheduleRender(),a.scheduleRender(),a.resumeFrom=c,b&&(a.resumeFrom.preserveOpacity=!0),c.snapshot&&(a.snapshot=c.snapshot,a.snapshot.latestValues=c.animationValues||c.latestValues),a.root&&a.root.isUpdating&&(a.isLayoutDirty=!0);let{crossfade:d}=a.options;!1===d&&c.hide()}}exitAnimationComplete(){this.members.forEach(a=>{let{options:b,resumingFrom:c}=a;b.onExitComplete&&b.onExitComplete(),c&&c.options.onExitComplete&&c.options.onExitComplete()})}scheduleRender(){this.members.forEach(a=>{a.instance&&a.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let mn={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},mo=["","X","Y","Z"],mp=0;function mq(a,b,c,d){let{latestValues:e}=b;e[a]&&(c[a]=e[a],b.setStaticValue(a,0),d&&(d[a]=0))}function mr({attachResizeListener:a,defaultParent:b,measureScroll:c,checkIsScrollRoot:d,resetTransform:e}){return class{constructor(a={},c=b?.()){this.id=mp++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,gJ.value&&(mn.nodes=mn.calculatedTargetDeltas=mn.calculatedProjections=0),this.nodes.forEach(mu),this.nodes.forEach(mB),this.nodes.forEach(mC),this.nodes.forEach(mv),gJ.addProjectionMetrics&&gJ.addProjectionMetrics(mn)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=a,this.root=c?c.root||c:this,this.path=c?[...c.path,c]:[],this.parent=c,this.depth=c?c.depth+1:0;for(let a=0;a<this.path.length;a++)this.path[a].shouldResetTransform=!0;this.root===this&&(this.nodes=new l$)}addEventListener(a,b){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new gE),this.eventHandlers.get(a).add(b)}notifyListeners(a,...b){let c=this.eventHandlers.get(a);c&&c.notify(...b)}hasListeners(a){return this.eventHandlers.has(a)}mount(b){if(this.instance)return;this.isSVG=g1(b)&&!(g1(b)&&"svg"===b.tagName),this.instance=b;let{layoutId:c,layout:d,visualElement:e}=this.options;if(e&&!e.current&&e.mount(b),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(d||c)&&(this.isLayoutDirty=!0),a){let c,d=0,e=()=>this.root.updateBlockedByResize=!1;gL.read(()=>{d=window.innerWidth}),a(b,()=>{let a=window.innerWidth;a!==d&&(d=a,this.root.updateBlockedByResize=!0,c&&c(),c=function(a,b){let c=gQ.now(),d=({timestamp:b})=>{let e=b-c;e>=250&&(gM(d),a(e-250))};return gL.setup(d,!0),()=>gM(d)}(e,250),lS.hasAnimatedSinceResize&&(lS.hasAnimatedSinceResize=!1,this.nodes.forEach(mA)))})}c&&this.root.registerSharedNode(c,this),!1!==this.options.animate&&e&&(c||d)&&this.addEventListener("didUpdate",({delta:a,hasLayoutChanged:b,hasRelativeLayoutChanged:c,layout:d})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let f=this.options.transition||e.getDefaultTransition()||mI,{onLayoutAnimationStart:g,onLayoutAnimationComplete:h}=e.getProps(),i=!this.targetLayout||!mj(this.targetLayout,d),j=!b&&c;if(this.options.layoutRoot||this.resumeFrom||j||b&&(i||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let b={...kR(f,"layout"),onPlay:g,onComplete:h};(e.shouldReduceMotion||this.options.layoutRoot)&&(b.delay=0,b.type=!1),this.startAnimation(b),this.setAnimationOrigin(a,j)}else b||mA(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=d})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),gM(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(mD),this.animationId++)}getTransformTemplate(){let{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function a(b){if(b.hasCheckedOptimisedAppear=!0,b.root===b)return;let{visualElement:c}=b.options;if(!c)return;let d=c.props[kN];if(window.MotionHasOptimisedAnimation(d,"transform")){let{layout:a,layoutId:c}=b.options;window.MotionCancelOptimisedAnimation(d,"transform",gL,!(a||c))}let{parent:e}=b;e&&!e.hasCheckedOptimisedAppear&&a(e)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let a=0;a<this.path.length;a++){let b=this.path[a];b.shouldResetTransform=!0,b.updateScroll("snapshot"),b.options.layoutRoot&&b.willUpdate(!1)}let{layoutId:b,layout:c}=this.options;if(void 0===b&&!c)return;let d=this.getTransformTemplate();this.prevTransformTemplateValue=d?d(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(mx);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(my);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(mz),this.nodes.forEach(ms),this.nodes.forEach(mt)):this.nodes.forEach(my),this.clearAllSnapshots();let a=gQ.now();gN.delta=hg(0,1e3/60,a-gN.timestamp),gN.timestamp=a,gN.isProcessing=!0,gO.update.process(gN),gO.preRender.process(gN),gO.render.process(gN),gN.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,jU.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(mw),this.sharedNodes.forEach(mE)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,gL.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){gL.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||lu(this.snapshot.measuredBox.x)||lu(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let a=0;a<this.path.length;a++)this.path[a].updateScroll();let a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=j$(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:b}=this.options;b&&b.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let b=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(b=!1),b&&this.instance){let b=d(this.instance);this.scroll={animationId:this.root.animationId,phase:a,isRoot:b,offset:c(this.instance),wasRoot:this.scroll?this.scroll.isRoot:b}}}resetTransform(){if(!e)return;let a=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,b=this.projectionDelta&&!mg(this.projectionDelta),c=this.getTransformTemplate(),d=c?c(this.latestValues,""):void 0,f=d!==this.prevTransformTemplateValue;a&&this.instance&&(b||jh(this.latestValues)||f)&&(e(this.instance,d),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){var b;let c=this.measurePageBox(),d=this.removeElementScroll(c);return a&&(d=this.removeTransform(d)),mL((b=d).x),mL(b.y),{animationId:this.root.animationId,measuredBox:c,layoutBox:d,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:a}=this.options;if(!a)return j$();let b=a.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(mN))){let{scroll:a}=this.root;a&&(jm(b.x,a.offset.x),jm(b.y,a.offset.y))}return b}removeElementScroll(a){let b=j$();if(l8(b,a),this.scroll?.wasRoot)return b;for(let c=0;c<this.path.length;c++){let d=this.path[c],{scroll:e,options:f}=d;d!==this.root&&e&&f.layoutScroll&&(e.wasRoot&&l8(b,a),jm(b.x,e.offset.x),jm(b.y,e.offset.y))}return b}applyTransform(a,b=!1){let c=j$();l8(c,a);for(let a=0;a<this.path.length;a++){let d=this.path[a];!b&&d.options.layoutScroll&&d.scroll&&d!==d.root&&jo(c,{x:-d.scroll.offset.x,y:-d.scroll.offset.y}),jh(d.latestValues)&&jo(c,d.latestValues)}return jh(this.latestValues)&&jo(c,this.latestValues),c}removeTransform(a){let b=j$();l8(b,a);for(let a=0;a<this.path.length;a++){let c=this.path[a];if(!c.instance||!jh(c.latestValues))continue;jg(c.latestValues)&&c.updateSnapshot();let d=j$();l8(d,c.measurePageBox()),me(b,c.latestValues,c.snapshot?c.snapshot.layoutBox:void 0,d)}return jh(this.latestValues)&&me(b,this.latestValues),b}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:void 0===a.crossfade||a.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==gN.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(a=!1){let b=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=b.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=b.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=b.isSharedProjectionDirty);let c=!!this.resumingFrom||this!==b;if(!(a||c&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:d,layoutId:e}=this.options;if(this.layout&&(d||e)){if(this.resolvedRelativeTargetAt=gN.timestamp,!this.targetDelta&&!this.relativeTarget){let a=this.getClosestProjectingParent();a&&a.layout&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=j$(),this.relativeTargetOrigin=j$(),lz(this.relativeTargetOrigin,this.layout.layoutBox,a.layout.layoutBox),l8(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=j$(),this.targetWithTransforms=j$()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var f,g,h;this.forceRelativeParentToResolveTarget(),f=this.target,g=this.relativeTarget,h=this.relativeParent.target,lx(f.x,g.x,h.x),lx(f.y,g.y,h.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):l8(this.target,this.layout.layoutBox),jl(this.target,this.targetDelta)):l8(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let a=this.getClosestProjectingParent();a&&!!a.resumingFrom==!!this.resumingFrom&&!a.options.layoutScroll&&a.target&&1!==this.animationProgress?(this.relativeParent=a,this.forceRelativeParentToResolveTarget(),this.relativeTarget=j$(),this.relativeTargetOrigin=j$(),lz(this.relativeTargetOrigin,this.target,a.target),l8(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}gJ.value&&mn.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||jg(this.parent.latestValues)||ji(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let a=this.getLead(),b=!!this.resumingFrom||this!==a,c=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(c=!1),b&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(c=!1),this.resolvedRelativeTargetAt===gN.timestamp&&(c=!1),c)return;let{layout:d,layoutId:e}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(d||e))return;l8(this.layoutCorrected,this.layout.layoutBox);let f=this.treeScale.x,g=this.treeScale.y;!function(a,b,c,d=!1){let e,f,g=c.length;if(g){b.x=b.y=1;for(let h=0;h<g;h++){f=(e=c[h]).projectionDelta;let{visualElement:g}=e.options;(!g||!g.props.style||"contents"!==g.props.style.display)&&(d&&e.options.layoutScroll&&e.scroll&&e!==e.root&&jo(a,{x:-e.scroll.offset.x,y:-e.scroll.offset.y}),f&&(b.x*=f.x.scale,b.y*=f.y.scale,jl(a,f)),d&&jh(e.latestValues)&&jo(a,e.latestValues))}b.x<1.0000000000001&&b.x>.999999999999&&(b.x=1),b.y<1.0000000000001&&b.y>.999999999999&&(b.y=1)}}(this.layoutCorrected,this.treeScale,this.path,b),a.layout&&!a.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(a.target=a.layout.layoutBox,a.targetWithTransforms=j$());let{target:h}=a;if(!h){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(l9(this.prevProjectionDelta.x,this.projectionDelta.x),l9(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),lw(this.projectionDelta,this.layoutCorrected,h,this.latestValues),this.treeScale.x===f&&this.treeScale.y===g&&ml(this.projectionDelta.x,this.prevProjectionDelta.x)&&ml(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",h)),gJ.value&&mn.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){if(this.options.visualElement?.scheduleRender(),a){let a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=jY(),this.projectionDelta=jY(),this.projectionDeltaWithTransform=jY()}setAnimationOrigin(a,b=!1){let c,d=this.snapshot,e=d?d.latestValues:{},f={...this.latestValues},g=jY();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!b;let h=j$(),i=(d?d.source:void 0)!==(this.layout?this.layout.source:void 0),j=this.getStack(),k=!j||j.members.length<=1,l=!!(i&&!k&&!0===this.options.crossfade&&!this.path.some(mH));this.animationProgress=0,this.mixTargetDelta=b=>{let d=b/1e3;if(mF(g.x,a.x,d),mF(g.y,a.y,d),this.setTargetDelta(g),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var j,m,n,o,p,q;lz(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),n=this.relativeTarget,o=this.relativeTargetOrigin,p=h,q=d,mG(n.x,o.x,p.x,q),mG(n.y,o.y,p.y,q),c&&(j=this.relativeTarget,m=c,mh(j.x,m.x)&&mh(j.y,m.y))&&(this.isProjectionDirty=!1),c||(c=j$()),l8(c,this.relativeTarget)}i&&(this.animationValues=f,function(a,b,c,d,e,f){e?(a.opacity=hR(0,c.opacity??1,l4(d)),a.opacityExit=hR(b.opacity??1,0,l5(d))):f&&(a.opacity=hR(b.opacity??1,c.opacity??1,d));for(let e=0;e<l0;e++){let f=`border${l_[e]}Radius`,g=l3(b,f),h=l3(c,f);(void 0!==g||void 0!==h)&&(g||(g=0),h||(h=0),0===g||0===h||l2(g)===l2(h)?(a[f]=Math.max(hR(l1(g),l1(h),d),0),(hz.test(h)||hz.test(g))&&(a[f]+="%")):a[f]=h)}(b.rotate||c.rotate)&&(a.rotate=hR(b.rotate||0,c.rotate||0,d))}(f,e,this.latestValues,d,l,k)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=d},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(a){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(gM(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=gL.update(()=>{lS.hasAnimatedSinceResize=!0,io.layout++,this.motionValue||(this.motionValue=gT(0)),this.currentAnimation=function(a,b,c){let d=il(a)?a:gT(a);return d.start(la("",d,b,c)),d.animation}(this.motionValue,[0,1e3],{...a,velocity:0,isSync:!0,onUpdate:b=>{this.mixTargetDelta(b),a.onUpdate&&a.onUpdate(b)},onStop:()=>{io.layout--},onComplete:()=>{io.layout--,a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let a=this.getLead(),{targetWithTransforms:b,target:c,layout:d,latestValues:e}=a;if(b&&c&&d){if(this!==a&&this.layout&&d&&mM(this.options.animationType,this.layout.layoutBox,d.layoutBox)){c=this.target||j$();let b=lu(this.layout.layoutBox.x);c.x.min=a.target.x.min,c.x.max=c.x.min+b;let d=lu(this.layout.layoutBox.y);c.y.min=a.target.y.min,c.y.max=c.y.min+d}l8(b,c),jo(b,e),lw(this.projectionDeltaWithTransform,this.layoutCorrected,b,e)}}registerSharedNode(a,b){this.sharedNodes.has(a)||this.sharedNodes.set(a,new mm),this.sharedNodes.get(a).add(b);let c=b.options.initialPromotionConfig;b.promote({transition:c?c.transition:void 0,preserveFollowOpacity:c&&c.shouldPreserveFollowOpacity?c.shouldPreserveFollowOpacity(b):void 0})}isLead(){let a=this.getStack();return!a||a.lead===this}getLead(){let{layoutId:a}=this.options;return a&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:a}=this.options;return a?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:b,preserveFollowOpacity:c}={}){let d=this.getStack();d&&d.promote(this,c),a&&(this.projectionDelta=void 0,this.needsReset=!0),b&&this.setOptions({transition:b})}relegate(){let a=this.getStack();return!!a&&a.relegate(this)}resetSkewAndRotation(){let{visualElement:a}=this.options;if(!a)return;let b=!1,{latestValues:c}=a;if((c.z||c.rotate||c.rotateX||c.rotateY||c.rotateZ||c.skewX||c.skewY)&&(b=!0),!b)return;let d={};c.z&&mq("z",a,d,this.animationValues);for(let b=0;b<mo.length;b++)mq(`rotate${mo[b]}`,a,d,this.animationValues),mq(`skew${mo[b]}`,a,d,this.animationValues);for(let b in a.render(),d)a.setStaticValue(b,d[b]),this.animationValues&&(this.animationValues[b]=d[b]);a.scheduleRender()}applyProjectionStyles(a,b){if(!this.instance||this.isSVG)return;if(!this.isVisible){a.visibility="hidden";return}let c=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,a.visibility="",a.opacity="",a.pointerEvents=kH(b?.pointerEvents)||"",a.transform=c?c(this.latestValues,""):"none";return}let d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){this.options.layoutId&&(a.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,a.pointerEvents=kH(b?.pointerEvents)||""),this.hasProjected&&!jh(this.latestValues)&&(a.transform=c?c({},""):"none",this.hasProjected=!1);return}a.visibility="";let e=d.animationValues||d.latestValues;this.applyTransformsToTarget();let f=function(a,b,c){let d="",e=a.x.translate/b.x,f=a.y.translate/b.y,g=c?.z||0;if((e||f||g)&&(d=`translate3d(${e}px, ${f}px, ${g}px) `),(1!==b.x||1!==b.y)&&(d+=`scale(${1/b.x}, ${1/b.y}) `),c){let{transformPerspective:a,rotate:b,rotateX:e,rotateY:f,skewX:g,skewY:h}=c;a&&(d=`perspective(${a}px) ${d}`),b&&(d+=`rotate(${b}deg) `),e&&(d+=`rotateX(${e}deg) `),f&&(d+=`rotateY(${f}deg) `),g&&(d+=`skewX(${g}deg) `),h&&(d+=`skewY(${h}deg) `)}let h=a.x.scale*b.x,i=a.y.scale*b.y;return(1!==h||1!==i)&&(d+=`scale(${h}, ${i})`),d||"none"}(this.projectionDeltaWithTransform,this.treeScale,e);c&&(f=c(e,f)),a.transform=f;let{x:g,y:h}=this.projectionDelta;for(let b in a.transformOrigin=`${100*g.origin}% ${100*h.origin}% 0`,d.animationValues?a.opacity=d===this?e.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:e.opacityExit:a.opacity=d===this?void 0!==e.opacity?e.opacity:"":void 0!==e.opacityExit?e.opacityExit:0,ki){if(void 0===e[b])continue;let{correct:c,applyTo:g,isCSSVariable:h}=ki[b],i="none"===f?e[b]:c(e[b],d);if(g){let b=g.length;for(let c=0;c<b;c++)a[g[c]]=i}else h?this.options.visualElement.renderState.vars[b]=i:a[b]=i}this.options.layoutId&&(a.pointerEvents=d===this?kH(b?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>a.currentAnimation?.stop()),this.root.nodes.forEach(mx),this.root.sharedNodes.clear()}}}function ms(a){a.updateLayout()}function mt(a){let b=a.resumeFrom?.snapshot||a.snapshot;if(a.isLead()&&a.layout&&b&&a.hasListeners("didUpdate")){let{layoutBox:c,measuredBox:d}=a.layout,{animationType:e}=a.options,f=b.source!==a.layout.source;"size"===e?lA(a=>{let d=f?b.measuredBox[a]:b.layoutBox[a],e=lu(d);d.min=c[a].min,d.max=d.min+e}):mM(e,b.layoutBox,c)&&lA(d=>{let e=f?b.measuredBox[d]:b.layoutBox[d],g=lu(c[d]);e.max=e.min+g,a.relativeTarget&&!a.currentAnimation&&(a.isProjectionDirty=!0,a.relativeTarget[d].max=a.relativeTarget[d].min+g)});let g=jY();lw(g,c,b.layoutBox);let h=jY();f?lw(h,a.applyTransform(d,!0),b.measuredBox):lw(h,c,b.layoutBox);let i=!mg(g),j=!1;if(!a.resumeFrom){let d=a.getClosestProjectingParent();if(d&&!d.resumeFrom){let{snapshot:e,layout:f}=d;if(e&&f){let g=j$();lz(g,b.layoutBox,e.layoutBox);let h=j$();lz(h,c,f.layoutBox),mj(g,h)||(j=!0),d.options.layoutRoot&&(a.relativeTarget=h,a.relativeTargetOrigin=g,a.relativeParent=d)}}}a.notifyListeners("didUpdate",{layout:c,snapshot:b,delta:h,layoutDelta:g,hasLayoutChanged:i,hasRelativeLayoutChanged:j})}else if(a.isLead()){let{onExitComplete:b}=a.options;b&&b()}a.options.transition=void 0}function mu(a){gJ.value&&mn.nodes++,a.parent&&(a.isProjecting()||(a.isProjectionDirty=a.parent.isProjectionDirty),a.isSharedProjectionDirty||(a.isSharedProjectionDirty=!!(a.isProjectionDirty||a.parent.isProjectionDirty||a.parent.isSharedProjectionDirty)),a.isTransformDirty||(a.isTransformDirty=a.parent.isTransformDirty))}function mv(a){a.isProjectionDirty=a.isSharedProjectionDirty=a.isTransformDirty=!1}function mw(a){a.clearSnapshot()}function mx(a){a.clearMeasurements()}function my(a){a.isLayoutDirty=!1}function mz(a){let{visualElement:b}=a.options;b&&b.getProps().onBeforeLayoutMeasure&&b.notify("BeforeLayoutMeasure"),a.resetTransform()}function mA(a){a.finishAnimation(),a.targetDelta=a.relativeTarget=a.target=void 0,a.isProjectionDirty=!0}function mB(a){a.resolveTargetDelta()}function mC(a){a.calcProjection()}function mD(a){a.resetSkewAndRotation()}function mE(a){a.removeLeadSnapshot()}function mF(a,b,c){a.translate=hR(b.translate,0,c),a.scale=hR(b.scale,1,c),a.origin=b.origin,a.originPoint=b.originPoint}function mG(a,b,c,d){a.min=hR(b.min,c.min,d),a.max=hR(b.max,c.max,d)}function mH(a){return a.animationValues&&void 0!==a.animationValues.opacityExit}let mI={duration:.45,ease:[.4,0,.1,1]},mJ=a=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(a),mK=mJ("applewebkit/")&&!mJ("chrome/")?Math.round:gH;function mL(a){a.min=mK(a.min),a.max=mK(a.max)}function mM(a,b,c){return"position"===a||"preserve-aspect"===a&&!(.2>=Math.abs(mk(b)-mk(c)))}function mN(a){return a!==a.root&&a.scroll?.wasRoot}let mO=mr({attachResizeListener:(a,b)=>lq(a,"resize",b),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),mP={current:void 0},mQ=mr({measureScroll:a=>({x:a.scrollLeft,y:a.scrollTop}),defaultParent:()=>{if(!mP.current){let a=new mO({});a.mount(window),a.setOptions({layoutScroll:!0}),mP.current=a}return mP.current},resetTransform:(a,b)=>{a.style.transform=void 0!==b?b:"none"},checkIsScrollRoot:a=>"fixed"===window.getComputedStyle(a).position});function mR(a,b){let c=g2(a),d=new AbortController;return[c,{passive:!0,...b,signal:d.signal},()=>d.abort()]}function mS(a){return!("touch"===a.pointerType||lp.x||lp.y)}function mT(a,b,c){let{props:d}=a;a.animationState&&d.whileHover&&a.animationState.setActive("whileHover","Start"===c);let e=d["onHover"+c];e&&gL.postRender(()=>e(b,ls(b)))}class mU extends ll{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=mR(a,c),g=a=>{if(!mS(a))return;let{target:c}=a,d=b(c,a);if("function"!=typeof d||!c)return;let f=a=>{mS(a)&&(d(a),c.removeEventListener("pointerleave",f))};c.addEventListener("pointerleave",f,e)};return d.forEach(a=>{a.addEventListener("pointerenter",g,e)}),f}(a,(a,b)=>(mT(this.node,b,"Start"),a=>mT(this.node,a,"End"))))}unmount(){}}class mV extends ll{constructor(){super(...arguments),this.isActive=!1}onFocus(){let a=!1;try{a=this.node.current.matches(":focus-visible")}catch(b){a=!0}a&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=hf(lq(this.node.current,"focus",()=>this.onFocus()),lq(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let mW=(a,b)=>!!b&&(a===b||mW(a,b.parentElement)),mX=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),mY=new WeakSet;function mZ(a){return b=>{"Enter"===b.key&&a(b)}}function m$(a,b){a.dispatchEvent(new PointerEvent("pointer"+b,{isPrimary:!0,bubbles:!0}))}function m_(a){return lr(a)&&!(lp.x||lp.y)}function m0(a,b,c){let{props:d}=a;if(a.current instanceof HTMLButtonElement&&a.current.disabled)return;a.animationState&&d.whileTap&&a.animationState.setActive("whileTap","Start"===c);let e=d["onTap"+("End"===c?"":c)];e&&gL.postRender(()=>e(b,ls(b)))}class m1 extends ll{mount(){let{current:a}=this.node;a&&(this.unmount=function(a,b,c={}){let[d,e,f]=mR(a,c),g=a=>{let d=a.currentTarget;if(!m_(a))return;mY.add(d);let f=b(d,a),g=(a,b)=>{window.removeEventListener("pointerup",h),window.removeEventListener("pointercancel",i),mY.has(d)&&mY.delete(d),m_(a)&&"function"==typeof f&&f(a,{success:b})},h=a=>{g(a,d===window||d===document||c.useGlobalTarget||mW(d,a.target))},i=a=>{g(a,!1)};window.addEventListener("pointerup",h,e),window.addEventListener("pointercancel",i,e)};return d.forEach(a=>{((c.useGlobalTarget?window:a).addEventListener("pointerdown",g,e),h3(a))&&(a.addEventListener("focus",a=>((a,b)=>{let c=a.currentTarget;if(!c)return;let d=mZ(()=>{if(mY.has(c))return;m$(c,"down");let a=mZ(()=>{m$(c,"up")});c.addEventListener("keyup",a,b),c.addEventListener("blur",()=>m$(c,"cancel"),b)});c.addEventListener("keydown",d,b),c.addEventListener("blur",()=>c.removeEventListener("keydown",d),b)})(a,e)),mX.has(a.tagName)||-1!==a.tabIndex||a.hasAttribute("tabindex")||(a.tabIndex=0))}),f}(a,(a,b)=>(m0(this.node,b,"Start"),(a,{success:b})=>m0(this.node,a,b?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let m2=new WeakMap,m3=new WeakMap,m4=a=>{let b=m2.get(a.target);b&&b(a)},m5=a=>{a.forEach(m4)},m6={some:0,all:1};class m7 extends ll{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:a={}}=this.node.getProps(),{root:b,margin:c,amount:d="some",once:e}=a,f={root:b?b.current:void 0,rootMargin:c,threshold:"number"==typeof d?d:m6[d]},g=a=>{let{isIntersecting:b}=a;if(this.isInView===b||(this.isInView=b,e&&!b&&this.hasEnteredView))return;b&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",b);let{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),f=b?c:d;f&&f(a)};var h=this.node.current;let i=function({root:a,...b}){let c=a||document;m3.has(c)||m3.set(c,{});let d=m3.get(c),e=JSON.stringify(b);return d[e]||(d[e]=new IntersectionObserver(m5,{root:a,...b})),d[e]}(f);return m2.set(h,g),i.observe(h),()=>{m2.delete(h),i.unobserve(h)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:a,prevProps:b}=this.node;["amount","margin","root"].some(function({viewport:a={}},{viewport:b={}}={}){return c=>a[c]!==b[c]}(a,b))&&this.startObserver()}unmount(){}}let m8=function(a,b){if("undefined"==typeof Proxy)return kP;let c=new Map,d=(c,d)=>kP(c,d,a,b);return new Proxy((a,b)=>d(a,b),{get:(e,f)=>"create"===f?d:(c.has(f)||c.set(f,kP(f,void 0,a,b)),c.get(f))})}({animation:{Feature:lm},exit:{Feature:lo},inView:{Feature:m7},tap:{Feature:m1},focus:{Feature:mV},hover:{Feature:mU},pan:{Feature:lR},drag:{Feature:lP,ProjectionNode:mQ,MeasureLayout:lX},layout:{ProjectionNode:mQ,MeasureLayout:lX}},(a,b)=>kv(a)?new kt(b):new kl(b,{allowProjection:a!==h.Fragment})),m9=({scrollContainerRef:a,texts:b=[],velocity:c=100,className:d="",damping:e=50,stiffness:f=400,numCopies:i=6,velocityMapping:j={input:[0,1e3],output:[0,5]},parallaxClassName:k,scrollerClassName:l,parallaxStyle:m,scrollerStyle:n})=>{function o({children:a,baseVelocity:b=c,scrollContainerRef:d,className:e="",damping:f,stiffness:i,numCopies:j,velocityMapping:k,parallaxClassName:l,scrollerClassName:m,parallaxStyle:n,scrollerStyle:o}){let p=gW(0),{scrollY:q}=function({container:a,target:b,...c}={}){let d=gV(ij),e=(0,h.useRef)(null),f=(0,h.useRef)(!1),g=(0,h.useCallback)(()=>(e.current=function(a,{axis:b="y",container:c=document.scrollingElement,...d}={}){var e,f;if(!c)return gH;let g={axis:b,container:c,...d};return"function"==typeof a?(e=a,f=g,2===e.length?id(a=>{e(a[f.axis].progress,a)},f):gZ(e,ig(f))):function(a,b){let c=ig(b);return a.attachTimeline({timeline:b.target?void 0:c,observe:a=>(a.pause(),gZ(b=>{a.time=a.duration*b},c))})}(a,g)}((a,{x:b,y:c})=>{d.scrollX.set(b.current),d.scrollXProgress.set(b.progress),d.scrollY.set(c.current),d.scrollYProgress.set(c.progress)},{...c,container:a?.current||void 0,target:b?.current||void 0}),()=>{e.current?.()}),[a,b,JSON.stringify(c.offset)]);return ii(()=>{if(f.current=!1,!(ik(a)||ik(b)))return g();f.current=!0},[g]),(0,h.useEffect)(()=>f.current?(gY(!ik(a),"Container ref is defined but not hydrated","use-scroll-ref"),gY(!ik(b),"Target ref is defined but not hydrated","use-scroll-ref"),g()):void 0,[g]),d}(d?{container:d}:{}),r=i_(i1(function(a){var b,c;let d=gW(a.getVelocity()),e=()=>{let b=a.getVelocity();d.set(b),b&&gL.update(e)};return b="change",c=()=>{gL.update(e,!1,!0)},(0,h.useInsertionEffect)(()=>a.on(b,c),[a,b,c]),d}(q),{damping:f??50,stiffness:i??400}),k?.input||[0,1e3],k?.output||[0,5],{clamp:!1}),s=(0,h.useRef)(null),t=function(a){let[b,c]=(0,h.useState)(0);return b}(0),u=i_(p,a=>0===t?"0px":`${function(a,b,c){let d=0-a;return((c-a)%d+d)%d+a}(-t,0,a)}px`),v=(0,h.useRef)(1);var w=(a,c)=>{let d=v.current*b*(c/1e3);0>r.get()?v.current=-1:r.get()>0&&(v.current=1),d+=v.current*d*r.get(),p.set(p.get()+d)};let x=(0,h.useRef)(0),{isStatic:y}=(0,h.useContext)(gU);(0,h.useEffect)(()=>{if(y)return;let a=({timestamp:a,delta:b})=>{x.current||(x.current=a),w(a-x.current,b)};return gL.update(a,!0),()=>gM(a)},[w]);let z=[];for(let b=0;b<j;b++)z.push((0,g.jsx)("span",{className:`flex-shrink-0 ${e}`,ref:0===b?s:null,children:a},b));return(0,g.jsx)("div",{className:`${l} relative overflow-hidden`,style:n,children:(0,g.jsx)(m8.div,{className:`${m} flex whitespace-nowrap text-center font-sans text-4xl font-bold tracking-[-0.02em] drop-shadow md:text-[5rem] md:leading-[5rem]`,style:{x:u,...o},children:z})})}return(0,g.jsx)("section",{children:b.map((b,h)=>(0,g.jsxs)(o,{className:d,baseVelocity:h%2!=0?-c:c,scrollContainerRef:a,damping:e,stiffness:f,numCopies:i,velocityMapping:j,parallaxClassName:k,scrollerClassName:l,parallaxStyle:m,scrollerStyle:n,children:[b,"\xa0"]},h))})},na=({text:a="",delay:b=200,className:c="",animateBy:d="words",direction:e="top",threshold:f=.1,rootMargin:i="0px",animationFrom:j,animationTo:k,easing:l=a=>a,onAnimationComplete:m,stepDuration:n=.35})=>{let o="words"===d?a.split(" "):a.split(""),[p,q]=(0,h.useState)(!1),r=(0,h.useRef)(null);(0,h.useEffect)(()=>{if(!r.current)return;let a=new IntersectionObserver(([b])=>{b.isIntersecting&&(q(!0),a.unobserve(r.current))},{threshold:f,rootMargin:i});return a.observe(r.current),()=>a.disconnect()},[f,i]);let s=(0,h.useMemo)(()=>"top"===e?{filter:"blur(10px)",opacity:0,y:-50}:{filter:"blur(10px)",opacity:0,y:50},[e]),t=(0,h.useMemo)(()=>[{filter:"blur(5px)",opacity:.5,y:"top"===e?5:-5},{filter:"blur(0px)",opacity:1,y:0}],[e]),u=j??s,v=k??t,w=v.length+1,x=n*(w-1),y=Array.from({length:w},(a,b)=>1===w?0:b/(w-1));return(0,g.jsx)("p",{ref:r,className:`blur-text ${c} flex flex-wrap`,children:o.map((a,c)=>{let e=((a,b)=>{let c=new Set([...Object.keys(a),...b.flatMap(a=>Object.keys(a))]),d={};return c.forEach(c=>{d[c]=[a[c],...b.map(a=>a[c])]}),d})(u,v),f={duration:x,times:y,delay:c*b/1e3};return f.ease=l,(0,g.jsxs)(m8.span,{initial:u,animate:p?e:u,transition:f,onAnimationComplete:c===o.length-1?m:void 0,style:{display:"inline-block",willChange:"transform, filter, opacity"},children:[" "===a?"\xa0":a,"words"===d&&c<o.length-1&&"\xa0"]},c)})})},nb={damping:30,stiffness:100,mass:2};function nc({imageSrc:a,altText:b="Tilted card image",captionText:c="",containerHeight:d="300px",containerWidth:e="100%",imageHeight:f="300px",imageWidth:i="300px",scaleOnHover:j=1.1,rotateAmplitude:k=14,showMobileWarning:l=!0,showTooltip:m=!0,overlayContent:n=null,displayOverlayContent:o=!1}){let p=(0,h.useRef)(null),q=gW(0),r=gW(0),s=i1(gW(0),nb),t=i1(gW(0),nb),u=i1(1,nb),v=i1(0),w=i1(0,{stiffness:350,damping:30,mass:1}),[x,y]=(0,h.useState)(0);return(0,g.jsxs)("figure",{ref:p,className:"relative w-full h-full [perspective:800px] flex flex-col items-center justify-center",style:{height:d,width:e},onMouseMove:function(a){if(!p.current)return;let b=p.current.getBoundingClientRect(),c=a.clientX-b.left-b.width/2,d=a.clientY-b.top-b.height/2,e=-(d/(b.height/2)*k),f=c/(b.width/2)*k;s.set(e),t.set(f),q.set(a.clientX-b.left),r.set(a.clientY-b.top);let g=d-x;w.set(-(.6*g)),y(d)},onMouseEnter:function(){u.set(j),v.set(1)},onMouseLeave:function(){v.set(0),u.set(1),s.set(0),t.set(0),w.set(0)},children:[l&&(0,g.jsx)("div",{className:"absolute top-4 text-center text-sm block sm:hidden",children:"This effect is not optimized for mobile. Check on desktop."}),(0,g.jsxs)(m8.div,{className:"relative [transform-style:preserve-3d]",style:{width:i,height:f,rotateX:s,rotateY:t,scale:u},children:[(0,g.jsx)(m8.img,{src:a,alt:b,className:"absolute top-0 left-0 object-cover rounded-[15px] will-change-transform [transform:translateZ(0)]",style:{width:i,height:f}}),o&&n&&(0,g.jsx)(m8.div,{className:"absolute top-0 left-0 z-[2] will-change-transform [transform:translateZ(30px)]",children:n})]}),m&&(0,g.jsx)(m8.figcaption,{className:"pointer-events-none absolute left-0 top-0 rounded-[4px] bg-white px-[10px] py-[4px] text-[10px] text-[#2d2d2d] opacity-0 z-[3] hidden sm:block",style:{x:q,y:r,opacity:v,rotate:w},children:c})]})}function nd(a,b){[...b].reverse().forEach(c=>{let d=a.getVariant(c);d&&kT(a,d),a.variantChildren&&a.variantChildren.forEach(a=>{nd(a,b)})})}function ne(){let a=!1,b=new Set,c={subscribe:a=>(b.add(a),()=>void b.delete(a)),start(c,d){gY(a,"controls.start() should only be called after a component has mounted. Consider calling within a useEffect hook.");let e=[];return b.forEach(a=>{e.push(le(a,c,{transitionOverride:d}))}),Promise.all(e)},set:c=>(gY(a,"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook."),b.forEach(a=>{var b,d;b=a,Array.isArray(d=c)?nd(b,d):"string"==typeof d?nd(b,[d]):kT(b,d)})),stop(){b.forEach(a=>{a.values.forEach(a=>a.stop())})},mount:()=>(a=!0,()=>{a=!1,c.stop()})};return c}let nf=function(){let a=gV(ne);return ii(a.mount,[]),a},ng=(a,b)=>({rotate:((a,b,c=!0)=>({from:b,to:b+360,ease:"linear",duration:a,type:"tween",repeat:c?1/0:0}))(a,b),scale:{type:"spring",damping:20,stiffness:300}}),nh=({text:a,spinDuration:b=20,onHover:c="speedUp",className:d=""})=>{let e=Array.from(a),f=nf(),i=gW(0);return(0,h.useEffect)(()=>{let a=i.get();f.start({rotate:a+360,scale:1,transition:ng(b,a)})},[b,a,c,f]),(0,g.jsx)(m8.div,{className:`m-0 mx-auto rounded-full w-[200px] h-[200px] relative font-black text-center cursor-pointer origin-center ${d}`,style:{rotate:i},initial:{rotate:0},animate:f,onMouseEnter:()=>{let a,d=i.get();if(!c)return;let e=1;switch(c){case"slowDown":a=ng(2*b,d);break;case"speedUp":a=ng(b/4,d);break;case"pause":a={rotate:{type:"spring",damping:20,stiffness:300},scale:{type:"spring",damping:20,stiffness:300}};break;case"goBonkers":a=ng(b/20,d),e=.8;break;default:a=ng(b,d)}f.start({rotate:d+360,scale:e,transition:a})},onMouseLeave:()=>{let a=i.get();f.start({rotate:a+360,scale:1,transition:ng(b,a)})},children:e.map((a,b)=>{let c=360/e.length*b,d=Math.PI/e.length,f=`rotateZ(${c}deg) translate3d(${d*b}px, ${d*b}px, 0)`;return(0,g.jsx)("span",{className:"absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]",style:{transform:f,WebkitTransform:f},children:a},b)})})};function ni({imageSrc:a,altText:b="Abdullah - Full Stack Developer"}){let[c,d]=(0,h.useState)(!1);return(0,g.jsx)("div",{className:"relative w-64 h-64 md:w-80 md:h-80 mx-auto [perspective:1000px]",children:(0,g.jsxs)(m8.div,{className:"relative w-full h-full [transform-style:preserve-3d] cursor-pointer",animate:{rotateY:180*!!c},transition:{duration:.8,ease:"easeInOut"},onClick:()=>{d(!c)},children:[(0,g.jsx)("div",{className:"absolute inset-0 w-full h-full [backface-visibility:hidden]",children:(0,g.jsx)(nc,{imageSrc:a,altText:b,captionText:"you can click it",containerHeight:"100%",containerWidth:"100%",imageHeight:"100%",imageWidth:"100%",scaleOnHover:1.05,rotateAmplitude:12,showMobileWarning:!1,showTooltip:!0})}),(0,g.jsx)("div",{className:"absolute inset-0 w-full h-full [backface-visibility:hidden] [transform:rotateY(180deg)]",children:(0,g.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-slate-50 to-gray-100 border-2 border-gray-200 rounded-3xl flex items-center justify-center shadow-2xl",children:(0,g.jsxs)("div",{className:"relative",children:[(0,g.jsx)(nh,{text:"scroll for more info about me • ",spinDuration:15,onHover:"speedUp",className:"text-gray-700 font-medium"}),(0,g.jsx)("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,g.jsx)("div",{className:"w-16 h-16 bg-gray-200/80 rounded-full flex items-center justify-center border border-gray-300",children:(0,g.jsx)("svg",{className:"w-8 h-8 text-gray-600 animate-bounce",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,g.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 14l-7 7m0 0l-7-7m7 7V3"})})})})]})})})]})})}function nj(){let[a,b]=(0,h.useState)(!1);return(0,g.jsx)("div",{className:"min-h-screen bg-white relative overflow-hidden",children:(0,g.jsx)("div",{className:"container mx-auto px-6 pt-20 pb-20 relative z-10",children:(0,g.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,g.jsxs)("div",{className:"relative mb-8",children:[(0,g.jsx)("div",{className:"absolute inset-0 flex items-center justify-center pointer-events-none opacity-5 z-0 overflow-hidden",children:(0,g.jsx)("div",{className:"w-full",children:(0,g.jsx)(m9,{texts:["make experiences you will never forget"],velocity:25,className:"text-gray-400",parallaxClassName:"w-full",scrollerClassName:"text-3xl md:text-5xl font-bold whitespace-nowrap tracking-wider"})})}),(0,g.jsx)("div",{className:`relative z-20 transition-all duration-1000 delay-300 ${a?"opacity-100 scale-100":"opacity-0 scale-95"}`,children:(0,g.jsx)(ni,{imageSrc:"/profile-placeholder.svg",altText:"Abdullah - Full Stack Developer"})})]}),(0,g.jsx)("div",{className:`mb-6 transition-all duration-1000 delay-500 ${a?"opacity-100":"opacity-0"}`,children:(0,g.jsx)(gB,{text:"Abdullah",className:"text-5xl md:text-7xl font-bold text-black",delay:150,duration:.8,splitType:"chars",from:{opacity:0,y:50,rotateX:-90},to:{opacity:1,y:0,rotateX:0}})}),(0,g.jsx)("div",{className:`mb-12 transition-all duration-1000 delay-700 ${a?"opacity-100":"opacity-0"}`,children:(0,g.jsx)("div",{className:"text-lg md:text-xl text-gray-600 whitespace-nowrap",children:(0,g.jsx)(na,{text:"Full Stack Developer & UI/UX Designer",className:"inline-block",delay:100,animateBy:"words",direction:"bottom"})})}),(0,g.jsx)("div",{className:`flex justify-center gap-4 mb-12 transition-all duration-1000 delay-900 ${a?"opacity-100 translate-y-0":"opacity-0 translate-y-8"}`,children:(0,g.jsxs)("div",{className:"flex items-center gap-3 px-6 py-3 bg-gray-50 rounded-full border border-gray-200 shadow-sm",children:[(0,g.jsxs)("div",{className:"flex items-center gap-1",children:[(0,g.jsx)("span",{className:"text-yellow-500 animate-pulse",children:"⭐"}),(0,g.jsx)("span",{className:"text-yellow-500 animate-pulse delay-150",children:"⭐"}),(0,g.jsx)("span",{className:"text-yellow-500 animate-pulse delay-300",children:"⭐"}),(0,g.jsx)("span",{className:"text-yellow-500 animate-pulse delay-450",children:"⭐"}),(0,g.jsx)("span",{className:"text-yellow-500 animate-pulse delay-600",children:"⭐"})]}),(0,g.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"80+ Happy Clients"})]})}),(0,g.jsx)("div",{className:`transition-all duration-1000 delay-1100 ${a?"opacity-100 translate-y-0":"opacity-0 translate-y-8"}`,children:(0,g.jsx)("button",{className:"bg-gray-900 text-white px-10 py-4 rounded-full font-semibold text-lg hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl border border-gray-800",children:"Let's Work Together!"})})]})})})}},8014:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>j,metadata:()=>i});var d=c(7413),e=c(260),f=c.n(e),g=c(3298),h=c.n(g);c(2704);let i={title:"Create Next App",description:"Generated by create next app"};function j({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:a})})}},8354:a=>{"use strict";a.exports=require("util")},8553:(a,b,c)=>{Promise.resolve().then(c.bind(c,7693))},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9134:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},9282:()=>{},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var b=require("../webpack-runtime.js");b.C(a);var c=b.X(0,[985,546],()=>b(b.s=6351));module.exports=c})();