(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{1057:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>i});var l=t(5155),s=t(2115);function i(){let[e,a]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{a(!0)},[]),(0,l.jsxs)("div",{className:"min-h-screen bg-white relative overflow-hidden",children:[(0,l.jsx)("div",{className:"absolute top-8 left-1/2 transform -translate-x-1/2 w-96 h-16",children:(0,l.jsxs)("svg",{viewBox:"0 0 400 60",className:"w-full h-full transition-all duration-1000 ".concat(e?"opacity-100 scale-100":"opacity-0 scale-95"),children:[(0,l.jsx)("path",{d:"M20 30 Q100 10 200 25 T380 30",stroke:"#000",strokeWidth:"3",fill:"none",className:"animate-pulse"}),(0,l.jsx)("circle",{cx:"150",cy:"20",r:"3",fill:"#ff6b35",className:"animate-bounce"}),(0,l.jsx)("circle",{cx:"250",cy:"35",r:"2",fill:"#ff6b35",className:"animate-bounce delay-300"})]})}),(0,l.jsx)("div",{className:"container mx-auto px-6 pt-32 pb-20 relative z-10",children:(0,l.jsxs)("div",{className:"text-center max-w-4xl mx-auto",children:[(0,l.jsx)("div",{className:"transition-all duration-1000 delay-300 ".concat(e?"opacity-100 translate-y-0":"opacity-0 translate-y-8"),children:(0,l.jsxs)("h1",{className:"text-5xl md:text-6xl font-bold mb-4",children:["Hi, I'm ",(0,l.jsx)("span",{className:"italic text-black",children:"Abdullah"}),"!"]})}),(0,l.jsx)("div",{className:"transition-all duration-1000 delay-500 ".concat(e?"opacity-100 translate-y-0":"opacity-0 translate-y-8"),children:(0,l.jsx)("p",{className:"text-lg md:text-xl text-gray-600 mb-12",children:"Full Stack Developer & UI/UX Designer"})}),(0,l.jsxs)("div",{className:"relative mb-12",children:[(0,l.jsx)("div",{className:"absolute inset-0 flex items-center justify-center pointer-events-none overflow-hidden",children:(0,l.jsx)("div",{className:"text-[12rem] md:text-[20rem] font-black text-gray-100 select-none transition-all duration-1500 delay-700 ".concat(e?"opacity-100 scale-100":"opacity-0 scale-110"),children:"ABDULLAH"})}),(0,l.jsx)("div",{className:"relative z-10 transition-all duration-1000 delay-900 ".concat(e?"opacity-100 scale-100":"opacity-0 scale-95"),children:(0,l.jsx)("div",{className:"w-48 h-48 md:w-64 md:h-64 mx-auto rounded-3xl bg-gradient-to-br from-blue-400 to-purple-500 p-1 shadow-2xl hover:shadow-3xl transition-shadow duration-300",children:(0,l.jsx)("div",{className:"w-full h-full rounded-3xl bg-white flex items-center justify-center overflow-hidden",children:(0,l.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center text-6xl",children:"\uD83D\uDC68‍\uD83D\uDCBB"})})})})]}),(0,l.jsx)("div",{className:"flex justify-center gap-4 mb-12 transition-all duration-1000 delay-1100 ".concat(e?"opacity-100 translate-y-0":"opacity-0 translate-y-8"),children:(0,l.jsxs)("div",{className:"flex items-center gap-2 text-sm text-gray-600",children:[(0,l.jsx)("span",{className:"animate-pulse",children:"\uD83C\uDF89"}),(0,l.jsx)("span",{className:"animate-pulse delay-150",children:"\uD83D\uDE0A"}),(0,l.jsx)("span",{className:"animate-pulse delay-300",children:"\uD83D\uDE80"}),(0,l.jsx)("span",{className:"ml-2",children:"80+ Happy Clients"})]})}),(0,l.jsx)("div",{className:"transition-all duration-1000 delay-1300 ".concat(e?"opacity-100 translate-y-0":"opacity-0 translate-y-8"),children:(0,l.jsx)("button",{className:"bg-black text-white px-8 py-4 rounded-full font-medium text-lg hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl",children:"Let's Work Together!"})})]})}),(0,l.jsx)("div",{className:"absolute bottom-8 right-8 w-32 h-16",children:(0,l.jsx)("svg",{viewBox:"0 0 120 60",className:"w-full h-full transition-all duration-1000 delay-1500 ".concat(e?"opacity-100 rotate-0":"opacity-0 rotate-12"),children:(0,l.jsx)("path",{d:"M10 50 Q30 20 60 40 Q90 60 110 30",stroke:"#000",strokeWidth:"2",fill:"none"})})})]})}},6983:(e,a,t)=>{Promise.resolve().then(t.bind(t,1057))}},e=>{e.O(0,[441,964,358],()=>e(e.s=6983)),_N_E=e.O()}]);