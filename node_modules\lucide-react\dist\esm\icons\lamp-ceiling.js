/**
 * @license lucide-react v0.535.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M12 2v5", key: "nd4vlx" }],
  ["path", { d: "M14.829 15.998a3 3 0 1 1-5.658 0", key: "1pybiy" }],
  [
    "path",
    {
      d: "M20.92 14.606A1 1 0 0 1 20 16H4a1 1 0 0 1-.92-1.394l3-7A1 1 0 0 1 7 7h10a1 1 0 0 1 .92.606z",
      key: "ma1wor"
    }
  ]
];
const LampCeiling = createLucideIcon("lamp-ceiling", __iconNode);

export { __iconNode, LampCeiling as default };
//# sourceMappingURL=lamp-ceiling.js.map
