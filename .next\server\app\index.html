<!DOCTYPE html><!--rG4p2YWMeiqkJ_nZhMptn--><html lang="en"><head><meta charSet="utf-8"/><meta name="viewport" content="width=device-width, initial-scale=1"/><link rel="preload" as="image" href="/profile-placeholder.svg"/><link rel="stylesheet" href="/_next/static/css/653eb371aaa3732e.css" data-precedence="next"/><link rel="preload" as="script" fetchPriority="low" href="/_next/static/chunks/webpack-4c819a731c5a9a00.js"/><script src="/_next/static/chunks/4bd1b696-cf72ae8a39fa05aa.js" async=""></script><script src="/_next/static/chunks/964-02efbd2195ef91bd.js" async=""></script><script src="/_next/static/chunks/main-app-f24600f817acaebe.js" async=""></script><script src="/_next/static/chunks/c15bf2b0-51c91f303884de57.js" async=""></script><script src="/_next/static/chunks/450-f138ac6aa4fdbcc7.js" async=""></script><script src="/_next/static/chunks/app/page-8650cd03d575a448.js" async=""></script><title>Create Next App</title><meta name="description" content="Generated by create next app"/><link rel="icon" href="/favicon.ico" type="image/x-icon" sizes="16x16"/><script src="/_next/static/chunks/polyfills-42372ed130431b0a.js" noModule=""></script></head><body class="__variable_5cfdac __variable_9a8899 antialiased"><div hidden=""><!--$--><!--/$--></div><div class="min-h-screen bg-white relative overflow-hidden"><div class="absolute inset-0 flex items-center justify-center pointer-events-none opacity-10 z-0"><section><div class="w-full relative overflow-hidden"><div class="text-6xl md:text-8xl font-black flex whitespace-nowrap text-center font-sans text-4xl font-bold tracking-[-0.02em] drop-shadow md:text-[5rem] md:leading-[5rem]" style="transform:none"><span class="flex-shrink-0 text-gray-800">make experiences you will never forget<!-- --> </span><span class="flex-shrink-0 text-gray-800">make experiences you will never forget<!-- --> </span><span class="flex-shrink-0 text-gray-800">make experiences you will never forget<!-- --> </span><span class="flex-shrink-0 text-gray-800">make experiences you will never forget<!-- --> </span><span class="flex-shrink-0 text-gray-800">make experiences you will never forget<!-- --> </span><span class="flex-shrink-0 text-gray-800">make experiences you will never forget<!-- --> </span></div></div></section></div><div class="container mx-auto px-6 pt-20 pb-20 relative z-10"><div class="text-center max-w-4xl mx-auto"><div class="mb-8 transition-all duration-1000 delay-300 opacity-0 scale-95"><div class="relative w-64 h-64 md:w-80 md:h-80 mx-auto [perspective:1000px]"><div class="relative w-full h-full [transform-style:preserve-3d] cursor-pointer"><div class="absolute inset-0 w-full h-full [backface-visibility:hidden]"><figure class="relative w-full h-full [perspective:800px] flex flex-col items-center justify-center" style="height:100%;width:100%"><div class="relative [transform-style:preserve-3d]" style="width:100%;height:100%;transform:none"><img src="/profile-placeholder.svg" alt="Abdullah - Full Stack Developer" class="absolute top-0 left-0 object-cover rounded-[15px] will-change-transform [transform:translateZ(0)]" style="width:100%;height:100%"/></div><figcaption class="pointer-events-none absolute left-0 top-0 rounded-[4px] bg-white px-[10px] py-[4px] text-[10px] text-[#2d2d2d] opacity-0 z-[3] hidden sm:block" style="opacity:0;transform:none">you can click it</figcaption></figure></div><div class="absolute inset-0 w-full h-full [backface-visibility:hidden] [transform:rotateY(180deg)]"><div class="w-full h-full bg-gradient-to-br from-blue-400 to-purple-500 rounded-3xl flex items-center justify-center shadow-2xl"><div class="relative"><div class="m-0 mx-auto rounded-full w-[200px] h-[200px] relative font-black text-white text-center cursor-pointer origin-center text-white" style="transform:none"><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(0deg) translate3d(0px, 0px, 0);-webkit-transform:rotateZ(0deg) translate3d(0px, 0px, 0)">s</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(11.25deg) translate3d(0.09817477042468103px, 0.09817477042468103px, 0);-webkit-transform:rotateZ(11.25deg) translate3d(0.09817477042468103px, 0.09817477042468103px, 0)">c</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(22.5deg) translate3d(0.19634954084936207px, 0.19634954084936207px, 0);-webkit-transform:rotateZ(22.5deg) translate3d(0.19634954084936207px, 0.19634954084936207px, 0)">r</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(33.75deg) translate3d(0.2945243112740431px, 0.2945243112740431px, 0);-webkit-transform:rotateZ(33.75deg) translate3d(0.2945243112740431px, 0.2945243112740431px, 0)">o</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(45deg) translate3d(0.39269908169872414px, 0.39269908169872414px, 0);-webkit-transform:rotateZ(45deg) translate3d(0.39269908169872414px, 0.39269908169872414px, 0)">l</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(56.25deg) translate3d(0.4908738521234052px, 0.4908738521234052px, 0);-webkit-transform:rotateZ(56.25deg) translate3d(0.4908738521234052px, 0.4908738521234052px, 0)">l</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(67.5deg) translate3d(0.5890486225480862px, 0.5890486225480862px, 0);-webkit-transform:rotateZ(67.5deg) translate3d(0.5890486225480862px, 0.5890486225480862px, 0)"> </span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(78.75deg) translate3d(0.6872233929727672px, 0.6872233929727672px, 0);-webkit-transform:rotateZ(78.75deg) translate3d(0.6872233929727672px, 0.6872233929727672px, 0)">f</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(90deg) translate3d(0.7853981633974483px, 0.7853981633974483px, 0);-webkit-transform:rotateZ(90deg) translate3d(0.7853981633974483px, 0.7853981633974483px, 0)">o</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(101.25deg) translate3d(0.8835729338221293px, 0.8835729338221293px, 0);-webkit-transform:rotateZ(101.25deg) translate3d(0.8835729338221293px, 0.8835729338221293px, 0)">r</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(112.5deg) translate3d(0.9817477042468103px, 0.9817477042468103px, 0);-webkit-transform:rotateZ(112.5deg) translate3d(0.9817477042468103px, 0.9817477042468103px, 0)"> </span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(123.75deg) translate3d(1.0799224746714913px, 1.0799224746714913px, 0);-webkit-transform:rotateZ(123.75deg) translate3d(1.0799224746714913px, 1.0799224746714913px, 0)">m</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(135deg) translate3d(1.1780972450961724px, 1.1780972450961724px, 0);-webkit-transform:rotateZ(135deg) translate3d(1.1780972450961724px, 1.1780972450961724px, 0)">o</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(146.25deg) translate3d(1.2762720155208536px, 1.2762720155208536px, 0);-webkit-transform:rotateZ(146.25deg) translate3d(1.2762720155208536px, 1.2762720155208536px, 0)">r</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(157.5deg) translate3d(1.3744467859455345px, 1.3744467859455345px, 0);-webkit-transform:rotateZ(157.5deg) translate3d(1.3744467859455345px, 1.3744467859455345px, 0)">e</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(168.75deg) translate3d(1.4726215563702154px, 1.4726215563702154px, 0);-webkit-transform:rotateZ(168.75deg) translate3d(1.4726215563702154px, 1.4726215563702154px, 0)"> </span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(180deg) translate3d(1.5707963267948966px, 1.5707963267948966px, 0);-webkit-transform:rotateZ(180deg) translate3d(1.5707963267948966px, 1.5707963267948966px, 0)">i</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(191.25deg) translate3d(1.6689710972195777px, 1.6689710972195777px, 0);-webkit-transform:rotateZ(191.25deg) translate3d(1.6689710972195777px, 1.6689710972195777px, 0)">n</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(202.5deg) translate3d(1.7671458676442586px, 1.7671458676442586px, 0);-webkit-transform:rotateZ(202.5deg) translate3d(1.7671458676442586px, 1.7671458676442586px, 0)">f</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(213.75deg) translate3d(1.8653206380689396px, 1.8653206380689396px, 0);-webkit-transform:rotateZ(213.75deg) translate3d(1.8653206380689396px, 1.8653206380689396px, 0)">o</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(225deg) translate3d(1.9634954084936207px, 1.9634954084936207px, 0);-webkit-transform:rotateZ(225deg) translate3d(1.9634954084936207px, 1.9634954084936207px, 0)"> </span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(236.25deg) translate3d(2.061670178918302px, 2.061670178918302px, 0);-webkit-transform:rotateZ(236.25deg) translate3d(2.061670178918302px, 2.061670178918302px, 0)">a</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(247.5deg) translate3d(2.1598449493429825px, 2.1598449493429825px, 0);-webkit-transform:rotateZ(247.5deg) translate3d(2.1598449493429825px, 2.1598449493429825px, 0)">b</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(258.75deg) translate3d(2.2580197197676637px, 2.2580197197676637px, 0);-webkit-transform:rotateZ(258.75deg) translate3d(2.2580197197676637px, 2.2580197197676637px, 0)">o</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(270deg) translate3d(2.356194490192345px, 2.356194490192345px, 0);-webkit-transform:rotateZ(270deg) translate3d(2.356194490192345px, 2.356194490192345px, 0)">u</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(281.25deg) translate3d(2.454369260617026px, 2.454369260617026px, 0);-webkit-transform:rotateZ(281.25deg) translate3d(2.454369260617026px, 2.454369260617026px, 0)">t</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(292.5deg) translate3d(2.552544031041707px, 2.552544031041707px, 0);-webkit-transform:rotateZ(292.5deg) translate3d(2.552544031041707px, 2.552544031041707px, 0)"> </span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(303.75deg) translate3d(2.650718801466388px, 2.650718801466388px, 0);-webkit-transform:rotateZ(303.75deg) translate3d(2.650718801466388px, 2.650718801466388px, 0)">m</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(315deg) translate3d(2.748893571891069px, 2.748893571891069px, 0);-webkit-transform:rotateZ(315deg) translate3d(2.748893571891069px, 2.748893571891069px, 0)">e</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(326.25deg) translate3d(2.84706834231575px, 2.84706834231575px, 0);-webkit-transform:rotateZ(326.25deg) translate3d(2.84706834231575px, 2.84706834231575px, 0)"> </span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(337.5deg) translate3d(2.945243112740431px, 2.945243112740431px, 0);-webkit-transform:rotateZ(337.5deg) translate3d(2.945243112740431px, 2.945243112740431px, 0)">•</span><span class="absolute inline-block inset-0 text-2xl transition-all duration-500 ease-[cubic-bezier(0,0,0,1)]" style="transform:rotateZ(348.75deg) translate3d(3.043417883165112px, 3.043417883165112px, 0);-webkit-transform:rotateZ(348.75deg) translate3d(3.043417883165112px, 3.043417883165112px, 0)"> </span></div><div class="absolute inset-0 flex items-center justify-center"><div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center"><svg class="w-8 h-8 text-white animate-bounce" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path></svg></div></div></div></div></div></div></div></div><div class="mb-6 transition-all duration-1000 delay-500 opacity-0"><p class="split-parent overflow-hidden inline-block whitespace-normal text-5xl md:text-7xl font-bold text-black" style="text-align:center;word-wrap:break-word">Abdullah</p></div><div class="mb-12 transition-all duration-1000 delay-700 opacity-0"><p class="blur-text text-lg md:text-xl text-gray-600 flex flex-wrap"><span style="display:inline-block;will-change:transform, filter, opacity;filter:blur(10px);opacity:0;transform:translateY(50px)">Full<!-- --> </span><span style="display:inline-block;will-change:transform, filter, opacity;filter:blur(10px);opacity:0;transform:translateY(50px)">Stack<!-- --> </span><span style="display:inline-block;will-change:transform, filter, opacity;filter:blur(10px);opacity:0;transform:translateY(50px)">Developer<!-- --> </span><span style="display:inline-block;will-change:transform, filter, opacity;filter:blur(10px);opacity:0;transform:translateY(50px)">&amp;<!-- --> </span><span style="display:inline-block;will-change:transform, filter, opacity;filter:blur(10px);opacity:0;transform:translateY(50px)">UI/UX<!-- --> </span><span style="display:inline-block;will-change:transform, filter, opacity;filter:blur(10px);opacity:0;transform:translateY(50px)">Designer</span></p></div><div class="flex justify-center gap-4 mb-12 transition-all duration-1000 delay-900 opacity-0 translate-y-8"><div class="flex items-center gap-2 text-sm text-gray-600"><span class="animate-pulse">🎉</span><span class="animate-pulse delay-150">😊</span><span class="animate-pulse delay-300">🚀</span><span class="ml-2">80+ Happy Clients</span></div></div><div class="transition-all duration-1000 delay-1100 opacity-0 translate-y-8"><button class="bg-black text-white px-8 py-4 rounded-full font-medium text-lg hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">Let&#x27;s Work Together!</button></div></div></div></div><!--$--><!--/$--><script src="/_next/static/chunks/webpack-4c819a731c5a9a00.js" id="_R_" async=""></script><script>(self.__next_f=self.__next_f||[]).push([0])</script><script>self.__next_f.push([1,"1:\"$Sreact.fragment\"\n2:I[7555,[],\"\"]\n3:I[1295,[],\"\"]\n4:I[894,[],\"ClientPageRoot\"]\n5:I[4957,[\"592\",\"static/chunks/c15bf2b0-51c91f303884de57.js\",\"450\",\"static/chunks/450-f138ac6aa4fdbcc7.js\",\"974\",\"static/chunks/app/page-8650cd03d575a448.js\"],\"default\"]\n8:I[9665,[],\"OutletBoundary\"]\na:I[4911,[],\"AsyncMetadataOutlet\"]\nc:I[9665,[],\"ViewportBoundary\"]\ne:I[9665,[],\"MetadataBoundary\"]\nf:\"$Sreact.suspense\"\n11:I[8393,[],\"\"]\n:HL[\"/_next/static/css/653eb371aaa3732e.css\",\"style\"]\n"])</script><script>self.__next_f.push([1,"0:{\"P\":null,\"b\":\"rG4p2YWMeiqkJ-nZhMptn\",\"p\":\"\",\"c\":[\"\",\"\"],\"i\":false,\"f\":[[[\"\",{\"children\":[\"__PAGE__\",{}]},\"$undefined\",\"$undefined\",true],[\"\",[\"$\",\"$1\",\"c\",{\"children\":[[[\"$\",\"link\",\"0\",{\"rel\":\"stylesheet\",\"href\":\"/_next/static/css/653eb371aaa3732e.css\",\"precedence\":\"next\",\"crossOrigin\":\"$undefined\",\"nonce\":\"$undefined\"}]],[\"$\",\"html\",null,{\"lang\":\"en\",\"children\":[\"$\",\"body\",null,{\"className\":\"__variable_5cfdac __variable_9a8899 antialiased\",\"children\":[\"$\",\"$L2\",null,{\"parallelRouterKey\":\"children\",\"error\":\"$undefined\",\"errorStyles\":\"$undefined\",\"errorScripts\":\"$undefined\",\"template\":[\"$\",\"$L3\",null,{}],\"templateStyles\":\"$undefined\",\"templateScripts\":\"$undefined\",\"notFound\":[[[\"$\",\"title\",null,{\"children\":\"404: This page could not be found.\"}],[\"$\",\"div\",null,{\"style\":{\"fontFamily\":\"system-ui,\\\"Segoe UI\\\",Roboto,Helvetica,Arial,sans-serif,\\\"Apple Color Emoji\\\",\\\"Segoe UI Emoji\\\"\",\"height\":\"100vh\",\"textAlign\":\"center\",\"display\":\"flex\",\"flexDirection\":\"column\",\"alignItems\":\"center\",\"justifyContent\":\"center\"},\"children\":[\"$\",\"div\",null,{\"children\":[[\"$\",\"style\",null,{\"dangerouslySetInnerHTML\":{\"__html\":\"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}\"}}],[\"$\",\"h1\",null,{\"className\":\"next-error-h1\",\"style\":{\"display\":\"inline-block\",\"margin\":\"0 20px 0 0\",\"padding\":\"0 23px 0 0\",\"fontSize\":24,\"fontWeight\":500,\"verticalAlign\":\"top\",\"lineHeight\":\"49px\"},\"children\":404}],[\"$\",\"div\",null,{\"style\":{\"display\":\"inline-block\"},\"children\":[\"$\",\"h2\",null,{\"style\":{\"fontSize\":14,\"fontWeight\":400,\"lineHeight\":\"49px\",\"margin\":0},\"children\":\"This page could not be found.\"}]}]]}]}]],[]],\"forbidden\":\"$undefined\",\"unauthorized\":\"$undefined\"}]}]}]]}],{\"children\":[\"__PAGE__\",[\"$\",\"$1\",\"c\",{\"children\":[[\"$\",\"$L4\",null,{\"Component\":\"$5\",\"searchParams\":{},\"params\":{},\"promises\":[\"$@6\",\"$@7\"]}],null,[\"$\",\"$L8\",null,{\"children\":[\"$L9\",[\"$\",\"$La\",null,{\"promise\":\"$@b\"}]]}]]}],{},null,false]},null,false],[\"$\",\"$1\",\"h\",{\"children\":[null,[[\"$\",\"$Lc\",null,{\"children\":\"$Ld\"}],null],[\"$\",\"$Le\",null,{\"children\":[\"$\",\"div\",null,{\"hidden\":true,\"children\":[\"$\",\"$f\",null,{\"fallback\":null,\"children\":\"$L10\"}]}]}]]}],false]],\"m\":\"$undefined\",\"G\":[\"$11\",[]],\"s\":false,\"S\":true}\n"])</script><script>self.__next_f.push([1,"6:{}\n7:\"$0:f:0:1:2:children:1:props:children:0:props:params\"\n"])</script><script>self.__next_f.push([1,"d:[[\"$\",\"meta\",\"0\",{\"charSet\":\"utf-8\"}],[\"$\",\"meta\",\"1\",{\"name\":\"viewport\",\"content\":\"width=device-width, initial-scale=1\"}]]\n9:null\n"])</script><script>self.__next_f.push([1,"12:I[8175,[],\"IconMark\"]\nb:{\"metadata\":[[\"$\",\"title\",\"0\",{\"children\":\"Create Next App\"}],[\"$\",\"meta\",\"1\",{\"name\":\"description\",\"content\":\"Generated by create next app\"}],[\"$\",\"link\",\"2\",{\"rel\":\"icon\",\"href\":\"/favicon.ico\",\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}],[\"$\",\"$L12\",\"3\",{}]],\"error\":null,\"digest\":\"$undefined\"}\n"])</script><script>self.__next_f.push([1,"10:\"$b:metadata\"\n"])</script></body></html>