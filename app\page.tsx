'use client';

import Image from "next/image";
import { useEffect, useState } from "react";

export default function Home() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Decorative top scribble */}
      <div className="absolute top-8 left-1/2 transform -translate-x-1/2 w-96 h-16">
        <svg
          viewBox="0 0 400 60"
          className={`w-full h-full transition-all duration-1000 ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}
        >
          <path
            d="M20 30 Q100 10 200 25 T380 30"
            stroke="#000"
            strokeWidth="3"
            fill="none"
            className="animate-pulse"
          />
          <circle cx="150" cy="20" r="3" fill="#ff6b35" className="animate-bounce" />
          <circle cx="250" cy="35" r="2" fill="#ff6b35" className="animate-bounce delay-300" />
        </svg>
      </div>

      {/* Main hero content */}
      <div className="container mx-auto px-6 pt-32 pb-20 relative z-10">
        <div className="text-center max-w-4xl mx-auto">
          {/* Main heading */}
          <div className={`transition-all duration-1000 delay-300 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <h1 className="text-5xl md:text-6xl font-bold mb-4">
              Hi, I'm <span className="italic text-black">Abdullah</span>!
            </h1>
          </div>

          {/* Subtitle */}
          <div className={`transition-all duration-1000 delay-500 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <p className="text-lg md:text-xl text-gray-600 mb-12">
              Full Stack Developer & UI/UX Designer
            </p>
          </div>

          {/* Profile image with background text */}
          <div className="relative mb-12">
            {/* Background large text */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none overflow-hidden">
              <div className={`text-[12rem] md:text-[20rem] font-black text-gray-100 select-none transition-all duration-1500 delay-700 ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-110'}`}>
                ABDULLAH
              </div>
            </div>

            {/* Profile image */}
            <div className={`relative z-10 transition-all duration-1000 delay-900 ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}>
              <div className="w-48 h-48 md:w-64 md:h-64 mx-auto rounded-3xl bg-gradient-to-br from-blue-400 to-purple-500 p-1 shadow-2xl hover:shadow-3xl transition-shadow duration-300">
                <div className="w-full h-full rounded-3xl bg-white flex items-center justify-center overflow-hidden">
                  {/* Placeholder for profile image - you can replace this with your actual image */}
                  <div className="w-full h-full bg-gradient-to-br from-gray-200 to-gray-300 flex items-center justify-center text-6xl">
                    👨‍💻
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Social icons */}
          <div className={`flex justify-center gap-4 mb-12 transition-all duration-1000 delay-1100 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span className="animate-pulse">🎉</span>
              <span className="animate-pulse delay-150">😊</span>
              <span className="animate-pulse delay-300">🚀</span>
              <span className="ml-2">80+ Happy Clients</span>
            </div>
          </div>

          {/* CTA Button */}
          <div className={`transition-all duration-1000 delay-1300 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <button className="bg-black text-white px-8 py-4 rounded-full font-medium text-lg hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
              Let's Work Together!
            </button>
          </div>
        </div>
      </div>

      {/* Decorative bottom scribble */}
      <div className="absolute bottom-8 right-8 w-32 h-16">
        <svg
          viewBox="0 0 120 60"
          className={`w-full h-full transition-all duration-1000 delay-1500 ${isLoaded ? 'opacity-100 rotate-0' : 'opacity-0 rotate-12'}`}
        >
          <path
            d="M10 50 Q30 20 60 40 Q90 60 110 30"
            stroke="#000"
            strokeWidth="2"
            fill="none"
          />
        </svg>
      </div>
    </div>
  );
}
