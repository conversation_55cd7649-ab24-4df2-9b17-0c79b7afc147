'use client';

import { useEffect, useState } from "react";
import SplitText from "../components/SplitText/SplitText";
import ScrollVelocity from "../components/ScrollVelocity/ScrollVelocity";
import BlurText from "../components/BlurText/BlurText";
import FlippableProfileCard from "../components/FlippableProfileCard";

export default function Home() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Main hero content */}
      <div className="container mx-auto px-6 pt-20 pb-20 relative z-10">
        <div className="text-center max-w-4xl mx-auto">

          {/* Profile Card with Background Text Behind It */}
          <div className="relative mb-8">
            {/* Background Scrolling Text - positioned behind profile card */}
            <div className="absolute inset-0 flex items-center justify-center pointer-events-none opacity-8 z-0 overflow-hidden">
              <div className="w-full">
                <ScrollVelocity
                  texts={["make experiences you will never forget"]}
                  velocity={30}
                  className="text-gray-300"
                  parallaxClassName="w-full"
                  scrollerClassName="text-4xl md:text-6xl font-black whitespace-nowrap"
                />
              </div>
            </div>

            {/* Profile Card */}
            <div className={`relative z-20 transition-all duration-1000 delay-300 ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}>
              <FlippableProfileCard
                imageSrc="/profile-placeholder.svg"
                altText="Abdullah - Full Stack Developer"
              />
            </div>
          </div>

          {/* Name with Split Text Animation */}
          <div className={`mb-6 transition-all duration-1000 delay-500 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>
            <SplitText
              text="Abdullah"
              className="text-5xl md:text-7xl font-bold text-black"
              delay={150}
              duration={0.8}
              splitType="chars"
              from={{ opacity: 0, y: 50, rotateX: -90 }}
              to={{ opacity: 1, y: 0, rotateX: 0 }}
            />
          </div>

          {/* Subtitle with Blur Text Animation - Fixed to stay on one line */}
          <div className={`mb-12 transition-all duration-1000 delay-700 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>
            <div className="text-lg md:text-xl text-gray-600 whitespace-nowrap">
              <BlurText
                text="Full Stack Developer & UI/UX Designer"
                className="inline-block"
                delay={100}
                animateBy="words"
                direction="bottom"
              />
            </div>
          </div>

          {/* Social proof */}
          <div className={`flex justify-center gap-4 mb-12 transition-all duration-1000 delay-900 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span className="animate-pulse">🎉</span>
              <span className="animate-pulse delay-150">😊</span>
              <span className="animate-pulse delay-300">🚀</span>
              <span className="ml-2">80+ Happy Clients</span>
            </div>
          </div>

          {/* CTA Button */}
          <div className={`transition-all duration-1000 delay-1100 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <button className="bg-black text-white px-8 py-4 rounded-full font-medium text-lg hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
              Let's Work Together!
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
