'use client';

import { useEffect, useState } from "react";
import SplitText from "../components/SplitText/SplitText";
import ScrollVelocity from "../components/ScrollVelocity/ScrollVelocity";
import BlurText from "../components/BlurText/BlurText";
import FlippableProfileCard from "../components/FlippableProfileCard";
import { AnimatedTooltip } from "../components/ui/animated-tooltip";
import Dock from "../components/Dock/Dock";

export default function Home() {
  const [isLoaded, setIsLoaded] = useState(false);

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  // Happy clients data for animated tooltip - using placeholder avatars
  const happyClients = [
    {
      id: 1,
      name: "<PERSON>",
      designation: "CEO at TechCorp",
      image: "https://api.dicebear.com/7.x/avataaars/svg?seed=Sarah&backgroundColor=b6e3f4&clothesColor=262e33",
    },
    {
      id: 2,
      name: "<PERSON>",
      designation: "CTO at StartupXYZ",
      image: "https://api.dicebear.com/7.x/avataaars/svg?seed=Michael&backgroundColor=c0aede&clothesColor=3c4f5c",
    },
    {
      id: 3,
      name: "Emily Rodriguez",
      designation: "Designer at Creative Co",
      image: "https://api.dicebear.com/7.x/avataaars/svg?seed=Emily&backgroundColor=ffd93d&clothesColor=6bd9e9",
    },
    {
      id: 4,
      name: "David Kim",
      designation: "Founder at InnovateLab",
      image: "https://api.dicebear.com/7.x/avataaars/svg?seed=David&backgroundColor=ffb3ba&clothesColor=74b9ff",
    },
  ];

  // Dock navigation items
  const dockItems = [
    {
      icon: (
        <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      ),
      label: "Home",
      onClick: () => console.log("Home clicked"),
    },
    {
      icon: (
        <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      ),
      label: "About",
      onClick: () => console.log("About clicked"),
    },
    {
      icon: (
        <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      label: "Projects",
      onClick: () => console.log("Projects clicked"),
    },
    {
      icon: (
        <svg className="w-6 h-6 text-gray-700" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
        </svg>
      ),
      label: "Contact",
      onClick: () => console.log("Contact clicked"),
    },
  ];

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Full Background Scrolling Text */}
      <div className="fixed inset-0 flex items-center justify-center pointer-events-none z-0">
        <div className="opacity-5">
          <ScrollVelocity
            texts={["make experiences you will never forget"]}
            velocity={30}
            className="text-gray-400"
            parallaxClassName="w-full"
            scrollerClassName="text-8xl md:text-[12rem] font-black whitespace-nowrap tracking-widest"
          />
        </div>
      </div>

      {/* Main hero content */}
      <div className="container mx-auto px-6 pt-20 pb-32 relative z-10">
        <div className="text-center max-w-4xl mx-auto">

          {/* Profile Card */}
          <div className="relative mb-8">
            <div className={`relative z-20 transition-all duration-1000 delay-300 ${isLoaded ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`}>
              <FlippableProfileCard
                imageSrc="/profile-placeholder.svg"
                altText="Abdullah - Full Stack Developer"
              />
            </div>
          </div>

          {/* Name with Split Text Animation */}
          <div className={`mb-6 transition-all duration-1000 delay-500 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>
            <SplitText
              text="Abdullah"
              className="text-5xl md:text-7xl font-bold text-black"
              delay={150}
              duration={0.8}
              splitType="chars"
              from={{ opacity: 0, y: 50, rotateX: -90 }}
              to={{ opacity: 1, y: 0, rotateX: 0 }}
            />
          </div>

          {/* Subtitle with Blur Text Animation - Fixed to stay on one line */}
          <div className={`mb-12 transition-all duration-1000 delay-700 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}>
            <div className="text-lg md:text-xl text-gray-600 whitespace-nowrap">
              <BlurText
                text="Full Stack Developer & UI/UX Designer"
                className="inline-block"
                delay={100}
                animateBy="words"
                direction="bottom"
              />
            </div>
          </div>

          {/* Clean Client Avatars */}
          <div className={`flex justify-center mb-12 transition-all duration-1000 delay-900 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <div className="flex items-center">
              <AnimatedTooltip items={happyClients} />
            </div>
          </div>

          {/* CTA Button */}
          <div className={`transition-all duration-1000 delay-1100 ${isLoaded ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'}`}>
            <button className="bg-black text-white px-10 py-4 rounded-full font-semibold text-lg hover:bg-gray-800 transition-all duration-300 hover:scale-105 shadow-lg hover:shadow-xl">
              Let's Work Together!
            </button>
          </div>
        </div>
      </div>

      {/* Bottom Dock Navigation */}
      <div className="fixed bottom-0 left-0 right-0 z-50">
        <Dock
          items={dockItems}
          magnification={70}
          distance={120}
          baseItemSize={48}
          panelHeight={60}
        />
      </div>
    </div>
  );
}
