"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[450],{10:(t,e,i)=>{i.d(e,{V:()=>h,f:()=>f});var n=i(4272);let r=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu;var s=i(614),o=i(1557);let a="number",l="color",u=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function h(t){let e=t.toString(),i=[],r={color:[],number:[],var:[]},s=[],o=0,h=e.replace(u,t=>(n.y.test(t)?(r.color.push(o),s.push(l),i.push(n.y.parse(t))):t.startsWith("var(")?(r.var.push(o),s.push("var"),i.push(t)):(r.number.push(o),s.push(a),i.push(parseFloat(t))),++o,"${}")).split("${}");return{values:i,split:h,indexes:r,types:s}}function c(t){return h(t).values}function d(t){let{split:e,types:i}=h(t),r=e.length;return t=>{let s="";for(let u=0;u<r;u++)if(s+=e[u],void 0!==t[u]){let e=i[u];e===a?s+=(0,o.a)(t[u]):e===l?s+=n.y.transform(t[u]):s+=t[u]}return s}}let p=t=>"number"==typeof t?0:n.y.test(t)?n.y.getAnimatableNone(t):t,f={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(s.S)?.length||0)+(t.match(r)?.length||0)>0},parse:c,createTransformer:d,getAnimatableNone:function(t){let e=c(t);return d(t)(e.map(p))}}},18:(t,e,i)=>{i.d(e,{U:()=>n,f:()=>r});let n=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],r=new Set(n)},98:(t,e,i)=>{i.d(e,{OQ:()=>u,bt:()=>a});var n=i(5626),r=i(2923),s=i(4261),o=i(9515);let a={current:void 0};class l{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=t=>{let e=s.k.now();if(this.updatedAt!==e&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty()},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=s.k.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=!isNaN(parseFloat(this.current)))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new n.v);let i=this.events[t].add(e);return"change"===t?()=>{i(),o.Gt.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t){this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return a.current&&a.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){let t=s.k.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||t-this.updatedAt>30)return 0;let e=Math.min(this.updatedAt-this.prevUpdatedAt,30);return(0,r.f)(parseFloat(this.current)-parseFloat(this.prevFrameValue),e)}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function u(t,e){return new l(t,e)}},144:(t,e,i)=>{i.d(e,{E:()=>a});var n=i(6330),r=i(8467),s=i(2886);let o={decay:n.B,inertia:n.B,tween:r.i,keyframes:r.i,spring:s.o};function a(t){"string"==typeof t.type&&(t.type=o[t.type])}},280:(t,e,i)=>{i.d(e,{E4:()=>a,Hr:()=>c,W9:()=>h});var n=i(4160),r=i(18),s=i(7887),o=i(4158);let a=t=>t===s.ai||t===o.px,l=new Set(["x","y","z"]),u=r.U.filter(t=>!l.has(t));function h(t){let e=[];return u.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(+!!i.startsWith("scale")))}),e}let c={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>(0,n.ry)(e,"x"),y:(t,{transform:e})=>(0,n.ry)(e,"y")};c.translateX=c.x,c.translateY=c.y},419:(t,e,i)=>{i.d(e,{K:()=>r});var n=i(2735);function r(t,e,i){let r=t.getProps();return(0,n.a)(r,e,void 0!==i?i:r.custom,t)}},532:(t,e,i)=>{i.d(e,{s:()=>y});var n=i(3191),r=i(1297),s=i(7215),o=i(4261),a=i(3704),l=i(6087),u=i(9515);let h=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>u.Gt.update(e,t),stop:()=>(0,u.WG)(e),now:()=>u.uv.isProcessing?u.uv.timestamp:o.k.now()}};var c=i(6330),d=i(8467),p=i(2458),f=i(6778),m=i(144),g=i(1513);let v=t=>t/100;class y extends g.q{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==o.k.now()&&this.tick(o.k.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},a.q.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;(0,m.E)(t);let{type:e=d.i,repeat:i=0,repeatDelay:r=0,repeatType:s,velocity:o=0}=t,{keyframes:a}=t,u=e||d.i;u!==d.i&&"number"!=typeof a[0]&&(this.mixKeyframes=(0,n.F)(v,(0,l.j)(a[0],a[1])),a=[0,100]);let h=u({...t,keyframes:a});"mirror"===s&&(this.mirroredGenerator=u({...t,keyframes:[...a].reverse(),velocity:-o})),null===h.calculatedDuration&&(h.calculatedDuration=(0,p.t)(h));let{calculatedDuration:c}=h;this.calculatedDuration=c,this.resolvedDuration=c+r,this.totalDuration=this.resolvedDuration*(i+1)-r,this.generator=h}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:n,mixKeyframes:s,mirroredGenerator:o,resolvedDuration:a,calculatedDuration:l}=this;if(null===this.startTime)return i.next(0);let{delay:u=0,keyframes:h,repeat:d,repeatType:p,repeatDelay:m,type:g,onUpdate:v,finalKeyframe:y}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-n/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let x=this.currentTime-u*(this.playbackSpeed>=0?1:-1),b=this.playbackSpeed>=0?x<0:x>n;this.currentTime=Math.max(x,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=n);let w=this.currentTime,T=i;if(d){let t=Math.min(this.currentTime,n)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,d+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/a)):"mirror"===p&&(T=o)),w=(0,r.q)(0,1,i)*a}let P=b?{done:!1,value:h[0]}:T.next(w);s&&(P.value=s(P.value));let{done:S}=P;b||null===l||(S=this.playbackSpeed>=0?this.currentTime>=n:this.currentTime<=0);let E=null===this.holdTime&&("finished"===this.state||"running"===this.state&&S);return E&&g!==c.B&&(P.value=(0,f.X)(h,this.options,y,this.speed)),v&&v(P.value),E&&this.finish(),P}then(t,e){return this.finished.then(t,e)}get duration(){return(0,s.X)(this.calculatedDuration)}get time(){return(0,s.X)(this.currentTime)}set time(t){t=(0,s.f)(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(o.k.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=(0,s.X)(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=h,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(o.k.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,a.q.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}},614:(t,e,i)=>{i.d(e,{S:()=>n});let n=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu},802:(t,e,i)=>{i.d(e,{os:()=>tb});var n,r,s,o,a,l,u,h=i(934),c={},d=180/Math.PI,p=Math.PI/180,f=Math.atan2,m=/([A-Z])/g,g=/(left|right|width|margin|padding|x)/i,v=/[\s,\(]\S/,y={autoAlpha:"opacity,visibility",scale:"scaleX,scaleY",alpha:"opacity"},x=function(t,e){return e.set(e.t,e.p,Math.round((e.s+e.c*t)*1e4)/1e4+e.u,e)},b=function(t,e){return e.set(e.t,e.p,1===t?e.e:Math.round((e.s+e.c*t)*1e4)/1e4+e.u,e)},w=function(t,e){return e.set(e.t,e.p,t?Math.round((e.s+e.c*t)*1e4)/1e4+e.u:e.b,e)},T=function(t,e){var i=e.s+e.c*t;e.set(e.t,e.p,~~(i+(i<0?-.5:.5))+e.u,e)},P=function(t,e){return e.set(e.t,e.p,t?e.e:e.b,e)},S=function(t,e){return e.set(e.t,e.p,1!==t?e.b:e.e,e)},E=function(t,e,i){return t.style[e]=i},A=function(t,e,i){return t.style.setProperty(e,i)},M=function(t,e,i){return t._gsap[e]=i},C=function(t,e,i){return t._gsap.scaleX=t._gsap.scaleY=i},k=function(t,e,i,n,r){var s=t._gsap;s.scaleX=s.scaleY=i,s.renderTransform(r,s)},V=function(t,e,i,n,r){var s=t._gsap;s[e]=i,s.renderTransform(r,s)},O="transform",D=O+"Origin",R=function t(e,i){var n=this,r=this.target,s=r.style,o=r._gsap;if(e in c&&s){if(this.tfm=this.tfm||{},"transform"===e)return y.transform.split(",").forEach(function(e){return t.call(n,e,i)});if(~(e=y[e]||e).indexOf(",")?e.split(",").forEach(function(t){return n.tfm[t]=Q(r,t)}):this.tfm[e]=o.x?o[e]:Q(r,e),e===D&&(this.tfm.zOrigin=o.zOrigin),this.props.indexOf(O)>=0)return;o.svg&&(this.svgo=r.getAttribute("data-svg-origin"),this.props.push(D,i,"")),e=O}(s||i)&&this.props.push(e,i,s[e])},_=function(t){t.translate&&(t.removeProperty("translate"),t.removeProperty("scale"),t.removeProperty("rotate"))},B=function(){var t,e,i=this.props,n=this.target,r=n.style,s=n._gsap;for(t=0;t<i.length;t+=3)i[t+1]?2===i[t+1]?n[i[t]](i[t+2]):n[i[t]]=i[t+2]:i[t+2]?r[i[t]]=i[t+2]:r.removeProperty("--"===i[t].substr(0,2)?i[t]:i[t].replace(m,"-$1").toLowerCase());if(this.tfm){for(e in this.tfm)s[e]=this.tfm[e];s.svg&&(s.renderTransform(),n.setAttribute("data-svg-origin",this.svgo||"")),(t=l())&&t.isStart||r[O]||(_(r),s.zOrigin&&r[D]&&(r[D]+=" "+s.zOrigin+"px",s.zOrigin=0,s.renderTransform()),s.uncache=1)}},L=function(t,e){var i={target:t,props:[],revert:B,save:R};return t._gsap||h.os.core.getCache(t),e&&t.style&&t.nodeType&&e.split(",").forEach(function(t){return i.save(t)}),i},F=function(t,e){var i=n.createElementNS?n.createElementNS((e||"http://www.w3.org/1999/xhtml").replace(/^https/,"http"),t):n.createElement(t);return i&&i.style?i:n.createElement(t)},j=function t(e,i,n){var r=getComputedStyle(e);return r[i]||r.getPropertyValue(i.replace(m,"-$1").toLowerCase())||r.getPropertyValue(i)||!n&&t(e,N(i)||i,1)||""},I="O,Moz,ms,Ms,Webkit".split(","),N=function(t,e,i){var n=(e||o).style,r=5;if(t in n&&!i)return t;for(t=t.charAt(0).toUpperCase()+t.substr(1);r--&&!(I[r]+t in n););return r<0?null:(3===r?"ms":r>=0?I[r]:"")+t},W=function(){"undefined"!=typeof window&&window.document&&(r=(n=window.document).documentElement,o=F("div")||{style:{}},F("div"),D=(O=N(O))+"Origin",o.style.cssText="border-width:0;line-height:0;position:absolute;padding:0",u=!!N("perspective"),l=h.os.core.reverting,s=1)},Y=function(t){var e,i=t.ownerSVGElement,n=F("svg",i&&i.getAttribute("xmlns")||"http://www.w3.org/2000/svg"),s=t.cloneNode(!0);s.style.display="block",n.appendChild(s),r.appendChild(n);try{e=s.getBBox()}catch(t){}return n.removeChild(s),r.removeChild(n),e},z=function(t,e){for(var i=e.length;i--;)if(t.hasAttribute(e[i]))return t.getAttribute(e[i])},X=function(t){var e,i;try{e=t.getBBox()}catch(n){e=Y(t),i=1}return e&&(e.width||e.height)||i||(e=Y(t)),!e||e.width||e.x||e.y?e:{x:+z(t,["x","cx","x1"])||0,y:+z(t,["y","cy","y1"])||0,width:0,height:0}},U=function(t){return!!(t.getCTM&&(!t.parentNode||t.ownerSVGElement)&&X(t))},G=function(t,e){if(e){var i,n=t.style;e in c&&e!==D&&(e=O),n.removeProperty?(("ms"===(i=e.substr(0,2))||"webkit"===e.substr(0,6))&&(e="-"+e),n.removeProperty("--"===i?e:e.replace(m,"-$1").toLowerCase())):n.removeAttribute(e)}},q=function(t,e,i,n,r,s){var o=new h.J7(t._pt,e,i,0,1,s?S:P);return t._pt=o,o.b=n,o.e=r,t._props.push(i),o},H={deg:1,rad:1,turn:1},$={grid:1,flex:1},K=function t(e,i,r,s){var a,l,u,d,p=parseFloat(r)||0,f=(r+"").trim().substr((p+"").length)||"px",m=o.style,v=g.test(i),y="svg"===e.tagName.toLowerCase(),x=(y?"client":"offset")+(v?"Width":"Height"),b="px"===s,w="%"===s;if(s===f||!p||H[s]||H[f])return p;if("px"===f||b||(p=t(e,i,r,"px")),d=e.getCTM&&U(e),(w||"%"===f)&&(c[i]||~i.indexOf("adius")))return a=d?e.getBBox()[v?"width":"height"]:e[x],(0,h.E_)(w?p/a*100:p/100*a);if(m[v?"width":"height"]=100+(b?f:s),l="rem"!==s&&~i.indexOf("adius")||"em"===s&&e.appendChild&&!y?e:e.parentNode,d&&(l=(e.ownerSVGElement||{}).parentNode),l&&l!==n&&l.appendChild||(l=n.body),(u=l._gsap)&&w&&u.width&&v&&u.time===h.au.time&&!u.uncache)return(0,h.E_)(p/u.width*100);if(w&&("height"===i||"width"===i)){var T=e.style[i];e.style[i]=100+s,a=e[x],T?e.style[i]=T:G(e,i)}else(w||"%"===f)&&!$[j(l,"display")]&&(m.position=j(e,"position")),l===e&&(m.position="static"),l.appendChild(o),a=o[x],l.removeChild(o),m.position="absolute";return v&&w&&((u=(0,h.a0)(l)).time=h.au.time,u.width=l[x]),(0,h.E_)(b?a*p/100:a&&p?100/a*p:0)},Q=function(t,e,i,n){var r;return s||W(),e in y&&"transform"!==e&&~(e=y[e]).indexOf(",")&&(e=e.split(",")[0]),c[e]&&"transform"!==e?(r=tu(t,n),r="transformOrigin"!==e?r[e]:r.svg?r.origin:th(j(t,D))+" "+r.zOrigin+"px"):(!(r=t.style[e])||"auto"===r||n||~(r+"").indexOf("calc("))&&(r=ti[e]&&ti[e](t,e,i)||j(t,e)||(0,h.n)(t,e)||+("opacity"===e)),i&&!~(r+"").trim().indexOf(" ")?K(t,e,r,i)+i:r},Z=function(t,e,i,n){if(!i||"none"===i){var r=N(e,t,1),s=r&&j(t,r,1);s&&s!==i?(e=r,i=s):"borderColor"===e&&(i=j(t,"borderTopColor"))}var o,a,l,u,c,d,p,f,m,g,v,y=new h.J7(this._pt,t.style,e,0,1,h.l1),x=0,b=0;if(y.b=i,y.e=n,i+="","var(--"===(n+="").substring(0,6)&&(n=j(t,n.substring(4,n.indexOf(")")))),"auto"===n&&(d=t.style[e],t.style[e]=n,n=j(t,e)||n,d?t.style[e]=d:G(t,e)),o=[i,n],(0,h.Uc)(o),i=o[0],n=o[1],l=i.match(h.vM)||[],(n.match(h.vM)||[]).length){for(;a=h.vM.exec(n);)p=a[0],m=n.substring(x,a.index),c?c=(c+1)%5:("rgba("===m.substr(-5)||"hsla("===m.substr(-5))&&(c=1),p!==(d=l[b++]||"")&&(u=parseFloat(d)||0,v=d.substr((u+"").length),"="===p.charAt(1)&&(p=(0,h.B0)(u,p)+v),f=parseFloat(p),g=p.substr((f+"").length),x=h.vM.lastIndex-g.length,g||(g=g||h.Yz.units[e]||v,x===n.length&&(n+=g,y.e+=g)),v!==g&&(u=K(t,e,d,g)||0),y._pt={_next:y._pt,p:m||1===b?m:",",s:u,c:f-u,m:c&&c<4||"zIndex"===e?Math.round:0});y.c=x<n.length?n.substring(x,n.length):""}else y.r="display"===e&&"none"===n?S:P;return h.Ks.test(n)&&(y.e=0),this._pt=y,y},J={top:"0%",bottom:"100%",left:"0%",right:"100%",center:"50%"},tt=function(t){var e=t.split(" "),i=e[0],n=e[1]||"50%";return("top"===i||"bottom"===i||"left"===n||"right"===n)&&(t=i,i=n,n=t),e[0]=J[i]||i,e[1]=J[n]||n,e.join(" ")},te=function(t,e){if(e.tween&&e.tween._time===e.tween._dur){var i,n,r,s=e.t,o=s.style,a=e.u,l=s._gsap;if("all"===a||!0===a)o.cssText="",n=1;else for(r=(a=a.split(",")).length;--r>-1;)c[i=a[r]]&&(n=1,i="transformOrigin"===i?D:O),G(s,i);n&&(G(s,O),l&&(l.svg&&s.removeAttribute("transform"),o.scale=o.rotate=o.translate="none",tu(s,1),l.uncache=1,_(o)))}},ti={clearProps:function(t,e,i,n,r){if("isFromStart"!==r.data){var s=t._pt=new h.J7(t._pt,e,i,0,0,te);return s.u=n,s.pr=-10,s.tween=r,t._props.push(i),1}}},tn=[1,0,0,1,0,0],tr={},ts=function(t){return"matrix(1, 0, 0, 1, 0, 0)"===t||"none"===t||!t},to=function(t){var e=j(t,O);return ts(e)?tn:e.substr(7).match(h.vX).map(h.E_)},ta=function(t,e){var i,n,s,o,a=t._gsap||(0,h.a0)(t),l=t.style,u=to(t);return a.svg&&t.getAttribute("transform")?"1,0,0,1,0,0"===(u=[(s=t.transform.baseVal.consolidate().matrix).a,s.b,s.c,s.d,s.e,s.f]).join(",")?tn:u:(u!==tn||t.offsetParent||t===r||a.svg||(s=l.display,l.display="block",(i=t.parentNode)&&(t.offsetParent||t.getBoundingClientRect().width)||(o=1,n=t.nextElementSibling,r.appendChild(t)),u=to(t),s?l.display=s:G(t,"display"),o&&(n?i.insertBefore(t,n):i?i.appendChild(t):r.removeChild(t))),e&&u.length>6?[u[0],u[1],u[4],u[5],u[12],u[13]]:u)},tl=function(t,e,i,n,r,s){var o,a,l,u,h=t._gsap,c=r||ta(t,!0),d=h.xOrigin||0,p=h.yOrigin||0,f=h.xOffset||0,m=h.yOffset||0,g=c[0],v=c[1],y=c[2],x=c[3],b=c[4],w=c[5],T=e.split(" "),P=parseFloat(T[0])||0,S=parseFloat(T[1])||0;i?c!==tn&&(a=g*x-v*y)&&(l=x/a*P+-y/a*S+(y*w-x*b)/a,u=-v/a*P+g/a*S-(g*w-v*b)/a,P=l,S=u):(P=(o=X(t)).x+(~T[0].indexOf("%")?P/100*o.width:P),S=o.y+(~(T[1]||T[0]).indexOf("%")?S/100*o.height:S)),n||!1!==n&&h.smooth?(h.xOffset=f+((b=P-d)*g+(w=S-p)*y)-b,h.yOffset=m+(b*v+w*x)-w):h.xOffset=h.yOffset=0,h.xOrigin=P,h.yOrigin=S,h.smooth=!!n,h.origin=e,h.originIsAbsolute=!!i,t.style[D]="0px 0px",s&&(q(s,h,"xOrigin",d,P),q(s,h,"yOrigin",p,S),q(s,h,"xOffset",f,h.xOffset),q(s,h,"yOffset",m,h.yOffset)),t.setAttribute("data-svg-origin",P+" "+S)},tu=function(t,e){var i=t._gsap||new h.n6(t);if("x"in i&&!e&&!i.uncache)return i;var n,r,s,o,a,l,c,m,g,v,y,x,b,w,T,P,S,E,A,M,C,k,V,R,_,B,L,F,I,N,W,Y,z=t.style,X=i.scaleX<0,G=getComputedStyle(t),q=j(t,D)||"0";return n=r=s=l=c=m=g=v=y=0,o=a=1,i.svg=!!(t.getCTM&&U(t)),G.translate&&(("none"!==G.translate||"none"!==G.scale||"none"!==G.rotate)&&(z[O]=("none"!==G.translate?"translate3d("+(G.translate+" 0 0").split(" ").slice(0,3).join(", ")+") ":"")+("none"!==G.rotate?"rotate("+G.rotate+") ":"")+("none"!==G.scale?"scale("+G.scale.split(" ").join(",")+") ":"")+("none"!==G[O]?G[O]:"")),z.scale=z.rotate=z.translate="none"),w=ta(t,i.svg),i.svg&&(i.uncache?(_=t.getBBox(),q=i.xOrigin-_.x+"px "+(i.yOrigin-_.y)+"px",R=""):R=!e&&t.getAttribute("data-svg-origin"),tl(t,R||q,!!R||i.originIsAbsolute,!1!==i.smooth,w)),x=i.xOrigin||0,b=i.yOrigin||0,w!==tn&&(E=w[0],A=w[1],M=w[2],C=w[3],n=k=w[4],r=V=w[5],6===w.length?(o=Math.sqrt(E*E+A*A),a=Math.sqrt(C*C+M*M),l=E||A?f(A,E)*d:0,(g=M||C?f(M,C)*d+l:0)&&(a*=Math.abs(Math.cos(g*p))),i.svg&&(n-=x-(x*E+b*M),r-=b-(x*A+b*C))):(Y=w[6],N=w[7],L=w[8],F=w[9],I=w[10],W=w[11],n=w[12],r=w[13],s=w[14],c=(T=f(Y,I))*d,T&&(R=k*(P=Math.cos(-T))+L*(S=Math.sin(-T)),_=V*P+F*S,B=Y*P+I*S,L=-(k*S)+L*P,F=-(V*S)+F*P,I=-(Y*S)+I*P,W=-(N*S)+W*P,k=R,V=_,Y=B),m=(T=f(-M,I))*d,T&&(R=E*(P=Math.cos(-T))-L*(S=Math.sin(-T)),_=A*P-F*S,B=M*P-I*S,W=C*S+W*P,E=R,A=_,M=B),l=(T=f(A,E))*d,T&&(R=E*(P=Math.cos(T))+A*(S=Math.sin(T)),_=k*P+V*S,A=A*P-E*S,V=V*P-k*S,E=R,k=_),c&&Math.abs(c)+Math.abs(l)>359.9&&(c=l=0,m=180-m),o=(0,h.E_)(Math.sqrt(E*E+A*A+M*M)),a=(0,h.E_)(Math.sqrt(V*V+Y*Y)),g=Math.abs(T=f(k,V))>2e-4?T*d:0,y=W?1/(W<0?-W:W):0),i.svg&&(R=t.getAttribute("transform"),i.forceCSS=t.setAttribute("transform","")||!ts(j(t,O)),R&&t.setAttribute("transform",R))),Math.abs(g)>90&&270>Math.abs(g)&&(X?(o*=-1,g+=l<=0?180:-180,l+=l<=0?180:-180):(a*=-1,g+=g<=0?180:-180)),e=e||i.uncache,i.x=n-((i.xPercent=n&&(!e&&i.xPercent||(Math.round(t.offsetWidth/2)===Math.round(-n)?-50:0)))?t.offsetWidth*i.xPercent/100:0)+"px",i.y=r-((i.yPercent=r&&(!e&&i.yPercent||(Math.round(t.offsetHeight/2)===Math.round(-r)?-50:0)))?t.offsetHeight*i.yPercent/100:0)+"px",i.z=s+"px",i.scaleX=(0,h.E_)(o),i.scaleY=(0,h.E_)(a),i.rotation=(0,h.E_)(l)+"deg",i.rotationX=(0,h.E_)(c)+"deg",i.rotationY=(0,h.E_)(m)+"deg",i.skewX=g+"deg",i.skewY=v+"deg",i.transformPerspective=y+"px",(i.zOrigin=parseFloat(q.split(" ")[2])||!e&&i.zOrigin||0)&&(z[D]=th(q)),i.xOffset=i.yOffset=0,i.force3D=h.Yz.force3D,i.renderTransform=i.svg?tm:u?tf:td,i.uncache=0,i},th=function(t){return(t=t.split(" "))[0]+" "+t[1]},tc=function(t,e,i){var n=(0,h.l_)(e);return(0,h.E_)(parseFloat(e)+parseFloat(K(t,"x",i+"px",n)))+n},td=function(t,e){e.z="0px",e.rotationY=e.rotationX="0deg",e.force3D=0,tf(t,e)},tp="0deg",tf=function(t,e){var i=e||this,n=i.xPercent,r=i.yPercent,s=i.x,o=i.y,a=i.z,l=i.rotation,u=i.rotationY,h=i.rotationX,c=i.skewX,d=i.skewY,f=i.scaleX,m=i.scaleY,g=i.transformPerspective,v=i.force3D,y=i.target,x=i.zOrigin,b="",w="auto"===v&&t&&1!==t||!0===v;if(x&&(h!==tp||u!==tp)){var T,P=parseFloat(u)*p,S=Math.sin(P),E=Math.cos(P);s=tc(y,s,-(S*(T=Math.cos(P=parseFloat(h)*p))*x)),o=tc(y,o,-(-Math.sin(P)*x)),a=tc(y,a,-(E*T*x)+x)}"0px"!==g&&(b+="perspective("+g+") "),(n||r)&&(b+="translate("+n+"%, "+r+"%) "),(w||"0px"!==s||"0px"!==o||"0px"!==a)&&(b+="0px"!==a||w?"translate3d("+s+", "+o+", "+a+") ":"translate("+s+", "+o+") "),l!==tp&&(b+="rotate("+l+") "),u!==tp&&(b+="rotateY("+u+") "),h!==tp&&(b+="rotateX("+h+") "),(c!==tp||d!==tp)&&(b+="skew("+c+", "+d+") "),(1!==f||1!==m)&&(b+="scale("+f+", "+m+") "),y.style[O]=b||"translate(0, 0)"},tm=function(t,e){var i,n,r,s,o,a=e||this,l=a.xPercent,u=a.yPercent,c=a.x,d=a.y,f=a.rotation,m=a.skewX,g=a.skewY,v=a.scaleX,y=a.scaleY,x=a.target,b=a.xOrigin,w=a.yOrigin,T=a.xOffset,P=a.yOffset,S=a.forceCSS,E=parseFloat(c),A=parseFloat(d);f=parseFloat(f),m=parseFloat(m),(g=parseFloat(g))&&(m+=g=parseFloat(g),f+=g),f||m?(f*=p,m*=p,i=Math.cos(f)*v,n=Math.sin(f)*v,r=-(Math.sin(f-m)*y),s=Math.cos(f-m)*y,m&&(g*=p,r*=o=Math.sqrt(1+(o=Math.tan(m-g))*o),s*=o,g&&(i*=o=Math.sqrt(1+(o=Math.tan(g))*o),n*=o)),i=(0,h.E_)(i),n=(0,h.E_)(n),r=(0,h.E_)(r),s=(0,h.E_)(s)):(i=v,s=y,n=r=0),(E&&!~(c+"").indexOf("px")||A&&!~(d+"").indexOf("px"))&&(E=K(x,"x",c,"px"),A=K(x,"y",d,"px")),(b||w||T||P)&&(E=(0,h.E_)(E+b-(b*i+w*r)+T),A=(0,h.E_)(A+w-(b*n+w*s)+P)),(l||u)&&(o=x.getBBox(),E=(0,h.E_)(E+l/100*o.width),A=(0,h.E_)(A+u/100*o.height)),o="matrix("+i+","+n+","+r+","+s+","+E+","+A+")",x.setAttribute("transform",o),S&&(x.style[O]=o)},tg=function(t,e,i,n,r){var s,o,a=(0,h.vQ)(r),l=parseFloat(r)*(a&&~r.indexOf("rad")?d:1)-n,u=n+l+"deg";return a&&("short"===(s=r.split("_")[1])&&(l%=360)!=l%180&&(l+=l<0?360:-360),"cw"===s&&l<0?l=(l+36e9)%360-360*~~(l/360):"ccw"===s&&l>0&&(l=(l-36e9)%360-360*~~(l/360))),t._pt=o=new h.J7(t._pt,e,i,n,l,b),o.e=u,o.u="deg",t._props.push(i),o},tv=function(t,e){for(var i in e)t[i]=e[i];return t},ty=function(t,e,i){var n,r,s,o,a,l,u,d=tv({},i._gsap),p=i.style;for(r in d.svg?(s=i.getAttribute("transform"),i.setAttribute("transform",""),p[O]=e,n=tu(i,1),G(i,O),i.setAttribute("transform",s)):(s=getComputedStyle(i)[O],p[O]=e,n=tu(i,1),p[O]=s),c)(s=d[r])!==(o=n[r])&&0>"perspective,force3D,transformOrigin,svgOrigin".indexOf(r)&&(a=(0,h.l_)(s)!==(u=(0,h.l_)(o))?K(i,r,s,u):parseFloat(s),l=parseFloat(o),t._pt=new h.J7(t._pt,n,r,a,l-a,x),t._pt.u=u||0,t._props.push(r));tv(n,d)};(0,h.fA)("padding,margin,Width,Radius",function(t,e){var i="Right",n="Bottom",r="Left",s=(e<3?["Top",i,n,r]:["Top"+r,"Top"+i,n+i,n+r]).map(function(i){return e<2?t+i:"border"+i+t});ti[e>1?"border"+t:t]=function(t,e,i,n,r){var o,a;if(arguments.length<4)return 5===(a=(o=s.map(function(e){return Q(t,e,i)})).join(" ")).split(o[0]).length?o[0]:a;o=(n+"").split(" "),a={},s.forEach(function(t,e){return a[t]=o[e]=o[e]||o[(e-1)/2|0]}),t.init(e,a,r)}});var tx={name:"css",register:W,targetTest:function(t){return t.style&&t.nodeType},init:function(t,e,i,n,r){var o,a,l,u,d,p,f,m,g,b,P,S,E,A,M,C,k=this._props,V=t.style,R=i.vars.startAt;for(f in s||W(),this.styles=this.styles||L(t),C=this.styles.props,this.tween=i,e)if("autoRound"!==f&&(a=e[f],!(h.wU[f]&&(0,h.Zm)(f,e,i,n,t,r)))){if(d=typeof a,p=ti[f],"function"===d&&(d=typeof(a=a.call(i,n,t,r))),"string"===d&&~a.indexOf("random(")&&(a=(0,h.Vy)(a)),p)p(this,t,f,a,i)&&(M=1);else if("--"===f.substr(0,2))o=(getComputedStyle(t).getPropertyValue(f)+"").trim(),a+="",h.qA.lastIndex=0,h.qA.test(o)||(m=(0,h.l_)(o),g=(0,h.l_)(a)),g?m!==g&&(o=K(t,f,o,g)+g):m&&(a+=m),this.add(V,"setProperty",o,a,n,r,0,0,f),k.push(f),C.push(f,0,V[f]);else if("undefined"!==d){if(R&&f in R?(o="function"==typeof R[f]?R[f].call(i,n,t,r):R[f],(0,h.vQ)(o)&&~o.indexOf("random(")&&(o=(0,h.Vy)(o)),(0,h.l_)(o+"")||"auto"===o||(o+=h.Yz.units[f]||(0,h.l_)(Q(t,f))||""),"="===(o+"").charAt(1)&&(o=Q(t,f))):o=Q(t,f),u=parseFloat(o),(b="string"===d&&"="===a.charAt(1)&&a.substr(0,2))&&(a=a.substr(2)),l=parseFloat(a),f in y&&("autoAlpha"===f&&(1===u&&"hidden"===Q(t,"visibility")&&l&&(u=0),C.push("visibility",0,V.visibility),q(this,V,"visibility",u?"inherit":"hidden",l?"inherit":"hidden",!l)),"scale"!==f&&"transform"!==f&&~(f=y[f]).indexOf(",")&&(f=f.split(",")[0])),P=f in c){if(this.styles.save(f),"string"===d&&"var(--"===a.substring(0,6)&&(l=parseFloat(a=j(t,a.substring(4,a.indexOf(")"))))),S||((E=t._gsap).renderTransform&&!e.parseTransform||tu(t,e.parseTransform),A=!1!==e.smoothOrigin&&E.smooth,(S=this._pt=new h.J7(this._pt,V,O,0,1,E.renderTransform,E,0,-1)).dep=1),"scale"===f)this._pt=new h.J7(this._pt,E,"scaleY",E.scaleY,(b?(0,h.B0)(E.scaleY,b+l):l)-E.scaleY||0,x),this._pt.u=0,k.push("scaleY",f),f+="X";else if("transformOrigin"===f){C.push(D,0,V[D]),a=tt(a),E.svg?tl(t,a,0,A,0,this):((g=parseFloat(a.split(" ")[2])||0)!==E.zOrigin&&q(this,E,"zOrigin",E.zOrigin,g),q(this,V,f,th(o),th(a)));continue}else if("svgOrigin"===f){tl(t,a,1,A,0,this);continue}else if(f in tr){tg(this,E,f,u,b?(0,h.B0)(u,b+a):a);continue}else if("smoothOrigin"===f){q(this,E,"smooth",E.smooth,a);continue}else if("force3D"===f){E[f]=a;continue}else if("transform"===f){ty(this,a,t);continue}}else f in V||(f=N(f)||f);if(P||(l||0===l)&&(u||0===u)&&!v.test(a)&&f in V)m=(o+"").substr((u+"").length),l||(l=0),g=(0,h.l_)(a)||(f in h.Yz.units?h.Yz.units[f]:m),m!==g&&(u=K(t,f,o,g)),this._pt=new h.J7(this._pt,P?E:V,f,u,(b?(0,h.B0)(u,b+l):l)-u,!P&&("px"===g||"zIndex"===f)&&!1!==e.autoRound?T:x),this._pt.u=g||0,m!==g&&"%"!==g&&(this._pt.b=o,this._pt.r=w);else if(f in V)Z.call(this,t,f,o,b?b+a:a);else if(f in t)this.add(t,f,o||t[f],b?b+a:a,n,r);else if("parseTransform"!==f){(0,h.dg)(f,a);continue}P||(f in V?C.push(f,0,V[f]):"function"==typeof t[f]?C.push(f,2,t[f]()):C.push(f,1,o||t[f])),k.push(f)}}M&&(0,h.St)(this)},render:function(t,e){if(e.tween._time||!l())for(var i=e._pt;i;)i.r(t,i.d),i=i._next;else e.styles.revert()},get:Q,aliases:y,getSetter:function(t,e,i){var n=y[e];return n&&0>n.indexOf(",")&&(e=n),e in c&&e!==D&&(t._gsap.x||Q(t,"x"))?i&&a===i?"scale"===e?C:M:(a=i||{},"scale"===e?k:V):t.style&&!(0,h.OF)(t.style[e])?E:~e.indexOf("-")?A:(0,h.Dx)(t,e)},core:{_removeProperty:G,_getMatrix:ta}};h.os.utils.checkPrefix=N,h.os.core.getStyleSaver=L,function(t,e,i,n){var r=(0,h.fA)(t+","+e+","+i,function(t){c[t]=1});(0,h.fA)(e,function(t){h.Yz.units[t]="deg",tr[t]=1}),y[r[13]]=t+","+e,(0,h.fA)(n,function(t){var e=t.split(":");y[e[1]]=r[e[0]]})}("x,y,z,scale,scaleX,scaleY,xPercent,yPercent","rotation,rotationX,rotationY,skewX,skewY","transform,transformOrigin,svgOrigin,force3D,smoothOrigin,transformPerspective","0:translateX,1:translateY,2:translateZ,8:rotate,8:rotationZ,8:rotateZ,9:rotateX,10:rotateY"),(0,h.fA)("x,y,z,top,right,bottom,left,width,height,fontSize,padding,margin,perspective",function(t){h.Yz.units[t]="px"}),h.os.registerPlugin(tx);var tb=h.os.registerPlugin(tx)||h.os;tb.core.Tween},1116:(t,e,i)=>{i.d(e,{J:()=>n});let n=(0,i(1917).p)(()=>void 0!==window.ScrollTimeline)},1297:(t,e,i)=>{i.d(e,{q:()=>n});let n=(t,e,i)=>i>e?e:i<t?t:i},1335:(t,e,i)=>{i.d(e,{u:()=>r});var n=i(9064);let r={test:(0,i(5920).$)("#"),parse:function(t){let e="",i="",n="",r="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),r=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),r=t.substring(4,5),e+=e,i+=i,n+=n,r+=r),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:r?parseInt(r,16)/255:1}},transform:n.B.transform}},1508:(t,e,i)=>{i.d(e,{Q:()=>n});let n=(0,i(2115).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},1513:(t,e,i)=>{i.d(e,{q:()=>n});class n{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}},1557:(t,e,i)=>{i.d(e,{a:()=>n});let n=t=>Math.round(1e5*t)/1e5},1765:(t,e,i)=>{i.d(e,{V:()=>n});let n=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2},1784:(t,e,i)=>{i.d(e,{Z:()=>s});var n=i(5818),r=i(3210);function s(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let o=(0,n.q)(0,e,s);t.push((0,r.k)(i,1,o))}}(e,t.length-1),e}},1788:(t,e,i)=>{i.d(e,{n:()=>n});let n="data-"+(0,i(8450).I)("framerAppearId")},1859:(t,e,i)=>{i.d(e,{f:()=>z});var n=i(8777);function r(t){t.duration=0,t.type}var s=i(9515),o=i(532),a=i(3387),l=i(9827),u=i(4261),h=i(6778),c=i(7322),d=i(7215),p=i(4542),f=i(1116),m=i(1513),g=i(3704),v=i(4744),y=i(8589),x=i(1917);let b={},w=function(t,e){let i=(0,x.p)(t);return()=>b[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing");var T=i(7705);let P=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,S={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:P([0,.65,.55,1]),circOut:P([.55,0,1,.45]),backIn:P([.31,.01,.66,-.59]),backOut:P([.33,1.53,.69,.99])};function E(t){return"function"==typeof t&&"applyToOptions"in t}class A extends m.q{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:n,pseudoElement:r,allowFlatten:s=!1,finalKeyframe:o,onComplete:a}=t;this.isPseudoElement=!!r,this.allowFlatten=s,this.options=t,(0,p.V)("string"!=typeof t.type,'Mini animate() doesn\'t support "type" as a string.',"mini-spring");let l=function({type:t,...e}){return E(t)&&w()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:n=0,duration:r=300,repeat:s=0,repeatType:o="loop",ease:a="easeOut",times:l}={},u){let h={[e]:i};l&&(h.offset=l);let c=function t(e,i){if(e)return"function"==typeof e?w()?(0,T.K)(e,i):"ease-out":(0,y.D)(e)?P(e):Array.isArray(e)?e.map(e=>t(e,i)||S.easeOut):S[e]}(a,r);Array.isArray(c)&&(h.easing=c),v.Q.value&&g.q.waapi++;let d={delay:n,duration:r,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"};u&&(d.pseudoElement=u);let p=t.animate(h,d);return v.Q.value&&p.finished.finally(()=>{g.q.waapi--}),p}(e,i,n,l,r),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!r){let t=(0,h.X)(n,this.options,o,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){e.startsWith("--")?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}a?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){let t=this.animation.effect?.getComputedTiming?.().duration||0;return(0,d.X)(Number(t))}get time(){return(0,d.X)(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=(0,d.f)(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&(0,f.J)())?(this.animation.timeline=t,l.l):e(this)}}var M=i(144),C=i(6009),k=i(3972),V=i(7712);let O={anticipate:C.b,backInOut:k.ZZ,circInOut:V.tn};class D extends A{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in O&&(t.ease=O[t.ease])}(t),(0,M.E)(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:n,element:r,...s}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new o.s({...s,autoplay:!1}),l=(0,d.f)(this.finishedTime??this.time);e.setWithVelocity(a.sample(l-10).value,a.sample(l).value,10),a.stop()}}var R=i(10);let _=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(R.f.test(t)||"0"===t)&&!t.startsWith("url(")),B=new Set(["opacity","clipPath","filter","transform"]),L=(0,x.p)(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class F extends m.q{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:r=0,repeatType:s="loop",keyframes:o,name:a,motionValue:l,element:h,...d}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=u.k.now();let p={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:r,repeatType:s,name:a,motionValue:l,element:h,...d},f=h?.KeyframeResolver||c.h;this.keyframeResolver=new f(o,(t,e,i)=>this.onKeyframesResolved(t,e,p,!i),a,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,n){this.keyframeResolver=void 0;let{name:s,type:c,velocity:d,delay:f,isHandoff:m,onUpdate:g}=i;this.resolvedAt=u.k.now(),!function(t,e,i,n){let r=t[0];if(null===r)return!1;if("display"===e||"visibility"===e)return!0;let s=t[t.length-1],o=_(r,e),a=_(s,e);return(0,p.$)(o===a,`You are trying to animate ${e} from "${r}" to "${s}". "${o?s:r}" is not an animatable value.`,"value-not-animatable"),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||E(i))&&n)}(t,s,c,d)&&((a.W.instantAnimations||!f)&&g?.((0,h.X)(t,i,e)),t[0]=t[t.length-1],r(i),i.repeat=0);let v={startTime:n?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},y=!m&&function(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:r,damping:s,type:o}=t;if(!(e?.owner?.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return L()&&i&&B.has(i)&&("transform"!==i||!l)&&!a&&!n&&"mirror"!==r&&0!==s&&"inertia"!==o}(v)?new D({...v,element:v.motionValue.owner.current}):new o.s(v);y.finished.then(()=>this.notifyFinished()).catch(l.l),this.pendingTimeline&&(this.stopTimeline=y.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=y}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),(0,c.q)()),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let j=t=>null!==t;var I=i(18);let N={type:"spring",stiffness:500,damping:25,restSpeed:10},W={type:"keyframes",duration:.8},Y={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},z=(t,e,i,l={},u,h)=>c=>{let p=(0,n.r)(l,t)||{},f=p.delay||l.delay||0,{elapsed:m=0}=l;m-=(0,d.f)(f);let g={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...p,delay:-m,onUpdate:t=>{e.set(t),p.onUpdate&&p.onUpdate(t)},onComplete:()=>{c(),p.onComplete&&p.onComplete()},name:t,motionValue:e,element:h?void 0:u};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:r,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(p)&&Object.assign(g,((t,{keyframes:e})=>e.length>2?W:I.f.has(t)?t.startsWith("scale")?{type:"spring",stiffness:550,damping:0===e[1]?2*Math.sqrt(550):30,restSpeed:10}:N:Y)(t,g)),g.duration&&(g.duration=(0,d.f)(g.duration)),g.repeatDelay&&(g.repeatDelay=(0,d.f)(g.repeatDelay)),void 0!==g.from&&(g.keyframes[0]=g.from);let v=!1;if(!1!==g.type&&(0!==g.duration||g.repeatDelay)||(r(g),0===g.delay&&(v=!0)),(a.W.instantAnimations||a.W.skipAnimations)&&(v=!0,r(g),g.delay=0),g.allowFlatten=!p.type&&!p.ease,v&&!h&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},n){let r=t.filter(j),s=e&&"loop"!==i&&e%2==1?0:r.length-1;return r[s]}(g.keyframes,p);if(void 0!==t)return void s.Gt.update(()=>{g.onUpdate(t),g.onComplete()})}return p.isSync?new o.s(g):new F(g)}},1917:(t,e,i)=>{i.d(e,{p:()=>n});function n(t){let e;return()=>(void 0===e&&(e=t()),e)}},2198:(t,e,i)=>{i.d(e,{K:()=>n});function n(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let n=document;e&&(n=e.current);let r=i?.[t]??n.querySelectorAll(t);return r?Array.from(r):[]}return Array.from(t)}},2458:(t,e,i)=>{i.d(e,{Y:()=>n,t:()=>r});let n=2e4;function r(t){let e=0,i=t.next(e);for(;!i.done&&e<n;)e+=50,i=t.next(e);return e>=n?1/0:e}},2483:(t,e,i)=>{i.d(e,{A:()=>s});var n=i(9827);let r=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function s(t,e,i,s){return t===e&&i===s?n.l:n=>0===n||1===n?n:r(function(t,e,i,n,s){let o,a,l=0;do(o=r(a=e+(i-e)/2,n,s)-t)>0?i=a:e=a;while(Math.abs(o)>1e-7&&++l<12);return a}(n,0,1,t,i),e,s)}},2720:(t,e,i)=>{let n,r;i.d(e,{L:()=>G});var s=i(98),o=i(4542),a=i(2115),l=i(9827),u=i(9515);function h(t,e){let i,n=()=>{let{currentTime:n}=e,r=(null===n?0:n.value)/100;i!==r&&t(r),i=r};return u.Gt.preUpdate(n,!0),()=>(0,u.WG)(n)}var c=i(1116),d=i(9782),p=i(2198);let f=new WeakMap,m=(t,e,i)=>(n,r)=>r&&r[0]?r[0][t+"Size"]:(0,d.x)(n)&&"getBBox"in n?n.getBBox()[e]:n[i],g=m("inline","width","offsetWidth"),v=m("block","height","offsetHeight");function y({target:t,borderBoxSize:e}){f.get(t)?.forEach(i=>{i(t,{get width(){return g(t,e)},get height(){return v(t,e)}})})}function x(t){t.forEach(y)}let b=new Set;var w=i(5818),T=i(2923);let P=()=>({current:0,offset:[],progress:0,scrollLength:0,targetOffset:0,targetLength:0,containerLength:0,velocity:0}),S={x:{length:"Width",position:"Left"},y:{length:"Height",position:"Top"}};function E(t,e,i,n){let r=i[e],{length:s,position:o}=S[e],a=r.current,l=i.time;r.current=t[`scroll${o}`],r.scrollLength=t[`scroll${s}`]-t[`client${s}`],r.offset.length=0,r.offset[0]=0,r.offset[1]=r.scrollLength,r.progress=(0,w.q)(0,r.scrollLength,r.current);let u=n-l;r.velocity=u>50?0:(0,T.f)(r.current-a,u)}var A=i(6775),M=i(1784),C=i(1297),k=i(7351);let V={start:0,center:.5,end:1};function O(t,e,i=0){let n=0;if(t in V&&(t=V[t]),"string"==typeof t){let e=parseFloat(t);t.endsWith("px")?n=e:t.endsWith("%")?t=e/100:t.endsWith("vw")?n=e/100*document.documentElement.clientWidth:t.endsWith("vh")?n=e/100*document.documentElement.clientHeight:t=e}return"number"==typeof t&&(n=e*t),i+n}let D=[0,0],R={All:[[0,0],[1,1]]},_={x:0,y:0},B=new WeakMap,L=new WeakMap,F=new WeakMap,j=t=>t===document.scrollingElement?window:t;function I(t,{container:e=document.scrollingElement,...i}={}){if(!e)return l.l;let s=F.get(e);s||(s=new Set,F.set(e,s));let o=function(t,e,i,n={}){return{measure:e=>{!function(t,e=t,i){if(i.x.targetOffset=0,i.y.targetOffset=0,e!==t){let n=e;for(;n&&n!==t;)i.x.targetOffset+=n.offsetLeft,i.y.targetOffset+=n.offsetTop,n=n.offsetParent}i.x.targetLength=e===t?e.scrollWidth:e.clientWidth,i.y.targetLength=e===t?e.scrollHeight:e.clientHeight,i.x.containerLength=t.clientWidth,i.y.containerLength=t.clientHeight}(t,n.target,i),E(t,"x",i,e),E(t,"y",i,e),i.time=e,(n.offset||n.target)&&function(t,e,i){let{offset:n=R.All}=i,{target:r=t,axis:s="y"}=i,o="y"===s?"height":"width",a=r!==t?function(t,e){let i={x:0,y:0},n=t;for(;n&&n!==e;)if((0,k.s)(n))i.x+=n.offsetLeft,i.y+=n.offsetTop,n=n.offsetParent;else if("svg"===n.tagName){let t=n.getBoundingClientRect(),e=(n=n.parentElement).getBoundingClientRect();i.x+=t.left-e.left,i.y+=t.top-e.top}else if(n instanceof SVGGraphicsElement){let{x:t,y:e}=n.getBBox();i.x+=t,i.y+=e;let r=null,s=n.parentNode;for(;!r;)"svg"===s.tagName&&(r=s),s=n.parentNode;n=r}else break;return i}(r,t):_,l=r===t?{width:t.scrollWidth,height:t.scrollHeight}:"getBBox"in r&&"svg"!==r.tagName?r.getBBox():{width:r.clientWidth,height:r.clientHeight},u={width:t.clientWidth,height:t.clientHeight};e[s].offset.length=0;let h=!e[s].interpolate,c=n.length;for(let t=0;t<c;t++){let i=function(t,e,i,n){let r=Array.isArray(t)?t:D,s=0;return"number"==typeof t?r=[t,t]:"string"==typeof t&&(r=(t=t.trim()).includes(" ")?t.split(" "):[t,V[t]?t:"0"]),(s=O(r[0],i,n))-O(r[1],e)}(n[t],u[o],l[o],a[s]);h||i===e[s].interpolatorOffsets[t]||(h=!0),e[s].offset[t]=i}h&&(e[s].interpolate=(0,A.G)(e[s].offset,(0,M.Z)(n),{clamp:!1}),e[s].interpolatorOffsets=[...e[s].offset]),e[s].progress=(0,C.q)(0,1,e[s].interpolate(e[s].current))}(t,i,n)},notify:()=>e(i)}}(e,t,{time:0,x:P(),y:P()},i);if(s.add(o),!B.has(e)){let t=()=>{for(let t of s)t.measure(u.uv.timestamp);u.Gt.preUpdate(i)},i=()=>{for(let t of s)t.notify()},o=()=>u.Gt.read(t);B.set(e,o);let a=j(e);window.addEventListener("resize",o,{passive:!0}),e!==document.documentElement&&L.set(e,"function"==typeof e?(b.add(e),r||(r=()=>{let t={get width(){return window.innerWidth},get height(){return window.innerHeight}};b.forEach(e=>e(t))},window.addEventListener("resize",r)),()=>{b.delete(e),b.size||"function"!=typeof r||(window.removeEventListener("resize",r),r=void 0)}):function(t,e){n||"undefined"!=typeof ResizeObserver&&(n=new ResizeObserver(x));let i=(0,p.K)(t);return i.forEach(t=>{let i=f.get(t);i||(i=new Set,f.set(t,i)),i.add(e),n?.observe(t)}),()=>{i.forEach(t=>{let i=f.get(t);i?.delete(e),i?.size||n?.unobserve(t)})}}(e,o)),a.addEventListener("scroll",o,{passive:!0}),o()}let a=B.get(e);return u.Gt.read(a,!1,!0),()=>{(0,u.WG)(a);let t=F.get(e);if(!t||(t.delete(o),t.size))return;let i=B.get(e);B.delete(e),i&&(j(e).removeEventListener("scroll",i),L.get(e)?.(),window.removeEventListener("resize",i))}}let N=new Map;function W({source:t,container:e,...i}){let{axis:n}=i;t&&(e=t);let r=N.get(e)??new Map;N.set(e,r);let s=i.target??"self",o=r.get(s)??{},a=n+(i.offset??[]).join(",");return o[a]||(o[a]=!i.target&&(0,c.J)()?new ScrollTimeline({source:e,axis:n}):function(t){let e={value:0},i=I(i=>{e.value=100*i[t.axis].progress},t);return{currentTime:e,cancel:i}}({container:e,...i})),o[a]}var Y=i(2885),z=i(7494);let X=()=>({scrollX:(0,s.OQ)(0),scrollY:(0,s.OQ)(0),scrollXProgress:(0,s.OQ)(0),scrollYProgress:(0,s.OQ)(0)}),U=t=>!!t&&!t.current;function G({container:t,target:e,...i}={}){let n=(0,Y.M)(X),r=(0,a.useRef)(null),s=(0,a.useRef)(!1),u=(0,a.useCallback)(()=>(r.current=function(t,{axis:e="y",container:i=document.scrollingElement,...n}={}){var r,s;if(!i)return l.l;let o={axis:e,container:i,...n};return"function"==typeof t?(r=t,s=o,2===r.length?I(t=>{r(t[s.axis].progress,t)},s):h(r,W(s))):function(t,e){let i=W(e);return t.attachTimeline({timeline:e.target?void 0:i,observe:t=>(t.pause(),h(e=>{t.time=t.duration*e},i))})}(t,o)}((t,{x:e,y:i})=>{n.scrollX.set(e.current),n.scrollXProgress.set(e.progress),n.scrollY.set(i.current),n.scrollYProgress.set(i.progress)},{...i,container:t?.current||void 0,target:e?.current||void 0}),()=>{r.current?.()}),[t,e,JSON.stringify(i.offset)]);return(0,z.E)(()=>{if(s.current=!1,!(U(t)||U(e)))return u();s.current=!0},[u]),(0,a.useEffect)(()=>s.current?((0,o.V)(!U(t),"Container ref is defined but not hydrated","use-scroll-ref"),(0,o.V)(!U(e),"Target ref is defined but not hydrated","use-scroll-ref"),u()):void 0,[u]),n}},2735:(t,e,i)=>{function n(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function r(t,e,i,r){if("function"==typeof e){let[s,o]=n(r);e=e(void 0!==i?i:t.custom,s,o)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,o]=n(r);e=e(void 0!==i?i:t.custom,s,o)}return e}i.d(e,{a:()=>r})},2885:(t,e,i)=>{i.d(e,{M:()=>r});var n=i(2115);function r(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},2886:(t,e,i)=>{i.d(e,{o:()=>f});var n=i(1297),r=i(7215),s=i(7705),o=i(2458),a=i(3945);let l={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};var u=i(4542);function h(t,e){return t*Math.sqrt(1-e*e)}let c=["duration","bounce"],d=["stiffness","damping","mass"];function p(t,e){return e.some(e=>void 0!==t[e])}function f(t=l.visualDuration,e=l.bounce){let i,m="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:g,restDelta:v}=m,y=m.keyframes[0],x=m.keyframes[m.keyframes.length-1],b={done:!1,value:y},{stiffness:w,damping:T,mass:P,duration:S,velocity:E,isResolvedFromDuration:A}=function(t){let e={velocity:l.velocity,stiffness:l.stiffness,damping:l.damping,mass:l.mass,isResolvedFromDuration:!1,...t};if(!p(t,d)&&p(t,c))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),r=i*i,s=2*(0,n.q)(.05,1,1-(t.bounce||0))*Math.sqrt(r);e={...e,mass:l.mass,stiffness:r,damping:s}}else{let i=function({duration:t=l.duration,bounce:e=l.bounce,velocity:i=l.velocity,mass:s=l.mass}){let o,a;(0,u.$)(t<=(0,r.f)(l.maxDuration),"Spring duration must be 10 seconds or less","spring-duration-limit");let c=1-e;c=(0,n.q)(l.minDamping,l.maxDamping,c),t=(0,n.q)(l.minDuration,l.maxDuration,(0,r.X)(t)),c<1?(o=e=>{let n=e*c,r=n*t;return .001-(n-i)/h(e,c)*Math.exp(-r)},a=e=>{let n=e*c*t,r=Math.pow(c,2)*Math.pow(e,2)*t,s=Math.exp(-n),a=h(Math.pow(e,2),c);return(n*i+i-r)*s*(-o(e)+.001>0?-1:1)/a}):(o=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),a=e=>t*t*(i-e)*Math.exp(-e*t));let d=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(o,a,5/t);if(t=(0,r.f)(t),isNaN(d))return{stiffness:l.stiffness,damping:l.damping,duration:t};{let e=Math.pow(d,2)*s;return{stiffness:e,damping:2*c*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:l.mass}).isResolvedFromDuration=!0}return e}({...m,velocity:-(0,r.X)(m.velocity||0)}),M=E||0,C=T/(2*Math.sqrt(w*P)),k=x-y,V=(0,r.X)(Math.sqrt(w/P)),O=5>Math.abs(k);if(g||(g=O?l.restSpeed.granular:l.restSpeed.default),v||(v=O?l.restDelta.granular:l.restDelta.default),C<1){let t=h(V,C);i=e=>x-Math.exp(-C*V*e)*((M+C*V*k)/t*Math.sin(t*e)+k*Math.cos(t*e))}else if(1===C)i=t=>x-Math.exp(-V*t)*(k+(M+V*k)*t);else{let t=V*Math.sqrt(C*C-1);i=e=>{let i=Math.exp(-C*V*e),n=Math.min(t*e,300);return x-i*((M+C*V*k)*Math.sinh(n)+t*k*Math.cosh(n))/t}}let D={calculatedDuration:A&&S||null,next:t=>{let e=i(t);if(A)b.done=t>=S;else{let n=0===t?M:0;C<1&&(n=0===t?(0,r.f)(M):(0,a.Y)(i,t,e));let s=Math.abs(x-e)<=v;b.done=Math.abs(n)<=g&&s}return b.value=b.done?x:e,b},toString:()=>{let t=Math.min((0,o.t)(D),o.Y),e=(0,s.K)(e=>D.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return D}f.applyToOptions=t=>{let e=function(t,e=100,i){let n=i({...t,keyframes:[0,e]}),s=Math.min((0,o.t)(n),o.Y);return{type:"keyframes",ease:t=>n.next(s*t).value/e,duration:(0,r.X)(s)}}(t,100,f);return t.ease=e.ease,t.duration=(0,r.f)(e.duration),t.type="keyframes",t}},2923:(t,e,i)=>{i.d(e,{f:()=>n});function n(t,e){return e?1e3/e*t:0}},3191:(t,e,i)=>{i.d(e,{F:()=>r});let n=(t,e)=>i=>e(t(i)),r=(...t)=>t.reduce(n)},3210:(t,e,i)=>{i.d(e,{k:()=>n});let n=(t,e,i)=>t+(e-t)*i},3387:(t,e,i)=>{i.d(e,{W:()=>n});let n={}},3704:(t,e,i)=>{i.d(e,{q:()=>n});let n={layout:0,mainThread:0,waapi:0}},3945:(t,e,i)=>{i.d(e,{Y:()=>r});var n=i(2923);function r(t,e,i){let r=Math.max(e-5,0);return(0,n.f)(i-t(r),e-r)}},3972:(t,e,i)=>{i.d(e,{Sz:()=>o,ZZ:()=>l,dg:()=>a});var n=i(2483),r=i(1765),s=i(4180);let o=(0,n.A)(.33,1.53,.69,.99),a=(0,s.G)(o),l=(0,r.V)(a)},4087:(t,e,i)=>{i.d(e,{N:()=>o});var n=i(9515),r=i(2115),s=i(1508);function o(t){let e=(0,r.useRef)(0),{isStatic:i}=(0,r.useContext)(s.Q);(0,r.useEffect)(()=>{if(i)return;let r=({timestamp:i,delta:n})=>{e.current||(e.current=i),t(i-e.current,n)};return n.Gt.update(r,!0),()=>(0,n.WG)(r)},[t])}},4158:(t,e,i)=>{i.d(e,{KN:()=>s,gQ:()=>u,px:()=>o,uj:()=>r,vh:()=>a,vw:()=>l});let n=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),r=n("deg"),s=n("%"),o=n("px"),a=n("vh"),l=n("vw"),u={...s,parse:t=>s.parse(t)/100,transform:t=>s.transform(100*t)}},4160:(t,e,i)=>{i.d(e,{Ib:()=>d,ry:()=>c,zs:()=>h});let n=t=>180*t/Math.PI,r=t=>o(n(Math.atan2(t[1],t[0]))),s={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:r,rotateZ:r,skewX:t=>n(Math.atan(t[1])),skewY:t=>n(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},o=t=>((t%=360)<0&&(t+=360),t),a=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),l=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),u={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:a,scaleY:l,scale:t=>(a(t)+l(t))/2,rotateX:t=>o(n(Math.atan2(t[6],t[5]))),rotateY:t=>o(n(Math.atan2(-t[2],t[0]))),rotateZ:r,rotate:r,skewX:t=>n(Math.atan(t[4])),skewY:t=>n(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function h(t){return+!!t.includes("scale")}function c(t,e){let i,n;if(!t||"none"===t)return h(e);let r=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(r)i=u,n=r;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=s,n=e}if(!n)return h(e);let o=i[e],a=n[1].split(",").map(p);return"function"==typeof o?o(a):a[o]}let d=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return c(i,e)};function p(t){return parseFloat(t.trim())}},4180:(t,e,i)=>{i.d(e,{G:()=>n});let n=t=>e=>1-t(1-e)},4261:(t,e,i)=>{let n;i.d(e,{k:()=>a});var r=i(3387),s=i(9515);function o(){n=void 0}let a={now:()=>(void 0===n&&a.set(s.uv.isProcessing||r.W.useManualTiming?s.uv.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(o)}}},4272:(t,e,i)=>{i.d(e,{y:()=>o});var n=i(1335),r=i(8476),s=i(9064);let o={test:t=>s.B.test(t)||n.u.test(t)||r.V.test(t),parse:t=>s.B.test(t)?s.B.parse(t):r.V.test(t)?r.V.parse(t):n.u.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?s.B.transform(t):r.V.transform(t),getAnimatableNone:t=>{let e=o.parse(t);return e.alpha=0,o.transform(e)}}},4542:(t,e,i)=>{i.d(e,{$:()=>n,V:()=>r});let n=()=>{},r=()=>{}},4744:(t,e,i)=>{i.d(e,{Q:()=>n});let n={value:null,addProjectionMetrics:null}},4803:(t,e,i)=>{i.d(e,{S:()=>n});let n=t=>!!(t&&t.getVelocity)},5228:(t,e,i)=>{i.d(e,{A:()=>b});let n,r,s,o="undefined"!=typeof Intl?new Intl.Segmenter:0,a=t=>"string"==typeof t?a(document.querySelectorAll(t)):"length"in t?Array.from(t):[t],l=t=>a(t).filter(t=>t instanceof HTMLElement),u=[],h=function(){},c=/\s+/g,d=RegExp("\\p{RI}\\p{RI}|\\p{Emoji}(\\p{EMod}|\\u{FE0F}\\u{20E3}?|[\\u{E0020}-\\u{E007E}]+\\u{E007F})?(\\u{200D}\\p{Emoji}(\\p{EMod}|\\u{FE0F}\\u{20E3}?|[\\u{E0020}-\\u{E007E}]+\\u{E007F})?)*|.","gu"),p={left:0,top:0,width:0,height:0},f=(t,e)=>{if(e){let i=new Set(t.join("").match(e)||u),n=t.length,r,s,o,a;if(i.size){for(;--n>-1;)for(o of(s=t[n],i))if(o.startsWith(s)&&o.length>s.length){for(r=0,a=s;o.startsWith(a+=t[n+ ++r])&&a.length<o.length;);if(r&&a.length===o.length){t[n]=o,t.splice(n+1,r);break}}}}return t},m=t=>"inline"===window.getComputedStyle(t).display&&(t.style.display="inline-block"),g=(t,e,i)=>e.insertBefore("string"==typeof t?document.createTextNode(t):t,i),v=(t,e,i)=>{let n=e[t+"sClass"]||"",{tag:r="div",aria:s="auto",propIndex:o=!1}=e,a="line"===t?"block":"inline-block",l=n.indexOf("++")>-1,u=e=>{let u=document.createElement(r),h=i.length+1;return n&&(u.className=n+(l?" "+n+h:"")),o&&u.style.setProperty("--"+t,h+""),"none"!==s&&u.setAttribute("aria-hidden","true"),"span"!==r&&(u.style.position="relative",u.style.display=a),u.textContent=e,i.push(u),u};return l&&(n=n.replace("++","")),u.collection=i,u},y=(t,e,i,n,r,s,a,l,h,d)=>{var p;let v=Array.from(t.childNodes),x=0,{wordDelimiter:b,reduceWhiteSpace:w=!0,prepareText:T}=e,P=t.getBoundingClientRect(),S=P,E=!w&&"pre"===window.getComputedStyle(t).whiteSpace.substring(0,3),A=0,M=i.collection,C,k,V,O,D,R,_,B,L,F,j,I,N,W,Y,z,X,U;for("object"==typeof b?(V=b.delimiter||b,k=b.replaceWith||""):k=""===b?"":b||" ",C=" "!==k;x<v.length;x++)if(3===(O=v[x]).nodeType){for(Y=O.textContent||"",w?Y=Y.replace(c," "):E&&(Y=Y.replace(/\n/g,k+"\n")),T&&(Y=T(Y,t)),O.textContent=Y,X=(D=k||V?Y.split(V||k):Y.match(l)||u)[D.length-1],B=C?" "===X.slice(-1):!X,X||D.pop(),S=P,(_=C?" "===D[0].charAt(0):!D[0])&&g(" ",t,O),D[0]||D.shift(),f(D,h),s&&d||(O.textContent=""),L=1;L<=D.length;L++)if(z=D[L-1],!w&&E&&"\n"===z.charAt(0)&&(null==(p=O.previousSibling)||p.remove(),g(document.createElement("br"),t,O),z=z.slice(1)),w||""!==z)if(" "===z)t.insertBefore(document.createTextNode(" "),O);else{if(C&&" "===z.charAt(0)&&g(" ",t,O),A&&1===L&&!_&&M.indexOf(A.parentNode)>-1?(R=M[M.length-1]).appendChild(document.createTextNode(n?"":z)):(g(R=i(n?"":z),t,O),A&&1===L&&!_&&R.insertBefore(A,R.firstChild)),n)for(U=0,j=o?f([...o.segment(z)].map(t=>t.segment),h):z.match(l)||u;U<j.length;U++)R.appendChild(" "===j[U]?document.createTextNode(" "):n(j[U]));if(s&&d){if(Y=O.textContent=Y.substring(z.length+1,Y.length),(F=R.getBoundingClientRect()).top>S.top&&F.left<=S.left){for(I=t.cloneNode(),N=t.childNodes[0];N&&N!==R;)W=N,N=N.nextSibling,I.appendChild(W);t.parentNode.insertBefore(I,t),r&&m(I)}S=F}(L<D.length||B)&&g(L>=D.length?" ":C&&" "===z.slice(-1)?" "+k:k,t,O)}else g(k,t,O);t.removeChild(O),A=0}else 1===O.nodeType&&(a&&a.indexOf(O)>-1?(M.indexOf(O.previousSibling)>-1&&M[M.length-1].appendChild(O),A=O):(y(O,e,i,n,r,s,a,l,h,!0),A=0),r&&m(O))},x=class t{constructor(t,e){this.isSplit=!1,s||b.register(window.gsap),this.elements=l(t),this.chars=[],this.words=[],this.lines=[],this.masks=[],this.vars=e,this._split=()=>this.isSplit&&this.split(this.vars);let i=[],n,r=()=>{let t=i.length,e;for(;t--;){let n=(e=i[t]).element.offsetWidth;if(n!==e.width){e.width=n,this._split();return}}};this._data={orig:i,obs:"undefined"!=typeof ResizeObserver&&new ResizeObserver(()=>{clearTimeout(n),n=setTimeout(r,200)})},h(this),this.split(e)}split(t){this.isSplit&&this.revert(),this.vars=t=t||this.vars||{};let{type:e="chars,words,lines",aria:i="auto",deepSlice:n=!0,smartWrap:s,onSplit:o,autoSplit:u=!1,specialChars:h,mask:c}=this.vars,f=e.indexOf("lines")>-1,m=e.indexOf("chars")>-1,g=e.indexOf("words")>-1,x=m&&!g&&!f,b=h&&("push"in h?RegExp("(?:"+h.join("|")+")","gu"):h),w=b?RegExp(b.source+"|"+d.source,"gu"):d,T=!!t.ignore&&l(t.ignore),{orig:P,animTime:S,obs:E}=this._data,A;return(m||g||f)&&(this.elements.forEach((e,r)=>{P[r]={element:e,html:e.innerHTML,ariaL:e.getAttribute("aria-label"),ariaH:e.getAttribute("aria-hidden")},"auto"===i?e.setAttribute("aria-label",(e.textContent||"").trim()):"hidden"===i&&e.setAttribute("aria-hidden","true");let o=[],l=[],u=[],h=m?v("char",t,o):null,c=v("word",t,l),d,S,E,A;if(y(e,t,c,h,x,n&&(f||x),T,w,b,!1),f){let i,n,r=a(e.childNodes),s=(i=v("line",t,u),n=window.getComputedStyle(e).textAlign||"left",(t,s)=>{let o=i("");for(o.style.textAlign=n,e.insertBefore(o,r[t]);t<s;t++)o.appendChild(r[t]);o.normalize()}),o,l=[],h=0,c=r.map(t=>1===t.nodeType?t.getBoundingClientRect():p),f=p;for(d=0;d<r.length;d++)1===(o=r[d]).nodeType&&("BR"===o.nodeName?(l.push(o),s(h,d+1),f=c[h=d+1]):(d&&c[d].top>f.top&&c[d].left<=f.left&&(s(h,d),h=d),f=c[d]));h<d&&s(h,d),l.forEach(t=>{var e;return null==(e=t.parentNode)?void 0:e.removeChild(t)})}if(!g){for(d=0;d<l.length;d++)if(S=l[d],m||!S.nextSibling||3!==S.nextSibling.nodeType)if(s&&!f){for((E=document.createElement("span")).style.whiteSpace="nowrap";S.firstChild;)E.appendChild(S.firstChild);S.replaceWith(E)}else S.replaceWith(...S.childNodes);else(A=S.nextSibling)&&3===A.nodeType&&(A.textContent=(S.textContent||"")+(A.textContent||""),S.remove());l.length=0,e.normalize()}this.lines.push(...u),this.words.push(...l),this.chars.push(...o)}),c&&this[c]&&this.masks.push(...this[c].map(t=>{let e=t.cloneNode();return t.replaceWith(e),e.appendChild(t),t.className&&(e.className=t.className.replace(/(\b\w+\b)/g,"$1-mask")),e.style.overflow="clip",e}))),this.isSplit=!0,r&&(u?r.addEventListener("loadingdone",this._split):"loading"===r.status&&console.warn("SplitText called before fonts loaded")),(A=o&&o(this))&&A.totalTime&&(this._data.anim=S?A.totalTime(S):A),f&&u&&this.elements.forEach((t,e)=>{P[e].width=t.offsetWidth,E&&E.observe(t)}),this}revert(){var t,e;let{orig:i,anim:n,obs:s}=this._data;return s&&s.disconnect(),i.forEach(({element:t,html:e,ariaL:i,ariaH:n})=>{t.innerHTML=e,i?t.setAttribute("aria-label",i):t.removeAttribute("aria-label"),n?t.setAttribute("aria-hidden",n):t.removeAttribute("aria-hidden")}),this.chars.length=this.words.length=this.lines.length=i.length=this.masks.length=0,this.isSplit=!1,null==r||r.removeEventListener("loadingdone",this._split),n&&(this._data.animTime=n.totalTime(),n.revert()),null==(e=(t=this.vars).onRevert)||e.call(t,this),this}static create(e,i){return new t(e,i)}static register(t){(n=n||t||window.gsap)&&(a=n.utils.toArray,h=n.core.context||h),!s&&window.innerWidth>0&&(r=document.fonts,s=!0)}};x.version="3.13.0";let b=x},5626:(t,e,i)=>{i.d(e,{v:()=>r});var n=i(6668);class r{constructor(){this.subscriptions=[]}add(t){return(0,n.Kq)(this.subscriptions,t),()=>(0,n.Ai)(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n)if(1===n)this.subscriptions[0](t,e,i);else for(let r=0;r<n;r++){let n=this.subscriptions[r];n&&n(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}},5818:(t,e,i)=>{i.d(e,{q:()=>n});let n=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n}},5910:(t,e,i)=>{i.d(e,{p:()=>n});let n=t=>Array.isArray(t)},5920:(t,e,i)=>{i.d(e,{$:()=>s,q:()=>o});var n=i(614);let r=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,s=(t,e)=>i=>!!("string"==typeof i&&r.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),o=(t,e,i)=>r=>{if("string"!=typeof r)return r;let[s,o,a,l]=r.match(n.S);return{[t]:parseFloat(s),[e]:parseFloat(o),[i]:parseFloat(a),alpha:void 0!==l?parseFloat(l):1}}},5982:(t,e,i)=>{i.d(e,{_:()=>f});var n=i(419),r=i(8777),s=i(9515),o=i(8109),a=i(8802),l=i(6333),u=i(6926),h=i(1859);function c(t,e,{delay:i=0,transitionOverride:n,type:d}={}){let{transition:p=t.getDefaultTransition(),transitionEnd:f,...m}=e;n&&(p=n);let g=[],v=d&&t.animationState&&t.animationState.getState()[d];for(let e in m){let n=t.getValue(e,t.latestValues[e]??null),a=m[e];if(void 0===a||v&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(v,e))continue;let c={delay:i,...(0,r.r)(p||{},e)},d=n.get();if(void 0!==d&&!n.isAnimating&&!Array.isArray(a)&&a===d&&!c.velocity)continue;let f=!1;if(window.MotionHandoffAnimation){let i=(0,u.P)(t);if(i){let t=window.MotionHandoffAnimation(i,e,s.Gt);null!==t&&(c.startTime=t,f=!0)}}(0,l.g)(t,e),n.start((0,h.f)(e,n,a,t.shouldReduceMotion&&o.$.has(e)?{type:!1}:c,t,f));let y=n.animation;y&&g.push(y)}return f&&Promise.all(g).then(()=>{s.Gt.update(()=>{f&&(0,a.U)(t,f)})}),g}var d=i(6622);function p(t,e,i={}){let r=(0,n.K)(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:s=t.getDefaultTransition()||{}}=r||{};i.transitionOverride&&(s=i.transitionOverride);let o=r?()=>Promise.all(c(t,r,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:r=0,staggerChildren:o,staggerDirection:a}=s;return function(t,e,i=0,n=0,r=0,s=1,o){let a=[];for(let l of t.variantChildren)l.notify("AnimationStart",e),a.push(p(l,e,{...o,delay:i+("function"==typeof n?0:n)+(0,d.L)(t.variantChildren,l,n,r,s)}).then(()=>l.notify("AnimationComplete",e)));return Promise.all(a)}(t,e,n,r,o,a,i)}:()=>Promise.resolve(),{when:l}=s;if(!l)return Promise.all([o(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[o,a]:[a,o];return t().then(()=>e())}}function f(t,e,i={}){let r;if(t.notify("AnimationStart",e),Array.isArray(e))r=Promise.all(e.map(e=>p(t,e,i)));else if("string"==typeof e)r=p(t,e,i);else{let s="function"==typeof e?(0,n.K)(t,e,i.custom):e;r=Promise.all(c(t,s,i))}return r.then(()=>{t.notify("AnimationComplete",e)})}},6009:(t,e,i)=>{i.d(e,{b:()=>r});var n=i(3972);let r=t=>(t*=2)<1?.5*(0,n.dg)(t):.5*(2-Math.pow(2,-10*(t-1)))},6087:(t,e,i)=>{i.d(e,{j:()=>S});var n=i(3191),r=i(4542),s=i(8606),o=i(4272),a=i(10),l=i(1335),u=i(8476);function h(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}var c=i(9064);function d(t,e){return i=>i>0?e:t}var p=i(3210);let f=(t,e,i)=>{let n=t*t,r=i*(e*e-n)+n;return r<0?0:Math.sqrt(r)},m=[l.u,c.B,u.V];function g(t){let e=m.find(e=>e.test(t));if((0,r.$)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`,"color-not-animatable"),!e)return!1;let i=e.parse(t);return e===u.V&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let r=0,s=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;r=h(a,n,t+1/3),s=h(a,n,t),o=h(a,n,t-1/3)}else r=s=o=i;return{red:Math.round(255*r),green:Math.round(255*s),blue:Math.round(255*o),alpha:n}}(i)),i}let v=(t,e)=>{let i=g(t),n=g(e);if(!i||!n)return d(t,e);let r={...i};return t=>(r.red=f(i.red,n.red,t),r.green=f(i.green,n.green,t),r.blue=f(i.blue,n.blue,t),r.alpha=(0,p.k)(i.alpha,n.alpha,t),c.B.transform(r))},y=new Set(["none","hidden"]);function x(t,e){return i=>(0,p.k)(t,e,i)}function b(t){return"number"==typeof t?x:"string"==typeof t?(0,s.p)(t)?d:o.y.test(t)?v:P:Array.isArray(t)?w:"object"==typeof t?o.y.test(t)?v:T:d}function w(t,e){let i=[...t],n=i.length,r=t.map((t,i)=>b(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=r[e](t);return i}}function T(t,e){let i={...t,...e},n={};for(let r in i)void 0!==t[r]&&void 0!==e[r]&&(n[r]=b(t[r])(t[r],e[r]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let P=(t,e)=>{let i=a.f.createTransformer(e),s=(0,a.V)(t),o=(0,a.V)(e);return s.indexes.var.length===o.indexes.var.length&&s.indexes.color.length===o.indexes.color.length&&s.indexes.number.length>=o.indexes.number.length?y.has(t)&&!o.values.length||y.has(e)&&!s.values.length?function(t,e){return y.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):(0,n.F)(w(function(t,e){let i=[],n={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let s=e.types[r],o=t.indexes[s][n[s]],a=t.values[o]??0;i[r]=a,n[s]++}return i}(s,o),o.values),i):((0,r.$)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`,"complex-values-different"),d(t,e))};function S(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?(0,p.k)(t,e,i):b(t)(t,e)}},6295:(t,e,i)=>{i.d(e,{P:()=>nf});var n=i(2115),r=i(18),s=i(4160),o=i(8606);function a({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}var l=i(3210);function u(t){return void 0===t||1===t}function h({scale:t,scaleX:e,scaleY:i}){return!u(t)||!u(e)||!u(i)}function c(t){return h(t)||d(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function d(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function p(t,e,i,n,r){return void 0!==r&&(t=n+r*(t-n)),n+i*(t-n)+e}function f(t,e=0,i=1,n,r){t.min=p(t.min,e,i,n,r),t.max=p(t.max,e,i,n,r)}function m(t,{x:e,y:i}){f(t.x,e.translate,e.scale,e.originPoint),f(t.y,i.translate,i.scale,i.originPoint)}function g(t,e){t.min=t.min+e,t.max=t.max+e}function v(t,e,i,n,r=.5){let s=(0,l.k)(t.min,t.max,r);f(t,e,i,s,n)}function y(t,e){v(t.x,e.x,e.scaleX,e.scale,e.originX),v(t.y,e.y,e.scaleY,e.scale,e.originY)}function x(t,e){return a(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}var b=i(8109),w=i(7887),T=i(4158);let P=t=>e=>e.test(t),S=[w.ai,T.px,T.KN,T.uj,T.vw,T.vh,{test:t=>"auto"===t,parse:t=>t}],E=t=>S.find(P(t));var A=i(4542);let M=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),C=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u;var k=i(7322);let V=t=>/^0[^.\s]+$/u.test(t);var O=i(10),D=i(614);let R=new Set(["brightness","contrast","saturate","opacity"]);function _(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(D.S)||[];if(!n)return t;let r=i.replace(n,""),s=+!!R.has(e);return n!==i&&(s*=100),e+"("+s+r+")"}let B=/\b([a-z-]*)\(.*?\)/gu,L={...O.f,getAnimatableNone:t=>{let e=t.match(B);return e?e.map(_).join(" "):t}};var F=i(4272);let j={...w.ai,transform:Math.round},I={rotate:T.uj,rotateX:T.uj,rotateY:T.uj,rotateZ:T.uj,scale:w.hs,scaleX:w.hs,scaleY:w.hs,scaleZ:w.hs,skew:T.uj,skewX:T.uj,skewY:T.uj,distance:T.px,translateX:T.px,translateY:T.px,translateZ:T.px,x:T.px,y:T.px,z:T.px,perspective:T.px,transformPerspective:T.px,opacity:w.X4,originX:T.gQ,originY:T.gQ,originZ:T.px},N={borderWidth:T.px,borderTopWidth:T.px,borderRightWidth:T.px,borderBottomWidth:T.px,borderLeftWidth:T.px,borderRadius:T.px,radius:T.px,borderTopLeftRadius:T.px,borderTopRightRadius:T.px,borderBottomRightRadius:T.px,borderBottomLeftRadius:T.px,width:T.px,maxWidth:T.px,height:T.px,maxHeight:T.px,top:T.px,right:T.px,bottom:T.px,left:T.px,padding:T.px,paddingTop:T.px,paddingRight:T.px,paddingBottom:T.px,paddingLeft:T.px,margin:T.px,marginTop:T.px,marginRight:T.px,marginBottom:T.px,marginLeft:T.px,backgroundPositionX:T.px,backgroundPositionY:T.px,...I,zIndex:j,fillOpacity:w.X4,strokeOpacity:w.X4,numOctaves:j},W={...N,color:F.y,backgroundColor:F.y,outlineColor:F.y,fill:F.y,stroke:F.y,borderColor:F.y,borderTopColor:F.y,borderRightColor:F.y,borderBottomColor:F.y,borderLeftColor:F.y,filter:L,WebkitFilter:L},Y=t=>W[t];function z(t,e){let i=Y(t);return i!==L&&(i=O.f),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let X=new Set(["auto","none","0"]);var U=i(280);class G extends k.h{constructor(t,e,i,n,r){super(t,e,i,n,r,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&(n=n.trim(),(0,o.p)(n))){let r=function t(e,i,n=1){(0,A.V)(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`,"max-css-var-depth");let[r,s]=function(t){let e=C.exec(t);if(!e)return[,];let[,i,n,r]=e;return[`--${i??n}`,r]}(e);if(!r)return;let a=window.getComputedStyle(i).getPropertyValue(r);if(a){let t=a.trim();return M(t)?parseFloat(t):t}return(0,o.p)(s)?t(s,i,n+1):s}(n,e.current);void 0!==r&&(t[i]=r),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!b.$.has(i)||2!==t.length)return;let[n,r]=t,s=E(n),a=E(r);if(s!==a)if((0,U.E4)(s)&&(0,U.E4)(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else U.Hr[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;(null===t[e]||("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||V(n)))&&i.push(e)}i.length&&function(t,e,i){let n,r=0;for(;r<t.length&&!n;){let e=t[r];"string"==typeof e&&!X.has(e)&&(0,O.V)(e).values.length&&(n=t[r]),r++}if(n&&i)for(let r of e)t[r]=z(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=U.Hr[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let n=t.getValue(e);n&&n.jump(this.measuredOrigin,!1);let r=i.length-1,s=i[r];i[r]=U.Hr[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==s&&void 0===this.finalKeyframe&&(this.finalKeyframe=s),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}var q=i(4803),H=i(4261),$=i(9515),K=i(98);let Q=[...S,F.y,O.f],{schedule:Z}=(0,i(8437).I)(queueMicrotask,!1);var J=i(5626);let tt={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},te={};for(let t in tt)te[t]={isEnabled:e=>tt[t].some(t=>!!e[t])};let ti=()=>({translate:0,scale:1,origin:0,originPoint:0}),tn=()=>({x:ti(),y:ti()}),tr=()=>({min:0,max:0}),ts=()=>({x:tr(),y:tr()});var to=i(8972);let ta={current:null},tl={current:!1},tu=new WeakMap;function th(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function tc(t){return"string"==typeof t||Array.isArray(t)}let td=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],tp=["initial",...td];function tf(t){return th(t.animate)||tp.some(e=>tc(t[e]))}function tm(t){return!!(tf(t)||t.variants)}var tg=i(2735);let tv=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class ty{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:r,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=k.h,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=H.k.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,$.Gt.render(this.render,!1,!0))};let{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!r,this.isControllingVariants=tf(e),this.isVariantNode=tm(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:u,...h}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in h){let e=h[t];void 0!==a[t]&&(0,q.S)(e)&&e.set(a[t])}}mount(t){this.current=t,tu.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),tl.current||function(){if(tl.current=!0,to.B)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ta.current=t.matches;t.addEventListener("change",e),e()}else ta.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ta.current),this.parent?.addChild(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),(0,$.WG)(this.notifyUpdate),(0,$.WG)(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent?.removeChild(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}addChild(t){this.children.add(t),this.enteringChildren??(this.enteringChildren=new Set),this.enteringChildren.add(t)}removeChild(t){this.children.delete(t),this.enteringChildren&&this.enteringChildren.delete(t)}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=r.f.has(t);n&&this.onBindTransform&&this.onBindTransform();let s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&$.Gt.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0),this.scheduleRender()});window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in te){let e=te[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):ts()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<tv.length;e++){let i=tv[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let r=e[n],s=i[n];if((0,q.S)(r))t.addValue(n,r);else if((0,q.S)(s))t.addValue(n,(0,K.OQ)(r,{owner:t}));else if(s!==r)if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(r):e.hasAnimated||e.set(r)}else{let e=t.getStaticValue(n);t.addValue(n,(0,K.OQ)(void 0!==e?e:r,{owner:t}))}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=(0,K.OQ)(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];if(null!=i){if("string"==typeof i&&(M(i)||V(i)))i=parseFloat(i);else{let n;n=i,!Q.find(P(n))&&O.f.test(e)&&(i=z(t,e))}this.setBaseTarget(t,(0,q.S)(i)?i.get():i)}return(0,q.S)(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let n=(0,tg.a)(this.props,i,this.presenceContext?.custom);n&&(e=n[t])}if(i&&void 0!==e)return e;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||(0,q.S)(n)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new J.v),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}scheduleRenderMicrotask(){Z.render(this.render)}}class tx extends ty{constructor(){super(...arguments),this.KeyframeResolver=G}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;(0,q.S)(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}let tb=(t,e)=>e&&"number"==typeof t?e.transform(t):t,tw={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},tT=r.U.length;function tP(t,e,i){let{style:n,vars:s,transformOrigin:a}=t,l=!1,u=!1;for(let t in e){let i=e[t];if(r.f.has(t)){l=!0;continue}if((0,o.j)(t)){s[t]=i;continue}{let e=tb(i,N[t]);t.startsWith("origin")?(u=!0,a[t]=e):n[t]=e}}if(!e.transform&&(l||i?n.transform=function(t,e,i){let n="",s=!0;for(let o=0;o<tT;o++){let a=r.U[o],l=t[a];if(void 0===l)continue;let u=!0;if(!(u="number"==typeof l?l===+!!a.startsWith("scale"):0===parseFloat(l))||i){let t=tb(l,N[a]);if(!u){s=!1;let e=tw[a]||a;n+=`${e}(${t}) `}i&&(e[a]=t)}}return n=n.trim(),i?n=i(e,s?"":n):s&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:i=0}=a;n.transformOrigin=`${t} ${e} ${i}`}}function tS(t,{style:e,vars:i},n,r){let s,o=t.style;for(s in e)o[s]=e[s];for(s in r?.applyProjectionStyles(o,n),i)o.setProperty(s,i[s])}let tE={};function tA(t,{layout:e,layoutId:i}){return r.f.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!tE[t]||"opacity"===t)}function tM(t,e,i){let{style:n}=t,r={};for(let s in n)((0,q.S)(n[s])||e.style&&(0,q.S)(e.style[s])||tA(s,t)||i?.getValue(s)?.liveStyle!==void 0)&&(r[s]=n[s]);return r}class tC extends tx{constructor(){super(...arguments),this.type="html",this.renderInstance=tS}readValueFromInstance(t,e){if(r.f.has(e))return this.projection?.isProjecting?(0,s.zs)(e):(0,s.Ib)(t,e);{let i=window.getComputedStyle(t),n=((0,o.j)(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return x(t,e)}build(t,e,i){tP(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return tM(t,e,i)}}var tk=i(8450);let tV={offset:"stroke-dashoffset",array:"stroke-dasharray"},tO={offset:"strokeDashoffset",array:"strokeDasharray"};function tD(t,{attrX:e,attrY:i,attrScale:n,pathLength:r,pathSpacing:s=1,pathOffset:o=0,...a},l,u,h){if(tP(t,a,u),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:d}=t;c.transform&&(d.transform=c.transform,delete c.transform),(d.transform||c.transformOrigin)&&(d.transformOrigin=c.transformOrigin??"50% 50%",delete c.transformOrigin),d.transform&&(d.transformBox=h?.transformBox??"fill-box",delete c.transformBox),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==n&&(c.scale=n),void 0!==r&&function(t,e,i=1,n=0,r=!0){t.pathLength=1;let s=r?tV:tO;t[s.offset]=T.px.transform(-n);let o=T.px.transform(e),a=T.px.transform(i);t[s.array]=`${o} ${a}`}(c,r,s,o,!1)}let tR=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]),t_=t=>"string"==typeof t&&"svg"===t.toLowerCase();function tB(t,e,i){let n=tM(t,e,i);for(let i in t)((0,q.S)(t[i])||(0,q.S)(e[i]))&&(n[-1!==r.U.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}class tL extends tx{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=ts}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(r.f.has(e)){let t=Y(e);return t&&t.default||0}return e=tR.has(e)?e:(0,tk.I)(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return tB(t,e,i)}build(t,e,i){tD(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,n){for(let i in tS(t,e,void 0,n),e.attrs)t.setAttribute(tR.has(i)?i:(0,tk.I)(i),e.attrs[i])}mount(t){this.isSVGTag=t_(t.tagName),super.mount(t)}}let tF=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function tj(t){if("string"!=typeof t||t.includes("-"));else if(tF.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var tI=i(5155);let tN=(0,n.createContext)({}),tW=(0,n.createContext)({strict:!1});var tY=i(1508);let tz=(0,n.createContext)({});function tX(t){return Array.isArray(t)?t.join(" "):t}let tU=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function tG(t,e,i){for(let n in e)(0,q.S)(e[n])||tA(n,i)||(t[n]=e[n])}let tq=()=>({...tU(),attrs:{}}),tH=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function t$(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||tH.has(t)}let tK=t=>!t$(t);try{!function(t){"function"==typeof t&&(tK=e=>e.startsWith("on")?!t$(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let tQ=(0,n.createContext)(null);var tZ=i(2885);function tJ(t){return(0,q.S)(t)?t.get():t}let t0=t=>(e,i)=>{let r=(0,n.useContext)(tz),s=(0,n.useContext)(tQ),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,n,r){return{latestValues:function(t,e,i,n){let r={},s=n(t,{});for(let t in s)r[t]=tJ(s[t]);let{initial:o,animate:a}=t,l=tf(t),u=tm(t);e&&u&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let h=!!i&&!1===i.initial,c=(h=h||!1===o)?a:o;if(c&&"boolean"!=typeof c&&!th(c)){let e=Array.isArray(c)?c:[c];for(let i=0;i<e.length;i++){let n=(0,tg.a)(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(i,n,r,t),renderState:e()}})(t,e,r,s);return i?o():(0,tZ.M)(o)},t1=t0({scrapeMotionValuesFromProps:tM,createRenderState:tU}),t2=t0({scrapeMotionValuesFromProps:tB,createRenderState:tq}),t5=Symbol.for("motionComponentSymbol");function t3(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}var t8=i(1788);let t9=(0,n.createContext)({});var t4=i(7494);function t7(t){var e,i;let{forwardMotionProps:r=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},s=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0;s&&function(t){for(let e in t)te[e]={...te[e],...t[e]}}(s);let a=tj(t)?t2:t1;function l(e,i){var s;let l,u={...(0,n.useContext)(tY.Q),...e,layoutId:function(t){let{layoutId:e}=t,i=(0,n.useContext)(tN).id;return i&&void 0!==e?i+"-"+e:e}(e)},{isStatic:h}=u,c=function(t){let{initial:e,animate:i}=function(t,e){if(tf(t)){let{initial:e,animate:i}=t;return{initial:!1===e||tc(e)?e:void 0,animate:tc(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,n.useContext)(tz));return(0,n.useMemo)(()=>({initial:e,animate:i}),[tX(e),tX(i)])}(e),d=a(e,h);if(!h&&to.B){(0,n.useContext)(tW).strict;let e=function(t){let{drag:e,layout:i}=te;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(u);l=e.MeasureLayout,c.visualElement=function(t,e,i,r,s){let{visualElement:o}=(0,n.useContext)(tz),a=(0,n.useContext)(tW),l=(0,n.useContext)(tQ),u=(0,n.useContext)(tY.Q).reducedMotion,h=(0,n.useRef)(null);r=r||a.renderer,!h.current&&r&&(h.current=r(t,{visualState:e,parent:o,props:i,presenceContext:l,blockInitialAnimation:!!l&&!1===l.initial,reducedMotionConfig:u}));let c=h.current,d=(0,n.useContext)(t9);c&&!c.projection&&s&&("html"===c.type||"svg"===c.type)&&function(t,e,i,n){let{layoutId:r,layout:s,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u,layoutCrossfade:h}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:r,layout:s,alwaysMeasureLayout:!!o||a&&t3(a),visualElement:t,animationType:"string"==typeof s?s:"both",initialPromotionConfig:n,crossfade:h,layoutScroll:l,layoutRoot:u})}(h.current,i,s,d);let p=(0,n.useRef)(!1);(0,n.useInsertionEffect)(()=>{c&&p.current&&c.update(i,l)});let f=i[t8.n],m=(0,n.useRef)(!!f&&!window.MotionHandoffIsComplete?.(f)&&window.MotionHasOptimisedAnimation?.(f));return(0,t4.E)(()=>{c&&(p.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),c.scheduleRenderMicrotask(),m.current&&c.animationState&&c.animationState.animateChanges())}),(0,n.useEffect)(()=>{c&&(!m.current&&c.animationState&&c.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(f)}),m.current=!1),c.enteringChildren=void 0)}),c}(t,d,u,o,e.ProjectionNode)}return(0,tI.jsxs)(tz.Provider,{value:c,children:[l&&c.visualElement?(0,tI.jsx)(l,{visualElement:c.visualElement,...u}):null,function(t,e,i,{latestValues:r},s,o=!1){let a=(tj(t)?function(t,e,i,r){let s=(0,n.useMemo)(()=>{let i=tq();return tD(i,e,t_(r),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};tG(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},r=function(t,e){let i=t.style||{},r={};return tG(r,i,t),Object.assign(r,function({transformTemplate:t},e){return(0,n.useMemo)(()=>{let i=tU();return tP(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),r}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=r,i})(e,r,s,t),l=function(t,e,i){let n={};for(let r in t)("values"!==r||"object"!=typeof t.values)&&(tK(r)||!0===i&&t$(r)||!e&&!t$(r)||t.draggable&&r.startsWith("onDrag"))&&(n[r]=t[r]);return n}(e,"string"==typeof t,o),u=t!==n.Fragment?{...l,...a,ref:i}:{},{children:h}=e,c=(0,n.useMemo)(()=>(0,q.S)(h)?h.get():h,[h]);return(0,n.createElement)(t,{...u,children:c})}(t,e,(s=c.visualElement,(0,n.useCallback)(t=>{t&&d.onMount&&d.onMount(t),s&&(t?s.mount(t):s.unmount()),i&&("function"==typeof i?i(t):t3(i)&&(i.current=t))},[s])),d,h,r)]})}l.displayName="motion.".concat("string"==typeof t?t:"create(".concat(null!=(i=null!=(e=t.displayName)?e:t.name)?i:"",")"));let u=(0,n.forwardRef)(l);return u[t5]=t,u}var t6=i(5982),et=i(6622),ee=i(5910);function ei(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}let en=tp.length;var er=i(419);let es=[...td].reverse(),eo=td.length;function ea(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function el(){return{animate:ea(!0),whileInView:ea(),whileHover:ea(),whileTap:ea(),whileDrag:ea(),whileFocus:ea(),exit:ea()}}class eu{constructor(t){this.isMounted=!1,this.node=t}update(){}}class eh extends eu{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(0,t6._)(t,e,i))),i=el(),n=!0,r=e=>(i,n)=>{let r=(0,er.K)(t,n,"exit"===e?t.presenceContext?.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function s(s){let{props:o}=t,a=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<en;t++){let n=tp[t],r=e.props[n];(tc(r)||!1===r)&&(i[n]=r)}return i}(t.parent)||{},l=[],u=new Set,h={},c=1/0;for(let e=0;e<eo;e++){var d,p;let f=es[e],m=i[f],g=void 0!==o[f]?o[f]:a[f],v=tc(g),y=f===s?m.isActive:null;!1===y&&(c=e);let x=g===a[f]&&g!==o[f]&&v;if(x&&n&&t.manuallyAnimateOnMount&&(x=!1),m.protectedKeys={...h},!m.isActive&&null===y||!g&&!m.prevProp||th(g)||"boolean"==typeof g)continue;let b=(d=m.prevProp,"string"==typeof(p=g)?p!==d:!!Array.isArray(p)&&!ei(p,d)),w=b||f===s&&m.isActive&&!x&&v||e>c&&v,T=!1,P=Array.isArray(g)?g:[g],S=P.reduce(r(f),{});!1===y&&(S={});let{prevResolvedValues:E={}}=m,A={...E,...S},M=e=>{w=!0,u.has(e)&&(T=!0,u.delete(e)),m.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in A){let e=S[t],i=E[t];if(!h.hasOwnProperty(t))((0,ee.p)(e)&&(0,ee.p)(i)?ei(e,i):e===i)?void 0!==e&&u.has(t)?M(t):m.protectedKeys[t]=!0:null!=e?M(t):u.add(t)}m.prevProp=g,m.prevResolvedValues=S,m.isActive&&(h={...h,...S}),n&&t.blockInitialAnimation&&(w=!1);let C=x&&b,k=!C||T;w&&k&&l.push(...P.map(e=>{let i={type:f};if("string"==typeof e&&n&&!C&&t.manuallyAnimateOnMount&&t.parent){let{parent:n}=t,r=(0,er.K)(n,e);if(n.enteringChildren&&r){let{delayChildren:e}=r.transition||{};i.delay=(0,et.L)(n.enteringChildren,t,e)}}return{animation:e,options:i}}))}if(u.size){let e={};if("boolean"!=typeof o.initial){let i=(0,er.K)(t,Array.isArray(o.initial)?o.initial[0]:o.initial);i&&i.transition&&(e.transition=i.transition)}u.forEach(i=>{let n=t.getBaseTarget(i),r=t.getValue(i);r&&(r.liveStyle=!0),e[i]=n??null}),l.push({animation:e})}let f=!!l.length;return n&&(!1===o.initial||o.initial===o.animate)&&!t.manuallyAnimateOnMount&&(f=!1),n=!1,f?e(l):Promise.resolve()}return{animateChanges:s,setActive:function(e,n){if(i[e].isActive===n)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,n)),i[e].isActive=n;let r=s(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=el(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();th(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let ec=0;class ed extends eu{constructor(){super(...arguments),this.id=ec++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}var ep=i(9827);let ef={x:!1,y:!1};var em=i(1859);function eg(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}let ev=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function ey(t){return{point:{x:t.pageX,y:t.pageY}}}function ex(t,e,i,n){return eg(t,e,t=>ev(t)&&i(t,ey(t)),n)}function eb(t){return t.max-t.min}function ew(t,e,i,n=.5){t.origin=n,t.originPoint=(0,l.k)(e.min,e.max,t.origin),t.scale=eb(i)/eb(e),t.translate=(0,l.k)(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function eT(t,e,i,n){ew(t.x,e.x,i.x,n?n.originX:void 0),ew(t.y,e.y,i.y,n?n.originY:void 0)}function eP(t,e,i){t.min=i.min+e.min,t.max=t.min+eb(e)}function eS(t,e,i){t.min=e.min-i.min,t.max=t.min+eb(e)}function eE(t,e,i){eS(t.x,e.x,i.x),eS(t.y,e.y,i.y)}function eA(t){return[t("x"),t("y")]}let eM=({current:t})=>t?t.ownerDocument.defaultView:null;var eC=i(6333),ek=i(3191),eV=i(7215);let eO=(t,e)=>Math.abs(t-e);class eD{constructor(t,e,{transformPagePoint:i,contextWindow:n=window,dragSnapToOrigin:r=!1,distanceThreshold:s=3}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=eB(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(eO(t.x,e.x)**2+eO(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=this.distanceThreshold;if(!e&&!i)return;let{point:n}=t,{timestamp:r}=$.uv;this.history.push({...n,timestamp:r});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=eR(e,this.transformPagePoint),$.Gt.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:r}=this.handlers;if(this.dragSnapToOrigin&&r&&r(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=eB("pointercancel"===t.type?this.lastMoveEventInfo:eR(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,s),n&&n(t,s)},!ev(t))return;this.dragSnapToOrigin=r,this.handlers=e,this.transformPagePoint=i,this.distanceThreshold=s,this.contextWindow=n||window;let o=eR(ey(t),this.transformPagePoint),{point:a}=o,{timestamp:l}=$.uv;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=e;u&&u(t,eB(o,this.history)),this.removeListeners=(0,ek.F)(ex(this.contextWindow,"pointermove",this.handlePointerMove),ex(this.contextWindow,"pointerup",this.handlePointerUp),ex(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,$.WG)(this.updatePoint)}}function eR(t,e){return e?{point:e(t.point)}:t}function e_(t,e){return{x:t.x-e.x,y:t.y-e.y}}function eB({point:t},e){return{point:t,delta:e_(t,eL(e)),offset:e_(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,r=eL(t);for(;i>=0&&(n=t[i],!(r.timestamp-n.timestamp>(0,eV.f)(.1)));)i--;if(!n)return{x:0,y:0};let s=(0,eV.X)(r.timestamp-n.timestamp);if(0===s)return{x:0,y:0};let o={x:(r.x-n.x)/s,y:(r.y-n.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,.1)}}function eL(t){return t[t.length-1]}var eF=i(5818),ej=i(1297);function eI(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function eN(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function eW(t,e,i){return{min:eY(t,e),max:eY(t,i)}}function eY(t,e){return"number"==typeof t?t:t[e]||0}let ez=new WeakMap;class eX{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=ts(),this.latestPointerEvent=null,this.latestPanInfo=null,this.visualElement=t}start(t,{snapToCursor:e=!1,distanceThreshold:i}={}){let{presenceContext:n}=this.visualElement;if(n&&!1===n.isPresent)return;let r=t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ey(t).point)},s=(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:r}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(ef[t])return null;else return ef[t]=!0,()=>{ef[t]=!1};return ef.x||ef.y?null:(ef.x=ef.y=!0,()=>{ef.x=ef.y=!1})}(i),!this.openDragLock))return;this.latestPointerEvent=t,this.latestPanInfo=e,this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),eA(t=>{let e=this.getAxisMotionValue(t).get()||0;if(T.KN.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];n&&(e=eb(n)*(parseFloat(e)/100))}}this.originPoint[t]=e}),r&&$.Gt.postRender(()=>r(t,e)),(0,eC.g)(this.visualElement,"transform");let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},o=(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e;let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:r,onDrag:s}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&r&&r(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},a=(t,e)=>{this.latestPointerEvent=t,this.latestPanInfo=e,this.stop(t,e),this.latestPointerEvent=null,this.latestPanInfo=null},l=()=>eA(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play()),{dragSnapToOrigin:u}=this.getProps();this.panSession=new eD(t,{onSessionStart:r,onStart:s,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,distanceThreshold:i,contextWindow:eM(this.visualElement)})}stop(t,e){let i=t||this.latestPointerEvent,n=e||this.latestPanInfo,r=this.isDragging;if(this.cancel(),!r||!n||!i)return;let{velocity:s}=n;this.startAnimation(s);let{onDragEnd:o}=this.getProps();o&&$.Gt.postRender(()=>o(i,n))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!eU(t,n,this.currentDirection))return;let r=this.getAxisMotionValue(t),s=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?(0,l.k)(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?(0,l.k)(i,t,n.max):Math.min(t,i)),t}(s,this.constraints[t],this.elastic[t])),r.set(s)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,n=this.constraints;t&&t3(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:n,right:r}){return{x:eI(t.x,i,r),y:eI(t.y,e,n)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:eW(t,"left","right"),y:eW(t,"top","bottom")}}(e),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&eA(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!t3(e))return!1;let n=e.current;(0,A.V)(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.","drag-constraints-ref");let{projection:r}=this.visualElement;if(!r||!r.layout)return!1;let s=function(t,e,i){let n=x(t,i),{scroll:r}=e;return r&&(g(n.x,r.offset.x),g(n.y,r.offset.y)),n}(n,r.root,this.visualElement.getTransformPagePoint()),o=(t=r.layout.layoutBox,{x:eN(t.x,s.x),y:eN(t.y,s.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=a(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:r,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(eA(o=>{if(!eU(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...r,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return(0,eC.g)(this.visualElement,t),i.start((0,em.f)(t,i,0,e,this.visualElement,!1))}stopAnimation(){eA(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){eA(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){eA(e=>{let{drag:i}=this.getProps();if(!eU(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,r=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:s}=n.layout.layoutBox[e];r.set(t[e]-(0,l.k)(i,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!t3(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};eA(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=eb(t),r=eb(e);return r>n?i=(0,eF.q)(e.min,e.max-n,t.min):n>r&&(i=(0,eF.q)(t.min,t.max-r,e.min)),(0,ej.q)(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:r}=this.visualElement.getProps();this.visualElement.current.style.transform=r?r({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),eA(e=>{if(!eU(e,t,null))return;let i=this.getAxisMotionValue(e),{min:r,max:s}=this.constraints[e];i.set((0,l.k)(r,s,n[e]))})}addListeners(){if(!this.visualElement.current)return;ez.set(this.visualElement,this);let t=ex(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();t3(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),$.Gt.read(e);let r=eg(window,"resize",()=>this.scalePositionWithinConstraints()),s=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(eA(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{r(),t(),n(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:r=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:r,dragElastic:s,dragMomentum:o}}}function eU(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class eG extends eu{constructor(t){super(t),this.removeGroupControls=ep.l,this.removeListeners=ep.l,this.controls=new eX(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||ep.l}unmount(){this.removeGroupControls(),this.removeListeners()}}let eq=t=>(e,i)=>{t&&$.Gt.postRender(()=>t(e,i))};class eH extends eu{constructor(){super(...arguments),this.removePointerDownListener=ep.l}onPointerDown(t){this.session=new eD(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:eM(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:eq(t),onStart:eq(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&$.Gt.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=ex(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let e$={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function eK(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let eQ={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!T.px.test(t))return t;else t=parseFloat(t);let i=eK(t,e.target.x),n=eK(t,e.target.y);return`${i}% ${n}%`}},eZ=!1;class eJ extends n.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:r}=t;for(let t in e1)tE[t]=e1[t],(0,o.j)(t)&&(tE[t].isCSSVariable=!0);r&&(e.group&&e.group.add(r),i&&i.register&&n&&i.register(r),eZ&&r.root.didUpdate(),r.addEventListener("animationComplete",()=>{this.safeToRemove()}),r.setOptions({...r.options,onExitComplete:()=>this.safeToRemove()})),e$.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:r}=this.props,{projection:s}=i;return s&&(s.isPresent=r,eZ=!0,n||t.layoutDependency!==e||void 0===e||t.isPresent!==r?s.willUpdate():this.safeToRemove(),t.isPresent!==r&&(r?s.promote():s.relegate()||$.Gt.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),Z.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;eZ=!0,n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function e0(t){let[e,i]=function(t=!0){let e=(0,n.useContext)(tQ);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:r,register:s}=e,o=(0,n.useId)();(0,n.useEffect)(()=>{if(t)return s(o)},[t]);let a=(0,n.useCallback)(()=>t&&r&&r(o),[o,r,t]);return!i&&r?[!1,a]:[!0]}(),r=(0,n.useContext)(tN);return(0,tI.jsx)(eJ,{...t,layoutGroup:r,switchLayoutGroup:(0,n.useContext)(t9),isPresent:e,safeToRemove:i})}let e1={borderRadius:{...eQ,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:eQ,borderTopRightRadius:eQ,borderBottomLeftRadius:eQ,borderBottomRightRadius:eQ,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=O.f.parse(t);if(n.length>5)return t;let r=O.f.createTransformer(t),s=+("number"!=typeof n[0]),o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+s]/=o,n[1+s]/=a;let u=(0,l.k)(o,a,.5);return"number"==typeof n[2+s]&&(n[2+s]/=u),"number"==typeof n[3+s]&&(n[3+s]/=u),r(n)}}};var e2=i(4744),e5=i(9782),e3=i(8777),e8=i(3704),e9=i(6926),e4=i(6668);let e7=(t,e)=>t.depth-e.depth;class e6{constructor(){this.children=[],this.isDirty=!1}add(t){(0,e4.Kq)(this.children,t),this.isDirty=!0}remove(t){(0,e4.Ai)(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(e7),this.isDirty=!1,this.children.forEach(t)}}var it=i(7712);let ie=["TopLeft","TopRight","BottomLeft","BottomRight"],ii=ie.length,ir=t=>"string"==typeof t?parseFloat(t):t,is=t=>"number"==typeof t||T.px.test(t);function io(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let ia=iu(0,.5,it.yT),il=iu(.5,.95,ep.l);function iu(t,e,i){return n=>n<t?0:n>e?1:i((0,eF.q)(t,e,n))}function ih(t,e){t.min=e.min,t.max=e.max}function ic(t,e){ih(t.x,e.x),ih(t.y,e.y)}function id(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function ip(t,e,i,n,r){return t-=e,t=n+1/i*(t-n),void 0!==r&&(t=n+1/r*(t-n)),t}function im(t,e,[i,n,r],s,o){!function(t,e=0,i=1,n=.5,r,s=t,o=t){if(T.KN.test(e)&&(e=parseFloat(e),e=(0,l.k)(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=(0,l.k)(s.min,s.max,n);t===s&&(a-=e),t.min=ip(t.min,e,i,a,r),t.max=ip(t.max,e,i,a,r)}(t,e[i],e[n],e[r],e.scale,s,o)}let ig=["x","scaleX","originX"],iv=["y","scaleY","originY"];function iy(t,e,i,n){im(t.x,e,ig,i?i.x:void 0,n?n.x:void 0),im(t.y,e,iv,i?i.y:void 0,n?n.y:void 0)}function ix(t){return 0===t.translate&&1===t.scale}function ib(t){return ix(t.x)&&ix(t.y)}function iw(t,e){return t.min===e.min&&t.max===e.max}function iT(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function iP(t,e){return iT(t.x,e.x)&&iT(t.y,e.y)}function iS(t){return eb(t.x)/eb(t.y)}function iE(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class iA{constructor(){this.members=[]}add(t){(0,e4.Kq)(this.members,t),t.scheduleRender()}remove(t){if((0,e4.Ai)(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let iM={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},iC=["","X","Y","Z"],ik=0;function iV(t,e,i,n){let{latestValues:r}=e;r[t]&&(i[t]=r[t],e.setStaticValue(t,0),n&&(n[t]=0))}function iO({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:r}){return class{constructor(t={},i=e?.()){this.id=ik++,this.animationId=0,this.animationCommitId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,e2.Q.value&&(iM.nodes=iM.calculatedTargetDeltas=iM.calculatedProjections=0),this.nodes.forEach(i_),this.nodes.forEach(iW),this.nodes.forEach(iY),this.nodes.forEach(iB),e2.Q.addProjectionMetrics&&e2.Q.addProjectionMetrics(iM)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new e6)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new J.v),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=(0,e5.x)(e)&&(!(0,e5.x)(e)||"svg"!==e.tagName),this.instance=e;let{layoutId:i,layout:n,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(n||i)&&(this.isLayoutDirty=!0),t){let i,n=0,r=()=>this.root.updateBlockedByResize=!1;$.Gt.read(()=>{n=window.innerWidth}),t(e,()=>{let t=window.innerWidth;t!==n&&(n=t,this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=H.k.now(),n=({timestamp:e})=>{let r=e-i;r>=250&&((0,$.WG)(n),t(r-250))};return $.Gt.setup(n,!0),()=>(0,$.WG)(n)}(r,250),e$.hasAnimatedSinceResize&&(e$.hasAnimatedSinceResize=!1,this.nodes.forEach(iN)))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&r&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||iH,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!iP(this.targetLayout,n),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...(0,e3.r)(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||iN(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),(0,$.WG)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(iz),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=(0,e9.P)(i);if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",$.Gt,!(t||i))}let{parent:r}=e;r&&!r.hasCheckedOptimisedAppear&&t(r)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(iF);return}if(this.animationId<=this.animationCommitId)return void this.nodes.forEach(ij);this.animationCommitId=this.animationId,this.isUpdating?(this.isUpdating=!1,this.nodes.forEach(iI),this.nodes.forEach(iD),this.nodes.forEach(iR)):this.nodes.forEach(ij),this.clearAllSnapshots();let t=H.k.now();$.uv.delta=(0,ej.q)(0,1e3/60,t-$.uv.timestamp),$.uv.timestamp=t,$.uv.isProcessing=!0,$.PP.update.process($.uv),$.PP.preRender.process($.uv),$.PP.render.process($.uv),$.uv.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,Z.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(iL),this.sharedNodes.forEach(iX)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,$.Gt.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){$.Gt.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||eb(this.snapshot.measuredBox.x)||eb(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=ts(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!r)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!ib(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,s=n!==this.prevTransformTemplateValue;t&&this.instance&&(e||c(this.latestValues)||s)&&(r(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),iQ((e=n).x),iQ(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return ts();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(iJ))){let{scroll:t}=this.root;t&&(g(e.x,t.offset.x),g(e.y,t.offset.y))}return e}removeElementScroll(t){let e=ts();if(ic(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let n=this.path[i],{scroll:r,options:s}=n;n!==this.root&&r&&s.layoutScroll&&(r.wasRoot&&ic(e,t),g(e.x,r.offset.x),g(e.y,r.offset.y))}return e}applyTransform(t,e=!1){let i=ts();ic(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&y(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),c(n.latestValues)&&y(i,n.latestValues)}return c(this.latestValues)&&y(i,this.latestValues),i}removeTransform(t){let e=ts();ic(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!c(i.latestValues))continue;h(i.latestValues)&&i.updateSnapshot();let n=ts();ic(n,i.measurePageBox()),iy(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return c(this.latestValues)&&iy(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==$.uv.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:n,layoutId:r}=this.options;if(this.layout&&(n||r)){if(this.resolvedRelativeTargetAt=$.uv.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ts(),this.relativeTargetOrigin=ts(),eE(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),ic(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=ts(),this.targetWithTransforms=ts()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var s,o,a;this.forceRelativeParentToResolveTarget(),s=this.target,o=this.relativeTarget,a=this.relativeParent.target,eP(s.x,o.x,a.x),eP(s.y,o.y,a.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):ic(this.target,this.layout.layoutBox),m(this.target,this.targetDelta)):ic(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=ts(),this.relativeTargetOrigin=ts(),eE(this.relativeTargetOrigin,this.target,t.target),ic(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}e2.Q.value&&iM.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||h(this.parent.latestValues)||d(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===$.uv.timestamp&&(i=!1),i)return;let{layout:n,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||r))return;ic(this.layoutCorrected,this.layout.layoutBox);let s=this.treeScale.x,o=this.treeScale.y;!function(t,e,i,n=!1){let r,s,o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(r=i[a]).projectionDelta;let{visualElement:o}=r.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&r.options.layoutScroll&&r.scroll&&r!==r.root&&y(t,{x:-r.scroll.offset.x,y:-r.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,m(t,s)),n&&c(r.latestValues)&&y(t,r.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=ts());let{target:a}=t;if(!a){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(id(this.prevProjectionDelta.x,this.projectionDelta.x),id(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),eT(this.projectionDelta,this.layoutCorrected,a,this.latestValues),this.treeScale.x===s&&this.treeScale.y===o&&iE(this.projectionDelta.x,this.prevProjectionDelta.x)&&iE(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",a)),e2.Q.value&&iM.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=tn(),this.projectionDelta=tn(),this.projectionDeltaWithTransform=tn()}setAnimationOrigin(t,e=!1){let i,n=this.snapshot,r=n?n.latestValues:{},s={...this.latestValues},o=tn();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=ts(),u=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),c=!h||h.members.length<=1,d=!!(u&&!c&&!0===this.options.crossfade&&!this.path.some(iq));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(iU(o.x,t.x,n),iU(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,p,f,m,g,v;eE(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),f=this.relativeTarget,m=this.relativeTargetOrigin,g=a,v=n,iG(f.x,m.x,g.x,v),iG(f.y,m.y,g.y,v),i&&(h=this.relativeTarget,p=i,iw(h.x,p.x)&&iw(h.y,p.y))&&(this.isProjectionDirty=!1),i||(i=ts()),ic(i,this.relativeTarget)}u&&(this.animationValues=s,function(t,e,i,n,r,s){r?(t.opacity=(0,l.k)(0,i.opacity??1,ia(n)),t.opacityExit=(0,l.k)(e.opacity??1,0,il(n))):s&&(t.opacity=(0,l.k)(e.opacity??1,i.opacity??1,n));for(let r=0;r<ii;r++){let s=`border${ie[r]}Radius`,o=io(e,s),a=io(i,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||is(o)===is(a)?(t[s]=Math.max((0,l.k)(ir(o),ir(a),n),0),(T.KN.test(a)||T.KN.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||i.rotate)&&(t.rotate=(0,l.k)(e.rotate||0,i.rotate||0,n))}(s,r,this.latestValues,n,d,c)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&((0,$.WG)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=$.Gt.update(()=>{e$.hasAnimatedSinceResize=!0,e8.q.layout++,this.motionValue||(this.motionValue=(0,K.OQ)(0)),this.currentAnimation=function(t,e,i){let n=(0,q.S)(t)?t:(0,K.OQ)(t);return n.start((0,em.f)("",n,e,i)),n.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{e8.q.layout--},onComplete:()=>{e8.q.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:r}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&iZ(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||ts();let e=eb(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=eb(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}ic(e,i),y(e,r),eT(this.projectionDeltaWithTransform,this.layoutCorrected,e,r)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new iA),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&iV("z",t,n,this.animationValues);for(let e=0;e<iC.length;e++)iV(`rotate${iC[e]}`,t,n,this.animationValues),iV(`skew${iC[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}applyProjectionStyles(t,e){if(!this.instance||this.isSVG)return;if(!this.isVisible){t.visibility="hidden";return}let i=this.getTransformTemplate();if(this.needsReset){this.needsReset=!1,t.visibility="",t.opacity="",t.pointerEvents=tJ(e?.pointerEvents)||"",t.transform=i?i(this.latestValues,""):"none";return}let n=this.getLead();if(!this.projectionDelta||!this.layout||!n.target){this.options.layoutId&&(t.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,t.pointerEvents=tJ(e?.pointerEvents)||""),this.hasProjected&&!c(this.latestValues)&&(t.transform=i?i({},""):"none",this.hasProjected=!1);return}t.visibility="";let r=n.animationValues||n.latestValues;this.applyTransformsToTarget();let s=function(t,e,i){let n="",r=t.x.translate/e.x,s=t.y.translate/e.y,o=i?.z||0;if((r||s||o)&&(n=`translate3d(${r}px, ${s}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:r,rotateY:s,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),r&&(n+=`rotateX(${r}deg) `),s&&(n+=`rotateY(${s}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,r);i&&(s=i(r,s)),t.transform=s;let{x:o,y:a}=this.projectionDelta;for(let e in t.transformOrigin=`${100*o.origin}% ${100*a.origin}% 0`,n.animationValues?t.opacity=n===this?r.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:r.opacityExit:t.opacity=n===this?void 0!==r.opacity?r.opacity:"":void 0!==r.opacityExit?r.opacityExit:0,tE){if(void 0===r[e])continue;let{correct:i,applyTo:o,isCSSVariable:a}=tE[e],l="none"===s?r[e]:i(r[e],n);if(o){let e=o.length;for(let i=0;i<e;i++)t[o[i]]=l}else a?this.options.visualElement.renderState.vars[e]=l:t[e]=l}this.options.layoutId&&(t.pointerEvents=n===this?tJ(e?.pointerEvents)||"":"none")}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(iF),this.root.sharedNodes.clear()}}}function iD(t){t.updateLayout()}function iR(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:n}=t.layout,{animationType:r}=t.options,s=e.source!==t.layout.source;"size"===r?eA(t=>{let n=s?e.measuredBox[t]:e.layoutBox[t],r=eb(n);n.min=i[t].min,n.max=n.min+r}):iZ(r,e.layoutBox,i)&&eA(n=>{let r=s?e.measuredBox[n]:e.layoutBox[n],o=eb(i[n]);r.max=r.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=tn();eT(o,i,e.layoutBox);let a=tn();s?eT(a,t.applyTransform(n,!0),e.measuredBox):eT(a,i,e.layoutBox);let l=!ib(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:r,layout:s}=n;if(r&&s){let o=ts();eE(o,e.layoutBox,r.layoutBox);let a=ts();eE(a,i,s.layoutBox),iP(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeLayoutChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function i_(t){e2.Q.value&&iM.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function iB(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function iL(t){t.clearSnapshot()}function iF(t){t.clearMeasurements()}function ij(t){t.isLayoutDirty=!1}function iI(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function iN(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function iW(t){t.resolveTargetDelta()}function iY(t){t.calcProjection()}function iz(t){t.resetSkewAndRotation()}function iX(t){t.removeLeadSnapshot()}function iU(t,e,i){t.translate=(0,l.k)(e.translate,0,i),t.scale=(0,l.k)(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function iG(t,e,i,n){t.min=(0,l.k)(e.min,i.min,n),t.max=(0,l.k)(e.max,i.max,n)}function iq(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let iH={duration:.45,ease:[.4,0,.1,1]},i$=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),iK=i$("applewebkit/")&&!i$("chrome/")?Math.round:ep.l;function iQ(t){t.min=iK(t.min),t.max=iK(t.max)}function iZ(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(iS(e)-iS(i)))}function iJ(t){return t!==t.root&&t.scroll?.wasRoot}let i0=iO({attachResizeListener:(t,e)=>eg(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),i1={current:void 0},i2=iO({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!i1.current){let t=new i0({});t.mount(window),t.setOptions({layoutScroll:!0}),i1.current=t}return i1.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});var i5=i(2198);function i3(t,e){let i=(0,i5.K)(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function i8(t){return!("touch"===t.pointerType||ef.x||ef.y)}function i9(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let r=n["onHover"+i];r&&$.Gt.postRender(()=>r(e,ey(e)))}class i4 extends eu{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=i3(t,i),o=t=>{if(!i8(t))return;let{target:i}=t,n=e(i,t);if("function"!=typeof n||!i)return;let s=t=>{i8(t)&&(n(t),i.removeEventListener("pointerleave",s))};i.addEventListener("pointerleave",s,r)};return n.forEach(t=>{t.addEventListener("pointerenter",o,r)}),s}(t,(t,e)=>(i9(this.node,e,"Start"),t=>i9(this.node,t,"End"))))}unmount(){}}class i7 extends eu{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=(0,ek.F)(eg(this.node.current,"focus",()=>this.onFocus()),eg(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}var i6=i(7351);let nt=(t,e)=>!!e&&(t===e||nt(t,e.parentElement)),ne=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),ni=new WeakSet;function nn(t){return e=>{"Enter"===e.key&&t(e)}}function nr(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}function ns(t){return ev(t)&&!(ef.x||ef.y)}function no(t,e,i){let{props:n}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let r=n["onTap"+("End"===i?"":i)];r&&$.Gt.postRender(()=>r(e,ey(e)))}class na extends eu{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,r,s]=i3(t,i),o=t=>{let n=t.currentTarget;if(!ns(t))return;ni.add(n);let s=e(n,t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),ni.has(n)&&ni.delete(n),ns(t)&&"function"==typeof s&&s(t,{success:e})},a=t=>{o(t,n===window||n===document||i.useGlobalTarget||nt(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,r),window.addEventListener("pointercancel",l,r)};return n.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",o,r),(0,i6.s)(t))&&(t.addEventListener("focus",t=>((t,e)=>{let i=t.currentTarget;if(!i)return;let n=nn(()=>{if(ni.has(i))return;nr(i,"down");let t=nn(()=>{nr(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>nr(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)})(t,r)),ne.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),s}(t,(t,e)=>(no(this.node,e,"Start"),(t,{success:e})=>no(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nl=new WeakMap,nu=new WeakMap,nh=t=>{let e=nl.get(t.target);e&&e(t)},nc=t=>{t.forEach(nh)},nd={some:0,all:1};class np extends eu{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:r}=t,s={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:nd[n]},o=t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,r&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),s=e?i:n;s&&s(t)};var a=this.node.current;let l=function({root:t,...e}){let i=t||document;nu.has(i)||nu.set(i,{});let n=nu.get(i),r=JSON.stringify(e);return n[r]||(n[r]=new IntersectionObserver(nc,{root:t,...e})),n[r]}(s);return nl.set(a,o),l.observe(a),()=>{nl.delete(a),l.unobserve(a)}}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nf=function(t,e){if("undefined"==typeof Proxy)return t7;let i=new Map,n=(i,n)=>t7(i,n,t,e);return new Proxy((t,e)=>n(t,e),{get:(r,s)=>"create"===s?n:(i.has(s)||i.set(s,t7(s,void 0,t,e)),i.get(s))})}({animation:{Feature:eh},exit:{Feature:ed},inView:{Feature:np},tap:{Feature:na},focus:{Feature:i7},hover:{Feature:i4},pan:{Feature:eH},drag:{Feature:eG,ProjectionNode:i2,MeasureLayout:e0},layout:{ProjectionNode:i2,MeasureLayout:e0}},(t,e)=>tj(t)?new tL(e):new tC(e,{allowProjection:t!==n.Fragment}))},6330:(t,e,i)=>{i.d(e,{B:()=>s});var n=i(2886),r=i(3945);function s({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:o=10,bounceStiffness:a=500,modifyTarget:l,min:u,max:h,restDelta:c=.5,restSpeed:d}){let p,f,m=t[0],g={done:!1,value:m},v=i*e,y=m+v,x=void 0===l?y:l(y);x!==y&&(v=x-m);let b=t=>-v*Math.exp(-t/s),w=t=>x+b(t),T=t=>{let e=b(t),i=w(t);g.done=Math.abs(e)<=c,g.value=g.done?x:i},P=t=>{let e;if(e=g.value,void 0!==u&&e<u||void 0!==h&&e>h){var i;p=t,f=(0,n.o)({keyframes:[g.value,(i=g.value,void 0===u?h:void 0===h||Math.abs(u-i)<Math.abs(h-i)?u:h)],velocity:(0,r.Y)(w,t,g.value),damping:o,stiffness:a,restDelta:c,restSpeed:d})}};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(f||void 0!==p||(e=!0,T(t),P(t)),void 0!==p&&t>=p)?f.next(t-p):(e||T(t),g)}}}},6333:(t,e,i)=>{i.d(e,{g:()=>s});var n=i(3387),r=i(4803);function s(t,e){let i=t.getValue("willChange");if((0,r.S)(i)&&i.add)return i.add(e);if(!i&&n.W.WillChange){let i=new n.W.WillChange("auto");t.addValue("willChange",i),i.add(e)}}},6622:(t,e,i)=>{i.d(e,{L:()=>n});function n(t,e,i,r=0,s=1){let o=Array.from(t).sort((t,e)=>t.sortNodePosition(e)).indexOf(e),a=t.size,l=(a-1)*r;return"function"==typeof i?i(o,a):1===s?o*r:l-o*r}},6668:(t,e,i)=>{function n(t,e){-1===t.indexOf(e)&&t.push(e)}function r(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}i.d(e,{Ai:()=>r,Kq:()=>n})},6775:(t,e,i)=>{i.d(e,{G:()=>h});var n=i(3387),r=i(9827),s=i(3191),o=i(4542),a=i(5818),l=i(1297),u=i(6087);function h(t,e,{clamp:i=!0,ease:c,mixer:d}={}){let p=t.length;if((0,o.V)(p===e.length,"Both input and output ranges must be the same length","range-length"),1===p)return()=>e[0];if(2===p&&e[0]===e[1])return()=>e[1];let f=t[0]===t[1];t[0]>t[p-1]&&(t=[...t].reverse(),e=[...e].reverse());let m=function(t,e,i){let o=[],a=i||n.W.mix||u.j,l=t.length-1;for(let i=0;i<l;i++){let n=a(t[i],t[i+1]);if(e){let t=Array.isArray(e)?e[i]||r.l:e;n=(0,s.F)(t,n)}o.push(n)}return o}(e,c,d),g=m.length,v=i=>{if(f&&i<t[0])return e[0];let n=0;if(g>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let r=(0,a.q)(t[n],t[n+1],i);return m[n](r)};return i?e=>v((0,l.q)(t[0],t[p-1],e)):v}},6778:(t,e,i)=>{i.d(e,{X:()=>r});let n=t=>null!==t;function r(t,{repeat:e,repeatType:i="loop"},s,o=1){let a=t.filter(n),l=o<0||e&&"loop"!==i&&e%2==1?0:a.length-1;return l&&void 0!==s?s:a[l]}},6836:(t,e,i)=>{i.d(e,{V:()=>o});var n=i(9515),r=i(2115),s=i(8619);function o(t){var e,i;let o=(0,s.d)(t.getVelocity()),a=()=>{let e=t.getVelocity();o.set(e),e&&n.Gt.update(a)};return e="change",i=()=>{n.Gt.update(a,!1,!0)},(0,r.useInsertionEffect)(()=>t.on(e,i),[t,e,i]),o}},6896:(t,e,i)=>{i.d(e,{s:()=>h});var n=i(2885),r=i(7494),s=i(4542),o=i(8802),a=i(5982);function l(t,e){[...e].reverse().forEach(i=>{let n=t.getVariant(i);n&&(0,o.U)(t,n),t.variantChildren&&t.variantChildren.forEach(t=>{l(t,e)})})}function u(){let t=!1,e=new Set,i={subscribe:t=>(e.add(t),()=>void e.delete(t)),start(i,n){(0,s.V)(t,"controls.start() should only be called after a component has mounted. Consider calling within a useEffect hook.");let r=[];return e.forEach(t=>{r.push((0,a._)(t,i,{transitionOverride:n}))}),Promise.all(r)},set:i=>((0,s.V)(t,"controls.set() should only be called after a component has mounted. Consider calling within a useEffect hook."),e.forEach(t=>{var e,n;e=t,Array.isArray(n=i)?l(e,n):"string"==typeof n?l(e,[n]):(0,o.U)(e,n)})),stop(){e.forEach(t=>{t.values.forEach(t=>t.stop())})},mount:()=>(t=!0,()=>{t=!1,i.stop()})};return i}let h=function(){let t=(0,n.M)(u);return(0,r.E)(t.mount,[]),t}},6926:(t,e,i)=>{i.d(e,{P:()=>r});var n=i(1788);function r(t){return t.props[n.n]}},7215:(t,e,i)=>{i.d(e,{X:()=>r,f:()=>n});let n=t=>1e3*t,r=t=>t/1e3},7322:(t,e,i)=>{i.d(e,{h:()=>d,q:()=>c});var n=i(280),r=i(9515);let s=new Set,o=!1,a=!1,l=!1;function u(){if(a){let t=Array.from(s).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=(0,n.W9)(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}a=!1,o=!1,s.forEach(t=>t.complete(l)),s.clear()}function h(){s.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(a=!0)})}function c(){l=!0,h(),u(),l=!1}class d{constructor(t,e,i,n,r,s=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=r,this.isAsync=s}scheduleResolve(){this.state="scheduled",this.isAsync?(s.add(this),o||(o=!0,r.Gt.read(h),r.Gt.resolveKeyframes(u))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;if(null===t[0]){let r=n?.get(),s=t[t.length-1];if(void 0!==r)t[0]=r;else if(i&&e){let n=i.readValue(e,s);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=s),n&&void 0===r&&n.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),s.delete(this)}cancel(){"scheduled"===this.state&&(s.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}},7351:(t,e,i)=>{i.d(e,{s:()=>r});var n=i(9364);function r(t){return(0,n.G)(t)&&"offsetHeight"in t}},7494:(t,e,i)=>{i.d(e,{E:()=>r});var n=i(2115);let r=i(8972).B?n.useLayoutEffect:n.useEffect},7602:(t,e,i)=>{i.d(e,{z:()=>c});var n=i(4803),r=i(532),s=i(9515);function o(t){return"number"==typeof t?t:parseFloat(t)}var a=i(2115),l=i(1508),u=i(8619),h=i(8829);function c(t,e={}){let{isStatic:i}=(0,a.useContext)(l.Q),d=()=>(0,n.S)(t)?t.get():t;if(i)return(0,h.G)(d);let p=(0,u.d)(d());return(0,a.useInsertionEffect)(()=>(function(t,e,i){let a,l=t.get(),u=null,h=l,c="string"==typeof l?l.replace(/[\d.-]/g,""):void 0,d=()=>{u&&(u.stop(),u=null)},p=()=>{d(),u=new r.s({keyframes:[o(t.get()),o(h)],velocity:t.getVelocity(),type:"spring",restDelta:.001,restSpeed:.01,...i,onUpdate:a})};if(t.attach((e,i)=>(h=e,a=t=>{var e,n;return i((e=t,(n=c)?e+n:e))},s.Gt.postRender(p),t.get()),d),(0,n.S)(e)){let i=e.on("change",e=>{var i,n;return t.set((i=e,(n=c)?i+n:i))}),n=t.on("destroy",i);return()=>{i(),n()}}return d})(p,t,e),[p,JSON.stringify(e)]),p}},7705:(t,e,i)=>{i.d(e,{K:()=>n});let n=(t,e,i=10)=>{let n="",r=Math.max(Math.round(e/i),2);for(let e=0;e<r;e++)n+=Math.round(1e4*t(e/(r-1)))/1e4+", ";return`linear(${n.substring(0,n.length-2)})`}},7712:(t,e,i)=>{i.d(e,{po:()=>s,tn:()=>a,yT:()=>o});var n=i(1765),r=i(4180);let s=t=>1-Math.sin(Math.acos(t)),o=(0,r.G)(s),a=(0,n.V)(s)},7887:(t,e,i)=>{i.d(e,{X4:()=>s,ai:()=>r,hs:()=>o});var n=i(1297);let r={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},s={...r,transform:t=>(0,n.q)(0,1,t)},o={...r,default:1}},8109:(t,e,i)=>{i.d(e,{$:()=>n});let n=new Set(["width","height","top","left","right","bottom",...i(18).U])},8437:(t,e,i)=>{i.d(e,{I:()=>o});var n=i(3387);let r=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"];var s=i(4744);function o(t,e){let i=!1,o=!0,a={delta:0,timestamp:0,isProcessing:!1},l=()=>i=!0,u=r.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,n=new Set,r=!1,o=!1,a=new WeakSet,l={delta:0,timestamp:0,isProcessing:!1},u=0;function h(e){a.has(e)&&(c.schedule(e),t()),u++,e(l)}let c={schedule:(t,e=!1,s=!1)=>{let o=s&&r?i:n;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{n.delete(t),a.delete(t)},process:t=>{if(l=t,r){o=!0;return}r=!0,[i,n]=[n,i],i.forEach(h),e&&s.Q.value&&s.Q.value.frameloop[e].push(u),u=0,i.clear(),r=!1,o&&(o=!1,c.process(t))}};return c}(l,e?i:void 0),t),{}),{setup:h,read:c,resolveKeyframes:d,preUpdate:p,update:f,preRender:m,render:g,postRender:v}=u,y=()=>{let r=n.W.useManualTiming?a.timestamp:performance.now();i=!1,n.W.useManualTiming||(a.delta=o?1e3/60:Math.max(Math.min(r-a.timestamp,40),1)),a.timestamp=r,a.isProcessing=!0,h.process(a),c.process(a),d.process(a),p.process(a),f.process(a),m.process(a),g.process(a),v.process(a),a.isProcessing=!1,i&&e&&(o=!1,t(y))};return{schedule:r.reduce((e,n)=>{let r=u[n];return e[n]=(e,n=!1,s=!1)=>(!i&&(i=!0,o=!0,a.isProcessing||t(y)),r.schedule(e,n,s)),e},{}),cancel:t=>{for(let e=0;e<r.length;e++)u[r[e]].cancel(t)},state:a,steps:u}}},8450:(t,e,i)=>{i.d(e,{I:()=>n});let n=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase()},8467:(t,e,i)=>{i.d(e,{i:()=>v});var n=i(2483);let r=(0,n.A)(.42,0,1,1),s=(0,n.A)(0,0,.58,1),o=(0,n.A)(.42,0,.58,1);var a=i(4542),l=i(9827),u=i(6009),h=i(3972),c=i(7712),d=i(8589);let p={linear:l.l,easeIn:r,easeInOut:o,easeOut:s,circIn:c.po,circInOut:c.tn,circOut:c.yT,backIn:h.dg,backInOut:h.ZZ,backOut:h.Sz,anticipate:u.b},f=t=>{if((0,d.D)(t)){(0,a.V)(4===t.length,"Cubic bezier arrays must contain four numerical values.","cubic-bezier-length");let[e,i,r,s]=t;return(0,n.A)(e,i,r,s)}return"string"==typeof t?((0,a.V)(void 0!==p[t],`Invalid easing type '${t}'`,"invalid-easing-type"),p[t]):t};var m=i(6775),g=i(1784);function v({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){var r;let s=Array.isArray(n)&&"number"!=typeof n[0]?n.map(f):f(n),a={done:!1,value:e[0]},l=(r=i&&i.length===e.length?i:(0,g.Z)(e),r.map(e=>e*t)),u=(0,m.G)(l,e,{ease:Array.isArray(s)?s:e.map(()=>s||o).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=u(e),a.done=e>=t,a)}}},8476:(t,e,i)=>{i.d(e,{V:()=>a});var n=i(7887),r=i(4158),s=i(1557),o=i(5920);let a={test:(0,o.$)("hsl","hue"),parse:(0,o.q)("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:o=1})=>"hsla("+Math.round(t)+", "+r.KN.transform((0,s.a)(e))+", "+r.KN.transform((0,s.a)(i))+", "+(0,s.a)(n.X4.transform(o))+")"}},8589:(t,e,i)=>{i.d(e,{D:()=>n});let n=t=>Array.isArray(t)&&"number"==typeof t[0]},8606:(t,e,i)=>{i.d(e,{j:()=>r,p:()=>o});let n=t=>e=>"string"==typeof e&&e.startsWith(t),r=n("--"),s=n("var(--"),o=t=>!!s(t)&&a.test(t.split("/*")[0].trim()),a=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu},8619:(t,e,i)=>{i.d(e,{d:()=>a});var n=i(98),r=i(2115),s=i(1508),o=i(2885);function a(t){let e=(0,o.M)(()=>(0,n.OQ)(t)),{isStatic:i}=(0,r.useContext)(s.Q);if(i){let[,i]=(0,r.useState)(t);(0,r.useEffect)(()=>e.on("change",i),[])}return e}},8777:(t,e,i)=>{i.d(e,{r:()=>n});function n(t,e){return t?.[e]??t?.default??t}},8802:(t,e,i)=>{i.d(e,{U:()=>o});var n=i(98),r=i(5910),s=i(419);function o(t,e){let{transitionEnd:i={},transition:o={},...a}=(0,s.K)(t,e)||{};for(let e in a={...a,...i}){var l;let i=(l=a[e],(0,r.p)(l)?l[l.length-1]||0:l);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,(0,n.OQ)(i))}}},8829:(t,e,i)=>{i.d(e,{G:()=>h});var n=i(6775),r=i(2885),s=i(9515),o=i(7494),a=i(8619);function l(t,e){let i=(0,a.d)(e()),n=()=>i.set(e());return n(),(0,o.E)(()=>{let e=()=>s.Gt.preRender(n,!1,!0),i=t.map(t=>t.on("change",e));return()=>{i.forEach(t=>t()),(0,s.WG)(n)}}),i}var u=i(98);function h(t,e,i,r){if("function"==typeof t){u.bt.current=[],t();let e=l(u.bt.current,t);return u.bt.current=void 0,e}let s="function"==typeof e?e:function(...t){let e=!Array.isArray(t[0]),i=e?0:-1,r=t[0+i],s=t[1+i],o=t[2+i],a=t[3+i],l=(0,n.G)(s,o,a);return e?l(r):l}(e,i,r);return Array.isArray(t)?c(t,s):c([t],([t])=>s(t))}function c(t,e){let i=(0,r.M)(()=>[]);return l(t,()=>{i.length=0;let n=t.length;for(let e=0;e<n;e++)i[e]=t[e].get();return e(i)})}},8972:(t,e,i)=>{i.d(e,{B:()=>n});let n="undefined"!=typeof window},9064:(t,e,i)=>{i.d(e,{B:()=>l});var n=i(1297),r=i(7887),s=i(1557),o=i(5920);let a={...r.ai,transform:t=>Math.round((0,n.q)(0,255,t))},l={test:(0,o.$)("rgb","red"),parse:(0,o.q)("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+a.transform(t)+", "+a.transform(e)+", "+a.transform(i)+", "+(0,s.a)(r.X4.transform(n))+")"}},9088:(t,e,i)=>{i.d(e,{u:()=>e1});var n,r,s,o,a,l,u,h,c,d,p,f,m,g=function(){return n||"undefined"!=typeof window&&(n=window.gsap)&&n.registerPlugin&&n},v=1,y=[],x=[],b=[],w=Date.now,T=function(t,e){return e},P=function(){var t=c.core,e=t.bridge||{},i=t._scrollers,n=t._proxies;i.push.apply(i,x),n.push.apply(n,b),x=i,b=n,T=function(t,i){return e[t](i)}},S=function(t,e){return~b.indexOf(t)&&b[b.indexOf(t)+1][e]},E=function(t){return!!~d.indexOf(t)},A=function(t,e,i,n,r){return t.addEventListener(e,i,{passive:!1!==n,capture:!!r})},M=function(t,e,i,n){return t.removeEventListener(e,i,!!n)},C="scrollLeft",k="scrollTop",V=function(){return p&&p.isPressed||x.cache++},O=function(t,e){var i=function i(n){if(n||0===n){v&&(s.history.scrollRestoration="manual");var r=p&&p.isPressed;t(n=i.v=Math.round(n)||(p&&p.iOS?1:0)),i.cacheID=x.cache,r&&T("ss",n)}else(e||x.cache!==i.cacheID||T("ref"))&&(i.cacheID=x.cache,i.v=t());return i.v+i.offset};return i.offset=0,t&&i},D={s:C,p:"left",p2:"Left",os:"right",os2:"Right",d:"width",d2:"Width",a:"x",sc:O(function(t){return arguments.length?s.scrollTo(t,R.sc()):s.pageXOffset||o[C]||a[C]||l[C]||0})},R={s:k,p:"top",p2:"Top",os:"bottom",os2:"Bottom",d:"height",d2:"Height",a:"y",op:D,sc:O(function(t){return arguments.length?s.scrollTo(D.sc(),t):s.pageYOffset||o[k]||a[k]||l[k]||0})},_=function(t,e){return(e&&e._ctx&&e._ctx.selector||n.utils.toArray)(t)[0]||("string"==typeof t&&!1!==n.config().nullTargetWarn?console.warn("Element not found:",t):null)},B=function(t,e){for(var i=e.length;i--;)if(e[i]===t||e[i].contains(t))return!0;return!1},L=function(t,e){var i=e.s,r=e.sc;E(t)&&(t=o.scrollingElement||a);var s=x.indexOf(t),l=r===R.sc?1:2;~s||(s=x.push(t)-1),x[s+l]||A(t,"scroll",V);var u=x[s+l],h=u||(x[s+l]=O(S(t,i),!0)||(E(t)?r:O(function(e){return arguments.length?t[i]=e:t[i]})));return h.target=t,u||(h.smooth="smooth"===n.getProperty(t,"scrollBehavior")),h},F=function(t,e,i){var n=t,r=t,s=w(),o=s,a=e||50,l=Math.max(500,3*a),u=function(t,e){var l=w();e||l-s>a?(r=n,n=t,o=s,s=l):i?n+=t:n=r+(t-r)/(l-o)*(s-o)};return{update:u,reset:function(){r=n=i?0:n,o=s=0},getVelocity:function(t){var e=o,a=r,h=w();return(t||0===t)&&t!==n&&u(t),s===o||h-o>l?0:(n+(i?a:-a))/((i?h:s)-e)*1e3}}},j=function(t,e){return e&&!t._gsapAllow&&t.preventDefault(),t.changedTouches?t.changedTouches[0]:t},I=function(t){var e=Math.max.apply(Math,t),i=Math.min.apply(Math,t);return Math.abs(e)>=Math.abs(i)?e:i},N=function(){(c=n.core.globals().ScrollTrigger)&&c.core&&P()},W=function(t){return n=t||g(),!r&&n&&"undefined"!=typeof document&&document.body&&(s=window,a=(o=document).documentElement,l=o.body,d=[s,o,a,l],n.utils.clamp,m=n.core.context||function(){},h="onpointerenter"in l?"pointer":"mouse",u=Y.isTouch=s.matchMedia&&s.matchMedia("(hover: none), (pointer: coarse)").matches?1:2*("ontouchstart"in s||navigator.maxTouchPoints>0||navigator.msMaxTouchPoints>0),f=Y.eventTypes=("ontouchstart"in a?"touchstart,touchmove,touchcancel,touchend":!("onpointerdown"in a)?"mousedown,mousemove,mouseup,mouseup":"pointerdown,pointermove,pointercancel,pointerup").split(","),setTimeout(function(){return v=0},500),N(),r=1),r};D.op=R,x.cache=0;var Y=function(){var t;function e(t){this.init(t)}return e.prototype.init=function(t){r||W(n)||console.warn("Please gsap.registerPlugin(Observer)"),c||N();var e=t.tolerance,i=t.dragMinimum,d=t.type,g=t.target,v=t.lineHeight,x=t.debounce,b=t.preventDefault,T=t.onStop,P=t.onStopDelay,S=t.ignore,C=t.wheelSpeed,k=t.event,O=t.onDragStart,Y=t.onDragEnd,z=t.onDrag,X=t.onPress,U=t.onRelease,G=t.onRight,q=t.onLeft,H=t.onUp,$=t.onDown,K=t.onChangeX,Q=t.onChangeY,Z=t.onChange,J=t.onToggleX,tt=t.onToggleY,te=t.onHover,ti=t.onHoverEnd,tn=t.onMove,tr=t.ignoreCheck,ts=t.isNormalizer,to=t.onGestureStart,ta=t.onGestureEnd,tl=t.onWheel,tu=t.onEnable,th=t.onDisable,tc=t.onClick,td=t.scrollSpeed,tp=t.capture,tf=t.allowClicks,tm=t.lockAxis,tg=t.onLockAxis;this.target=g=_(g)||a,this.vars=t,S&&(S=n.utils.toArray(S)),e=e||1e-9,i=i||0,C=C||1,td=td||1,d=d||"wheel,touch,pointer",x=!1!==x,v||(v=parseFloat(s.getComputedStyle(l).lineHeight)||22);var tv,ty,tx,tb,tw,tT,tP,tS=this,tE=0,tA=0,tM=t.passive||!b&&!1!==t.passive,tC=L(g,D),tk=L(g,R),tV=tC(),tO=tk(),tD=~d.indexOf("touch")&&!~d.indexOf("pointer")&&"pointerdown"===f[0],tR=E(g),t_=g.ownerDocument||o,tB=[0,0,0],tL=[0,0,0],tF=0,tj=function(){return tF=w()},tI=function(t,e){return(tS.event=t)&&S&&B(t.target,S)||e&&tD&&"touch"!==t.pointerType||tr&&tr(t,e)},tN=function(){var t=tS.deltaX=I(tB),i=tS.deltaY=I(tL),n=Math.abs(t)>=e,r=Math.abs(i)>=e;Z&&(n||r)&&Z(tS,t,i,tB,tL),n&&(G&&tS.deltaX>0&&G(tS),q&&tS.deltaX<0&&q(tS),K&&K(tS),J&&tS.deltaX<0!=tE<0&&J(tS),tE=tS.deltaX,tB[0]=tB[1]=tB[2]=0),r&&($&&tS.deltaY>0&&$(tS),H&&tS.deltaY<0&&H(tS),Q&&Q(tS),tt&&tS.deltaY<0!=tA<0&&tt(tS),tA=tS.deltaY,tL[0]=tL[1]=tL[2]=0),(tb||tx)&&(tn&&tn(tS),tx&&(O&&1===tx&&O(tS),z&&z(tS),tx=0),tb=!1),tT&&(tT=!1,1)&&tg&&tg(tS),tw&&(tl(tS),tw=!1),tv=0},tW=function(t,e,i){tB[i]+=t,tL[i]+=e,tS._vx.update(t),tS._vy.update(e),x?tv||(tv=requestAnimationFrame(tN)):tN()},tY=function(t,e){tm&&!tP&&(tS.axis=tP=Math.abs(t)>Math.abs(e)?"x":"y",tT=!0),"y"!==tP&&(tB[2]+=t,tS._vx.update(t,!0)),"x"!==tP&&(tL[2]+=e,tS._vy.update(e,!0)),x?tv||(tv=requestAnimationFrame(tN)):tN()},tz=function(t){if(!tI(t,1)){var e=(t=j(t,b)).clientX,n=t.clientY,r=e-tS.x,s=n-tS.y,o=tS.isDragging;tS.x=e,tS.y=n,(o||(r||s)&&(Math.abs(tS.startX-e)>=i||Math.abs(tS.startY-n)>=i))&&(tx=o?2:1,o||(tS.isDragging=!0),tY(r,s))}},tX=tS.onPress=function(t){tI(t,1)||t&&t.button||(tS.axis=tP=null,ty.pause(),tS.isPressed=!0,t=j(t),tE=tA=0,tS.startX=tS.x=t.clientX,tS.startY=tS.y=t.clientY,tS._vx.reset(),tS._vy.reset(),A(ts?g:t_,f[1],tz,tM,!0),tS.deltaX=tS.deltaY=0,X&&X(tS))},tU=tS.onRelease=function(t){if(!tI(t,1)){M(ts?g:t_,f[1],tz,!0);var e=!isNaN(tS.y-tS.startY),i=tS.isDragging,r=i&&(Math.abs(tS.x-tS.startX)>3||Math.abs(tS.y-tS.startY)>3),o=j(t);!r&&e&&(tS._vx.reset(),tS._vy.reset(),b&&tf&&n.delayedCall(.08,function(){if(w()-tF>300&&!t.defaultPrevented){if(t.target.click)t.target.click();else if(t_.createEvent){var e=t_.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,s,1,o.screenX,o.screenY,o.clientX,o.clientY,!1,!1,!1,!1,0,null),t.target.dispatchEvent(e)}}})),tS.isDragging=tS.isGesturing=tS.isPressed=!1,T&&i&&!ts&&ty.restart(!0),tx&&tN(),Y&&i&&Y(tS),U&&U(tS,r)}},tG=function(t){return t.touches&&t.touches.length>1&&(tS.isGesturing=!0)&&to(t,tS.isDragging)},tq=function(){return tS.isGesturing=!1,ta(tS)},tH=function(t){if(!tI(t)){var e=tC(),i=tk();tW((e-tV)*td,(i-tO)*td,1),tV=e,tO=i,T&&ty.restart(!0)}},t$=function(t){if(!tI(t)){t=j(t,b),tl&&(tw=!0);var e=(1===t.deltaMode?v:2===t.deltaMode?s.innerHeight:1)*C;tW(t.deltaX*e,t.deltaY*e,0),T&&!ts&&ty.restart(!0)}},tK=function(t){if(!tI(t)){var e=t.clientX,i=t.clientY,n=e-tS.x,r=i-tS.y;tS.x=e,tS.y=i,tb=!0,T&&ty.restart(!0),(n||r)&&tY(n,r)}},tQ=function(t){tS.event=t,te(tS)},tZ=function(t){tS.event=t,ti(tS)},tJ=function(t){return tI(t)||j(t,b)&&tc(tS)};ty=tS._dc=n.delayedCall(P||.25,function(){tS._vx.reset(),tS._vy.reset(),ty.pause(),T&&T(tS)}).pause(),tS.deltaX=tS.deltaY=0,tS._vx=F(0,50,!0),tS._vy=F(0,50,!0),tS.scrollX=tC,tS.scrollY=tk,tS.isDragging=tS.isGesturing=tS.isPressed=!1,m(this),tS.enable=function(t){return!tS.isEnabled&&(A(tR?t_:g,"scroll",V),d.indexOf("scroll")>=0&&A(tR?t_:g,"scroll",tH,tM,tp),d.indexOf("wheel")>=0&&A(g,"wheel",t$,tM,tp),(d.indexOf("touch")>=0&&u||d.indexOf("pointer")>=0)&&(A(g,f[0],tX,tM,tp),A(t_,f[2],tU),A(t_,f[3],tU),tf&&A(g,"click",tj,!0,!0),tc&&A(g,"click",tJ),to&&A(t_,"gesturestart",tG),ta&&A(t_,"gestureend",tq),te&&A(g,h+"enter",tQ),ti&&A(g,h+"leave",tZ),tn&&A(g,h+"move",tK)),tS.isEnabled=!0,tS.isDragging=tS.isGesturing=tS.isPressed=tb=tx=!1,tS._vx.reset(),tS._vy.reset(),tV=tC(),tO=tk(),t&&t.type&&tX(t),tu&&tu(tS)),tS},tS.disable=function(){tS.isEnabled&&(y.filter(function(t){return t!==tS&&E(t.target)}).length||M(tR?t_:g,"scroll",V),tS.isPressed&&(tS._vx.reset(),tS._vy.reset(),M(ts?g:t_,f[1],tz,!0)),M(tR?t_:g,"scroll",tH,tp),M(g,"wheel",t$,tp),M(g,f[0],tX,tp),M(t_,f[2],tU),M(t_,f[3],tU),M(g,"click",tj,!0),M(g,"click",tJ),M(t_,"gesturestart",tG),M(t_,"gestureend",tq),M(g,h+"enter",tQ),M(g,h+"leave",tZ),M(g,h+"move",tK),tS.isEnabled=tS.isPressed=tS.isDragging=!1,th&&th(tS))},tS.kill=tS.revert=function(){tS.disable();var t=y.indexOf(tS);t>=0&&y.splice(t,1),p===tS&&(p=0)},y.push(tS),ts&&E(g)&&(p=tS),tS.enable(k)},t=[{key:"velocityX",get:function(){return this._vx.getVelocity()}},{key:"velocityY",get:function(){return this._vy.getVelocity()}}],function(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}(e.prototype,t),e}();Y.version="3.13.0",Y.create=function(t){return new Y(t)},Y.register=W,Y.getAll=function(){return y.slice()},Y.getById=function(t){return y.filter(function(e){return e.vars.id===t})[0]},g()&&n.registerPlugin(Y);var z,X,U,G,q,H,$,K,Q,Z,J,tt,te,ti,tn,tr,ts,to,ta,tl,tu,th,tc,td,tp,tf,tm,tg,tv,ty,tx,tb,tw,tT,tP,tS,tE,tA,tM=1,tC=Date.now,tk=tC(),tV=0,tO=0,tD=function(t,e,i){var n=tG(t)&&("clamp("===t.substr(0,6)||t.indexOf("max")>-1);return i["_"+e+"Clamp"]=n,n?t.substr(6,t.length-7):t},tR=function(t,e){return e&&(!tG(t)||"clamp("!==t.substr(0,6))?"clamp("+t+")":t},t_=function(){return ti=1},tB=function(){return ti=0},tL=function(t){return t},tF=function(t){return Math.round(1e5*t)/1e5||0},tj=function(){return"undefined"!=typeof window},tI=function(){return z||tj()&&(z=window.gsap)&&z.registerPlugin&&z},tN=function(t){return!!~$.indexOf(t)},tW=function(t){return("Height"===t?tx:U["inner"+t])||q["client"+t]||H["client"+t]},tY=function(t){return S(t,"getBoundingClientRect")||(tN(t)?function(){return eH.width=U.innerWidth,eH.height=tx,eH}:function(){return er(t)})},tz=function(t,e,i){var n=i.d,r=i.d2,s=i.a;return(s=S(t,"getBoundingClientRect"))?function(){return s()[n]}:function(){return(e?tW(r):t["client"+r])||0}},tX=function(t,e){var i=e.s,n=e.d2,r=e.d,s=e.a;return Math.max(0,(s=S(t,i="scroll"+n))?s()-tY(t)()[r]:tN(t)?(q[i]||H[i])-tW(n):t[i]-t["offset"+n])},tU=function(t,e){for(var i=0;i<ta.length;i+=3)(!e||~e.indexOf(ta[i+1]))&&t(ta[i],ta[i+1],ta[i+2])},tG=function(t){return"string"==typeof t},tq=function(t){return"function"==typeof t},tH=function(t){return"number"==typeof t},t$=function(t){return"object"==typeof t},tK=function(t,e,i){return t&&t.progress(+!e)&&i&&t.pause()},tQ=function(t,e){if(t.enabled){var i=t._ctx?t._ctx.add(function(){return e(t)}):e(t);i&&i.totalTime&&(t.callbackAnimation=i)}},tZ=Math.abs,tJ="left",t0="right",t1="bottom",t2="width",t5="height",t3="Right",t8="Left",t9="Bottom",t4="padding",t7="margin",t6="Width",et="Height",ee=function(t){return U.getComputedStyle(t)},ei=function(t){var e=ee(t).position;t.style.position="absolute"===e||"fixed"===e?e:"relative"},en=function(t,e){for(var i in e)i in t||(t[i]=e[i]);return t},er=function(t,e){var i=e&&"matrix(1, 0, 0, 1, 0, 0)"!==ee(t)[tn]&&z.to(t,{x:0,y:0,xPercent:0,yPercent:0,rotation:0,rotationX:0,rotationY:0,scale:1,skewX:0,skewY:0}).progress(1),n=t.getBoundingClientRect();return i&&i.progress(0).kill(),n},es=function(t,e){var i=e.d2;return t["offset"+i]||t["client"+i]||0},eo=function(t){var e,i=[],n=t.labels,r=t.duration();for(e in n)i.push(n[e]/r);return i},ea=function(t){var e=z.utils.snap(t),i=Array.isArray(t)&&t.slice(0).sort(function(t,e){return t-e});return i?function(t,n,r){var s;if(void 0===r&&(r=.001),!n)return e(t);if(n>0){for(t-=r,s=0;s<i.length;s++)if(i[s]>=t)return i[s];return i[s-1]}for(s=i.length,t+=r;s--;)if(i[s]<=t)return i[s];return i[0]}:function(i,n,r){void 0===r&&(r=.001);var s=e(i);return!n||Math.abs(s-i)<r||s-i<0==n<0?s:e(n<0?i-t:i+t)}},el=function(t,e,i,n){return i.split(",").forEach(function(i){return t(e,i,n)})},eu=function(t,e,i,n,r){return t.addEventListener(e,i,{passive:!n,capture:!!r})},eh=function(t,e,i,n){return t.removeEventListener(e,i,!!n)},ec=function(t,e,i){(i=i&&i.wheelHandler)&&(t(e,"wheel",i),t(e,"touchmove",i))},ed={startColor:"green",endColor:"red",indent:0,fontSize:"16px",fontWeight:"normal"},ep={toggleActions:"play",anticipatePin:0},ef={top:0,left:0,center:.5,bottom:1,right:1},em=function(t,e){if(tG(t)){var i=t.indexOf("="),n=~i?(t.charAt(i-1)+1)*parseFloat(t.substr(i+1)):0;~i&&(t.indexOf("%")>i&&(n*=e/100),t=t.substr(0,i-1)),t=n+(t in ef?ef[t]*e:~t.indexOf("%")?parseFloat(t)*e/100:parseFloat(t)||0)}return t},eg=function(t,e,i,n,r,s,o,a){var l=r.startColor,u=r.endColor,h=r.fontSize,c=r.indent,d=r.fontWeight,p=G.createElement("div"),f=tN(i)||"fixed"===S(i,"pinType"),m=-1!==t.indexOf("scroller"),g=f?H:i,v=-1!==t.indexOf("start"),y=v?l:u,x="border-color:"+y+";font-size:"+h+";color:"+y+";font-weight:"+d+";pointer-events:none;white-space:nowrap;font-family:sans-serif,Arial;z-index:1000;padding:4px 8px;border-width:0;border-style:solid;";return x+="position:"+((m||a)&&f?"fixed;":"absolute;"),(m||a||!f)&&(x+=(n===R?t0:t1)+":"+(s+parseFloat(c))+"px;"),o&&(x+="box-sizing:border-box;text-align:left;width:"+o.offsetWidth+"px;"),p._isStart=v,p.setAttribute("class","gsap-marker-"+t+(e?" marker-"+e:"")),p.style.cssText=x,p.innerText=e||0===e?t+"-"+e:t,g.children[0]?g.insertBefore(p,g.children[0]):g.appendChild(p),p._offset=p["offset"+n.op.d2],ev(p,0,n,v),p},ev=function(t,e,i,n){var r={display:"block"},s=i[n?"os2":"p2"],o=i[n?"p2":"os2"];t._isFlipped=n,r[i.a+"Percent"]=n?-100:0,r[i.a]=n?"1px":0,r["border"+s+t6]=1,r["border"+o+t6]=0,r[i.p]=e+"px",z.set(t,r)},ey=[],ex={},eb=function(){return tC()-tV>34&&(tP||(tP=requestAnimationFrame(eI)))},ew=function(){tc&&tc.isPressed&&!(tc.startX>H.clientWidth)||(x.cache++,tc?tP||(tP=requestAnimationFrame(eI)):eI(),tV||eM("scrollStart"),tV=tC())},eT=function(){tf=U.innerWidth,tp=U.innerHeight},eP=function(t){x.cache++,(!0===t||!te&&!th&&!G.fullscreenElement&&!G.webkitFullscreenElement&&(!td||tf!==U.innerWidth||Math.abs(U.innerHeight-tp)>.25*U.innerHeight))&&K.restart(!0)},eS={},eE=[],eA=function t(){return eh(e1,"scrollEnd",t)||eL(!0)},eM=function(t){return eS[t]&&eS[t].map(function(t){return t()})||eE},eC=[],ek=function(t){for(var e=0;e<eC.length;e+=5)(!t||eC[e+4]&&eC[e+4].query===t)&&(eC[e].style.cssText=eC[e+1],eC[e].getBBox&&eC[e].setAttribute("transform",eC[e+2]||""),eC[e+3].uncache=1)},eV=function(t,e){var i;for(tr=0;tr<ey.length;tr++)(i=ey[tr])&&(!e||i._ctx===e)&&(t?i.kill(1):i.revert(!0,!0));tb=!0,e&&ek(e),e||eM("revert")},eO=function(t,e){x.cache++,(e||!tS)&&x.forEach(function(t){return tq(t)&&t.cacheID++&&(t.rec=0)}),tG(t)&&(U.history.scrollRestoration=tv=t)},eD=0,eR=function(){if(tE!==eD){var t=tE=eD;requestAnimationFrame(function(){return t===eD&&eL(!0)})}},e_=function(){H.appendChild(ty),tx=!tc&&ty.offsetHeight||U.innerHeight,H.removeChild(ty)},eB=function(t){return Q(".gsap-marker-start, .gsap-marker-end, .gsap-marker-scroller-start, .gsap-marker-scroller-end").forEach(function(e){return e.style.display=t?"none":"block"})},eL=function(t,e){if(q=G.documentElement,H=G.body,$=[U,G,q,H],tV&&!t&&!tb)return void eu(e1,"scrollEnd",eA);e_(),tS=e1.isRefreshing=!0,x.forEach(function(t){return tq(t)&&++t.cacheID&&(t.rec=t())});var i=eM("refreshInit");tl&&e1.sort(),e||eV(),x.forEach(function(t){tq(t)&&(t.smooth&&(t.target.style.scrollBehavior="auto"),t(0))}),ey.slice(0).forEach(function(t){return t.refresh()}),tb=!1,ey.forEach(function(t){if(t._subPinOffset&&t.pin){var e=t.vars.horizontal?"offsetWidth":"offsetHeight",i=t.pin[e];t.revert(!0,1),t.adjustPinSpacing(t.pin[e]-i),t.refresh()}}),tw=1,eB(!0),ey.forEach(function(t){var e=tX(t.scroller,t._dir),i="max"===t.vars.end||t._endClamp&&t.end>e,n=t._startClamp&&t.start>=e;(i||n)&&t.setPositions(n?e-1:t.start,i?Math.max(n?e:t.start+1,e):t.end,!0)}),eB(!1),tw=0,i.forEach(function(t){return t&&t.render&&t.render(-1)}),x.forEach(function(t){tq(t)&&(t.smooth&&requestAnimationFrame(function(){return t.target.style.scrollBehavior="smooth"}),t.rec&&t(t.rec))}),eO(tv,1),K.pause(),eD++,tS=2,eI(2),ey.forEach(function(t){return tq(t.vars.onRefresh)&&t.vars.onRefresh(t)}),tS=e1.isRefreshing=!1,eM("refresh")},eF=0,ej=1,eI=function(t){if(2===t||!tS&&!tb){e1.isUpdating=!0,tA&&tA.update(0);var e=ey.length,i=tC(),n=i-tk>=50,r=e&&ey[0].scroll();if(ej=eF>r?-1:1,tS||(eF=r),n&&(tV&&!ti&&i-tV>200&&(tV=0,eM("scrollEnd")),J=tk,tk=i),ej<0){for(tr=e;tr-- >0;)ey[tr]&&ey[tr].update(0,n);ej=1}else for(tr=0;tr<e;tr++)ey[tr]&&ey[tr].update(0,n);e1.isUpdating=!1}tP=0},eN=[tJ,"top",t1,t0,t7+t9,t7+t3,t7+"Top",t7+t8,"display","flexShrink","float","zIndex","gridColumnStart","gridColumnEnd","gridRowStart","gridRowEnd","gridArea","justifySelf","alignSelf","placeSelf","order"],eW=eN.concat([t2,t5,"boxSizing","max"+t6,"max"+et,"position",t7,t4,t4+"Top",t4+t3,t4+t9,t4+t8]),eY=function(t,e,i){eU(i);var n=t._gsap;if(n.spacerIsNative)eU(n.spacerState);else if(t._gsap.swappedIn){var r=e.parentNode;r&&(r.insertBefore(t,e),r.removeChild(e))}t._gsap.swappedIn=!1},ez=function(t,e,i,n){if(!t._gsap.swappedIn){for(var r,s=eN.length,o=e.style,a=t.style;s--;)o[r=eN[s]]=i[r];o.position="absolute"===i.position?"absolute":"relative","inline"===i.display&&(o.display="inline-block"),a[t1]=a[t0]="auto",o.flexBasis=i.flexBasis||"auto",o.overflow="visible",o.boxSizing="border-box",o[t2]=es(t,D)+"px",o[t5]=es(t,R)+"px",o[t4]=a[t7]=a.top=a[tJ]="0",eU(n),a[t2]=a["max"+t6]=i[t2],a[t5]=a["max"+et]=i[t5],a[t4]=i[t4],t.parentNode!==e&&(t.parentNode.insertBefore(e,t),e.appendChild(t)),t._gsap.swappedIn=!0}},eX=/([A-Z])/g,eU=function(t){if(t){var e,i,n=t.t.style,r=t.length,s=0;for((t.t._gsap||z.core.getCache(t.t)).uncache=1;s<r;s+=2)i=t[s+1],e=t[s],i?n[e]=i:n[e]&&n.removeProperty(e.replace(eX,"-$1").toLowerCase())}},eG=function(t){for(var e=eW.length,i=t.style,n=[],r=0;r<e;r++)n.push(eW[r],i[eW[r]]);return n.t=t,n},eq=function(t,e,i){for(var n,r=[],s=t.length,o=8*!!i;o<s;o+=2)n=t[o],r.push(n,n in e?e[n]:t[o+1]);return r.t=t.t,r},eH={left:0,top:0},e$=function(t,e,i,n,r,s,o,a,l,u,h,c,d,p){tq(t)&&(t=t(a)),tG(t)&&"max"===t.substr(0,3)&&(t=c+("="===t.charAt(4)?em("0"+t.substr(3),i):0));var f,m,g,v=d?d.time():0;if(d&&d.seek(0),isNaN(t)||(t*=1),tH(t))d&&(t=z.utils.mapRange(d.scrollTrigger.start,d.scrollTrigger.end,0,c,t)),o&&ev(o,i,n,!0);else{tq(e)&&(e=e(a));var y,x,b,w,T=(t||"0").split(" ");(y=er(g=_(e,a)||H)||{}).left||y.top||"none"!==ee(g).display||(w=g.style.display,g.style.display="block",y=er(g),w?g.style.display=w:g.style.removeProperty("display")),x=em(T[0],y[n.d]),b=em(T[1]||"0",i),t=y[n.p]-l[n.p]-u+x+r-b,o&&ev(o,b,n,i-b<20||o._isStart&&b>20),i-=i-b}if(p&&(a[p]=t||-.001,t<0&&(t=0)),s){var P=t+i,S=s._isStart;f="scroll"+n.d2,ev(s,P,n,S&&P>20||!S&&(h?Math.max(H[f],q[f]):s.parentNode[f])<=P+1),h&&(l=er(o),h&&(s.style[n.op.p]=l[n.op.p]-n.op.m-s._offset+"px"))}return d&&g&&(f=er(g),d.seek(c),m=er(g),d._caScrollDist=f[n.p]-m[n.p],t=t/d._caScrollDist*c),d&&d.seek(v),d?t:Math.round(t)},eK=/(webkit|moz|length|cssText|inset)/i,eQ=function(t,e,i,n){if(t.parentNode!==e){var r,s,o=t.style;if(e===H){for(r in t._stOrig=o.cssText,s=ee(t))+r||eK.test(r)||!s[r]||"string"!=typeof o[r]||"0"===r||(o[r]=s[r]);o.top=i,o.left=n}else o.cssText=t._stOrig;z.core.getCache(t).uncache=1,e.appendChild(t)}},eZ=function(t,e,i){var n=e,r=n;return function(e){var s=Math.round(t());return s!==n&&s!==r&&Math.abs(s-n)>3&&Math.abs(s-r)>3&&(e=s,i&&i()),r=n,n=Math.round(e)}},eJ=function(t,e,i){var n={};n[e.p]="+="+i,z.set(t,n)},e0=function(t,e){var i=L(t,e),n="_scroll"+e.p2,r=function e(r,s,o,a,l){var u=e.tween,h=s.onComplete,c={};o=o||i();var d=eZ(i,o,function(){u.kill(),e.tween=0});return l=a&&l||0,a=a||r-o,u&&u.kill(),s[n]=r,s.inherit=!1,s.modifiers=c,c[n]=function(){return d(o+a*u.ratio+l*u.ratio*u.ratio)},s.onUpdate=function(){x.cache++,e.tween&&eI()},s.onComplete=function(){e.tween=0,h&&h.call(u)},u=e.tween=z.to(t,s)};return t[n]=i,i.wheelHandler=function(){return r.tween&&r.tween.kill()&&(r.tween=0)},eu(t,"wheel",i.wheelHandler),e1.isTouch&&eu(t,"touchmove",i.wheelHandler),r},e1=function(){function t(e,i){X||t.register(z)||console.warn("Please gsap.registerPlugin(ScrollTrigger)"),tg(this),this.init(e,i)}return t.prototype.init=function(e,i){if(this.progress=this.start=0,this.vars&&this.kill(!0,!0),!tO){this.update=this.refresh=this.kill=tL;return}var n,r,s,o,a,l,u,h,c,d,p,f,m,g,v,y,w,T,P,E,A,M,C,k,V,O,B,F,j,I,N,W,Y,X,$,K,tt,tn,ts,to,ta,th=e=en(tG(e)||tH(e)||e.nodeType?{trigger:e}:e,ep),tc=th.onUpdate,td=th.toggleClass,tp=th.id,tf=th.onToggle,tm=th.onRefresh,tg=th.scrub,tv=th.trigger,ty=th.pin,tx=th.pinSpacing,tb=th.invalidateOnRefresh,tP=th.anticipatePin,tE=th.onScrubComplete,tk=th.onSnapComplete,t_=th.once,tB=th.snap,tj=th.pinReparent,tI=th.pinSpacer,tW=th.containerAnimation,tU=th.fastScrollEnd,tJ=th.preventOverlaps,t0=e.horizontal||e.containerAnimation&&!1!==e.horizontal?D:R,t1=!tg&&0!==tg,el=_(e.scroller||U),ec=z.core.getCache(el),ef=tN(el),ev=("pinType"in e?e.pinType:S(el,"pinType")||ef&&"fixed")==="fixed",eb=[e.onEnter,e.onLeave,e.onEnterBack,e.onLeaveBack],eT=t1&&e.toggleActions.split(" "),eS="markers"in e?e.markers:ep.markers,eE=ef?0:parseFloat(ee(el)["border"+t0.p2+t6])||0,eM=this,eC=e.onRefreshInit&&function(){return e.onRefreshInit(eM)},ek=tz(el,ef,t0),eV=!ef||~b.indexOf(el)?tY(el):function(){return eH},eO=0,eD=0,e_=0,eB=L(el,t0);if(eM._startClamp=eM._endClamp=!1,eM._dir=t0,tP*=45,eM.scroller=el,eM.scroll=tW?tW.time.bind(tW):eB,l=eB(),eM.vars=e,i=i||e.animation,"refreshPriority"in e&&(tl=1,-9999===e.refreshPriority&&(tA=eM)),ec.tweenScroll=ec.tweenScroll||{top:e0(el,R),left:e0(el,D)},eM.tweenTo=s=ec.tweenScroll[t0.p],eM.scrubDuration=function(t){($=tH(t)&&t)?X?X.duration(t):X=z.to(i,{ease:"expo",totalProgress:"+=0",inherit:!1,duration:$,paused:!0,onComplete:function(){return tE&&tE(eM)}}):(X&&X.progress(1).kill(),X=0)},i&&(i.vars.lazy=!1,i._initted&&!eM.isReverted||!1!==i.vars.immediateRender&&!1!==e.immediateRender&&i.duration()&&i.render(0,!0,!0),eM.animation=i.pause(),i.scrollTrigger=eM,eM.scrubDuration(tg),W=0,tp||(tp=i.vars.id)),tB&&((!t$(tB)||tB.push)&&(tB={snapTo:tB}),"scrollBehavior"in H.style&&z.set(ef?[H,q]:el,{scrollBehavior:"auto"}),x.forEach(function(t){return tq(t)&&t.target===(ef?G.scrollingElement||q:el)&&(t.smooth=!1)}),a=tq(tB.snapTo)?tB.snapTo:"labels"===tB.snapTo?(n=i,function(t){return z.utils.snap(eo(n),t)}):"labelsDirectional"===tB.snapTo?(r=i,function(t,e){return ea(eo(r))(t,e.direction)}):!1!==tB.directional?function(t,e){return ea(tB.snapTo)(t,tC()-eD<500?0:e.direction)}:z.utils.snap(tB.snapTo),K=t$(K=tB.duration||{min:.1,max:2})?Z(K.min,K.max):Z(K,K),tt=z.delayedCall(tB.delay||$/2||.1,function(){var t=eB(),e=tC()-eD<500,n=s.tween;if((e||10>Math.abs(eM.getVelocity()))&&!n&&!ti&&eO!==t){var r,o,l=(t-h)/y,u=i&&!t1?i.totalProgress():l,d=e?0:(u-Y)/(tC()-J)*1e3||0,p=z.utils.clamp(-l,1-l,tZ(d/2)*d/.185),f=l+(!1===tB.inertia?0:p),m=tB,g=m.onStart,v=m.onInterrupt,x=m.onComplete;if(tH(r=a(f,eM))||(r=f),o=Math.max(0,Math.round(h+r*y)),t<=c&&t>=h&&o!==t){if(n&&!n._initted&&n.data<=tZ(o-t))return;!1===tB.inertia&&(p=r-l),s(o,{duration:K(tZ(.185*Math.max(tZ(f-u),tZ(r-u))/d/.05||0)),ease:tB.ease||"power3",data:tZ(o-t),onInterrupt:function(){return tt.restart(!0)&&v&&v(eM)},onComplete:function(){eM.update(),eO=eB(),i&&!t1&&(X?X.resetTo("totalProgress",r,i._tTime/i._tDur):i.progress(r)),W=Y=i&&!t1?i.totalProgress():eM.progress,tk&&tk(eM),x&&x(eM)}},t,p*y,o-t-p*y),g&&g(eM,s.tween)}}else eM.isActive&&eO!==t&&tt.restart(!0)}).pause()),tp&&(ex[tp]=eM),(ta=(tv=eM.trigger=_(tv||!0!==ty&&ty))&&tv._gsap&&tv._gsap.stRevert)&&(ta=ta(eM)),ty=!0===ty?tv:_(ty),tG(td)&&(td={targets:tv,className:td}),ty&&(!1===tx||tx===t7||(tx=(!!tx||!ty.parentNode||!ty.parentNode.style||"flex"!==ee(ty.parentNode).display)&&t4),eM.pin=ty,(o=z.core.getCache(ty)).spacer?w=o.pinState:(tI&&((tI=_(tI))&&!tI.nodeType&&(tI=tI.current||tI.nativeElement),o.spacerIsNative=!!tI,tI&&(o.spacerState=eG(tI))),o.spacer=E=tI||G.createElement("div"),E.classList.add("pin-spacer"),tp&&E.classList.add("pin-spacer-"+tp),o.pinState=w=eG(ty)),!1!==e.force3D&&z.set(ty,{force3D:!0}),eM.spacer=E=o.spacer,O=(N=ee(ty))[tx+t0.os2],M=z.getProperty(ty),C=z.quickSetter(ty,t0.a,"px"),ez(ty,E,N),P=eG(ty)),eS){g=t$(eS)?en(eS,ed):ed,f=eg("scroller-start",tp,el,t0,g,0),m=eg("scroller-end",tp,el,t0,g,0,f),A=f["offset"+t0.op.d2];var eL=_(S(el,"content")||el);d=this.markerStart=eg("start",tp,eL,t0,g,A,0,tW),p=this.markerEnd=eg("end",tp,eL,t0,g,A,0,tW),tW&&(to=z.quickSetter([d,p],t0.a,"px")),ev||b.length&&!0===S(el,"fixedMarkers")||(ei(ef?H:el),z.set([f,m],{force3D:!0}),F=z.quickSetter(f,t0.a,"px"),I=z.quickSetter(m,t0.a,"px"))}if(tW){var eF=tW.vars.onUpdate,eI=tW.vars.onUpdateParams;tW.eventCallback("onUpdate",function(){eM.update(0,0,1),eF&&eF.apply(tW,eI||[])})}if(eM.previous=function(){return ey[ey.indexOf(eM)-1]},eM.next=function(){return ey[ey.indexOf(eM)+1]},eM.revert=function(t,e){if(!e)return eM.kill(!0);var n=!1!==t||!eM.enabled,r=te;n!==eM.isReverted&&(n&&(tn=Math.max(eB(),eM.scroll.rec||0),e_=eM.progress,ts=i&&i.progress()),d&&[d,p,f,m].forEach(function(t){return t.style.display=n?"none":"block"}),n&&(te=eM,eM.update(n)),!ty||tj&&eM.isActive||(n?eY(ty,E,w):ez(ty,E,ee(ty),B)),n||eM.update(n),te=r,eM.isReverted=n)},eM.refresh=function(n,r,o,a){if(!te&&eM.enabled||r){if(ty&&n&&tV)return void eu(t,"scrollEnd",eA);!tS&&eC&&eC(eM),te=eM,s.tween&&!o&&(s.tween.kill(),s.tween=0),X&&X.pause(),tb&&i&&(i.revert({kill:!1}).invalidate(),i.getChildren&&i.getChildren(!0,!0,!1).forEach(function(t){return t.vars.immediateRender&&t.render(0,!0,!0)})),eM.isReverted||eM.revert(!0,!0),eM._subPinOffset=!1;var g,x,b,S,A,C,O,F,I,N,W,Y,U,$=ek(),K=eV(),Q=tW?tW.duration():tX(el,t0),Z=y<=.01||!y,J=0,ti=a||0,tr=t$(o)?o.end:e.end,to=e.endTrigger||tv,ta=t$(o)?o.start:e.start||(0!==e.start&&tv?ty?"0 0":"0 100%":0),tl=eM.pinnedContainer=e.pinnedContainer&&_(e.pinnedContainer,eM),th=tv&&Math.max(0,ey.indexOf(eM))||0,tc=th;for(eS&&t$(o)&&(Y=z.getProperty(f,t0.p),U=z.getProperty(m,t0.p));tc-- >0;)(C=ey[tc]).end||C.refresh(0,1)||(te=eM),(O=C.pin)&&(O===tv||O===ty||O===tl)&&!C.isReverted&&(N||(N=[]),N.unshift(C),C.revert(!0,!0)),C!==ey[tc]&&(th--,tc--);for(tq(ta)&&(ta=ta(eM)),h=e$(ta=tD(ta,"start",eM),tv,$,t0,eB(),d,f,eM,K,eE,ev,Q,tW,eM._startClamp&&"_startClamp")||(ty?-.001:0),tq(tr)&&(tr=tr(eM)),tG(tr)&&!tr.indexOf("+=")&&(~tr.indexOf(" ")?tr=(tG(ta)?ta.split(" ")[0]:"")+tr:(J=em(tr.substr(2),$),tr=tG(ta)?ta:(tW?z.utils.mapRange(0,tW.duration(),tW.scrollTrigger.start,tW.scrollTrigger.end,h):h)+J,to=tv)),tr=tD(tr,"end",eM),c=Math.max(h,e$(tr||(to?"100% 0":Q),to,$,t0,eB()+J,p,m,eM,K,eE,ev,Q,tW,eM._endClamp&&"_endClamp"))||-.001,J=0,tc=th;tc--;)(O=(C=ey[tc]).pin)&&C.start-C._pinPush<=h&&!tW&&C.end>0&&(g=C.end-(eM._startClamp?Math.max(0,C.start):C.start),(O===tv&&C.start-C._pinPush<h||O===tl)&&isNaN(ta)&&(J+=g*(1-C.progress)),O===ty&&(ti+=g));if(h+=J,c+=J,eM._startClamp&&(eM._startClamp+=J),eM._endClamp&&!tS&&(eM._endClamp=c||-.001,c=Math.min(c,tX(el,t0))),y=c-h||(h-=.01)&&.001,Z&&(e_=z.utils.clamp(0,1,z.utils.normalize(h,c,tn))),eM._pinPush=ti,d&&J&&((g={})[t0.a]="+="+J,tl&&(g[t0.p]="-="+eB()),z.set([d,p],g)),ty&&!(tw&&eM.end>=tX(el,t0)))g=ee(ty),S=t0===R,b=eB(),k=parseFloat(M(t0.a))+ti,!Q&&c>1&&(W={style:W=(ef?G.scrollingElement||q:el).style,value:W["overflow"+t0.a.toUpperCase()]},ef&&"scroll"!==ee(H)["overflow"+t0.a.toUpperCase()]&&(W.style["overflow"+t0.a.toUpperCase()]="scroll")),ez(ty,E,g),P=eG(ty),x=er(ty,!0),F=ev&&L(el,S?D:R)(),tx?((B=[tx+t0.os2,y+ti+"px"]).t=E,(tc=tx===t4?es(ty,t0)+y+ti:0)&&(B.push(t0.d,tc+"px"),"auto"!==E.style.flexBasis&&(E.style.flexBasis=tc+"px")),eU(B),tl&&ey.forEach(function(t){t.pin===tl&&!1!==t.vars.pinSpacing&&(t._subPinOffset=!0)}),ev&&eB(tn)):(tc=es(ty,t0))&&"auto"!==E.style.flexBasis&&(E.style.flexBasis=tc+"px"),ev&&((A={top:x.top+(S?b-h:F)+"px",left:x.left+(S?F:b-h)+"px",boxSizing:"border-box",position:"fixed"})[t2]=A["max"+t6]=Math.ceil(x.width)+"px",A[t5]=A["max"+et]=Math.ceil(x.height)+"px",A[t7]=A[t7+"Top"]=A[t7+t3]=A[t7+t9]=A[t7+t8]="0",A[t4]=g[t4],A[t4+"Top"]=g[t4+"Top"],A[t4+t3]=g[t4+t3],A[t4+t9]=g[t4+t9],A[t4+t8]=g[t4+t8],T=eq(w,A,tj),tS&&eB(0)),i?(I=i._initted,tu(1),i.render(i.duration(),!0,!0),V=M(t0.a)-k+y+ti,j=Math.abs(y-V)>1,ev&&j&&T.splice(T.length-2,2),i.render(0,!0,!0),I||i.invalidate(!0),i.parent||i.totalTime(i.totalTime()),tu(0)):V=y,W&&(W.value?W.style["overflow"+t0.a.toUpperCase()]=W.value:W.style.removeProperty("overflow-"+t0.a));else if(tv&&eB()&&!tW)for(x=tv.parentNode;x&&x!==H;)x._pinOffset&&(h-=x._pinOffset,c-=x._pinOffset),x=x.parentNode;N&&N.forEach(function(t){return t.revert(!1,!0)}),eM.start=h,eM.end=c,l=u=tS?tn:eB(),tW||tS||(l<tn&&eB(tn),eM.scroll.rec=0),eM.revert(!1,!0),eD=tC(),tt&&(eO=-1,tt.restart(!0)),te=0,i&&t1&&(i._initted||ts)&&i.progress()!==ts&&i.progress(ts||0,!0).render(i.time(),!0,!0),(Z||e_!==eM.progress||tW||tb||i&&!i._initted)&&(i&&!t1&&(i._initted||e_||!1!==i.vars.immediateRender)&&i.totalProgress(tW&&h<-.001&&!e_?z.utils.normalize(h,c,0):e_,!0),eM.progress=Z||(l-h)/y===e_?0:e_),ty&&tx&&(E._pinOffset=Math.round(eM.progress*V)),X&&X.invalidate(),isNaN(Y)||(Y-=z.getProperty(f,t0.p),U-=z.getProperty(m,t0.p),eJ(f,t0,Y),eJ(d,t0,Y-(a||0)),eJ(m,t0,U),eJ(p,t0,U-(a||0))),Z&&!tS&&eM.update(),!tm||tS||v||(v=!0,tm(eM),v=!1)}},eM.getVelocity=function(){return(eB()-u)/(tC()-J)*1e3||0},eM.endAnimation=function(){tK(eM.callbackAnimation),i&&(X?X.progress(1):i.paused()?t1||tK(i,eM.direction<0,1):tK(i,i.reversed()))},eM.labelToScroll=function(t){return i&&i.labels&&(h||eM.refresh()||h)+i.labels[t]/i.duration()*y||0},eM.getTrailing=function(t){var e=ey.indexOf(eM),i=eM.direction>0?ey.slice(0,e).reverse():ey.slice(e+1);return(tG(t)?i.filter(function(e){return e.vars.preventOverlaps===t}):i).filter(function(t){return eM.direction>0?t.end<=h:t.start>=c})},eM.update=function(t,e,n){if(!tW||n||t){var r,o,a,d,p,m,g,v=!0===tS?tn:eM.scroll(),x=t?0:(v-h)/y,b=x<0?0:x>1?1:x||0,w=eM.progress;if(e&&(u=l,l=tW?eB():v,tB&&(Y=W,W=i&&!t1?i.totalProgress():b)),tP&&ty&&!te&&!tM&&tV&&(!b&&h<v+(v-u)/(tC()-J)*tP?b=1e-4:1===b&&c>v+(v-u)/(tC()-J)*tP&&(b=.9999)),b!==w&&eM.enabled){if(d=(p=(r=eM.isActive=!!b&&b<1)!=(!!w&&w<1))||!!b!=!!w,eM.direction=b>w?1:-1,eM.progress=b,d&&!te&&(o=b&&!w?0:1===b?1:1===w?2:3,t1&&(a=!p&&"none"!==eT[o+1]&&eT[o+1]||eT[o],g=i&&("complete"===a||"reset"===a||a in i))),tJ&&(p||g)&&(g||tg||!i)&&(tq(tJ)?tJ(eM):eM.getTrailing(tJ).forEach(function(t){return t.endAnimation()})),!t1&&(!X||te||tM?i&&i.totalProgress(b,!!(te&&(eD||t))):(X._dp._time-X._start!==X._time&&X.render(X._dp._time-X._start),X.resetTo?X.resetTo("totalProgress",b,i._tTime/i._tDur):(X.vars.totalProgress=b,X.invalidate().restart()))),ty)if(t&&tx&&(E.style[tx+t0.os2]=O),ev){if(d){if(m=!t&&b>w&&c+1>v&&v+1>=tX(el,t0),tj)if(!t&&(r||m)){var S=er(ty,!0),A=v-h;eQ(ty,H,S.top+(t0===R?A:0)+"px",S.left+(t0===R?0:A)+"px")}else eQ(ty,E);eU(r||m?T:P),j&&b<1&&r||C(k+(1!==b||m?0:V))}}else C(tF(k+V*b));!tB||s.tween||te||tM||tt.restart(!0),td&&(p||t_&&b&&(b<1||!tT))&&Q(td.targets).forEach(function(t){return t.classList[r||t_?"add":"remove"](td.className)}),!tc||t1||t||tc(eM),d&&!te?(t1&&(g&&("complete"===a?i.pause().totalProgress(1):"reset"===a?i.restart(!0).pause():"restart"===a?i.restart(!0):i[a]()),tc&&tc(eM)),(p||!tT)&&(tf&&p&&tQ(eM,tf),eb[o]&&tQ(eM,eb[o]),t_&&(1===b?eM.kill(!1,1):eb[o]=0),!p&&eb[o=1===b?1:3]&&tQ(eM,eb[o])),tU&&!r&&Math.abs(eM.getVelocity())>(tH(tU)?tU:2500)&&(tK(eM.callbackAnimation),X?X.progress(1):tK(i,"reverse"===a?1:!b,1))):t1&&tc&&!te&&tc(eM)}if(I){var M=tW?v/tW.duration()*(tW._caScrollDist||0):v;F(M+ +!!f._isFlipped),I(M)}to&&to(-v/tW.duration()*(tW._caScrollDist||0))}},eM.enable=function(e,i){eM.enabled||(eM.enabled=!0,eu(el,"resize",eP),ef||eu(el,"scroll",ew),eC&&eu(t,"refreshInit",eC),!1!==e&&(eM.progress=e_=0,l=u=eO=eB()),!1!==i&&eM.refresh())},eM.getTween=function(t){return t&&s?s.tween:X},eM.setPositions=function(t,e,i,n){if(tW){var r=tW.scrollTrigger,s=tW.duration(),o=r.end-r.start;t=r.start+o*t/s,e=r.start+o*e/s}eM.refresh(!1,!1,{start:tR(t,i&&!!eM._startClamp),end:tR(e,i&&!!eM._endClamp)},n),eM.update()},eM.adjustPinSpacing=function(t){if(B&&t){var e=B.indexOf(t0.d)+1;B[e]=parseFloat(B[e])+t+"px",B[1]=parseFloat(B[1])+t+"px",eU(B)}},eM.disable=function(e,i){if(eM.enabled&&(!1!==e&&eM.revert(!0,!0),eM.enabled=eM.isActive=!1,i||X&&X.pause(),tn=0,o&&(o.uncache=1),eC&&eh(t,"refreshInit",eC),tt&&(tt.pause(),s.tween&&s.tween.kill()&&(s.tween=0)),!ef)){for(var n=ey.length;n--;)if(ey[n].scroller===el&&ey[n]!==eM)return;eh(el,"resize",eP),ef||eh(el,"scroll",ew)}},eM.kill=function(t,n){eM.disable(t,n),X&&!n&&X.kill(),tp&&delete ex[tp];var r=ey.indexOf(eM);r>=0&&ey.splice(r,1),r===tr&&ej>0&&tr--,r=0,ey.forEach(function(t){return t.scroller===eM.scroller&&(r=1)}),r||tS||(eM.scroll.rec=0),i&&(i.scrollTrigger=null,t&&i.revert({kill:!1}),n||i.kill()),d&&[d,p,f,m].forEach(function(t){return t.parentNode&&t.parentNode.removeChild(t)}),tA===eM&&(tA=0),ty&&(o&&(o.uncache=1),r=0,ey.forEach(function(t){return t.pin===ty&&r++}),r||(o.spacer=0)),e.onKill&&e.onKill(eM)},ey.push(eM),eM.enable(!1,!1),ta&&ta(eM),i&&i.add&&!y){var eN=eM.update;eM.update=function(){eM.update=eN,x.cache++,h||c||eM.refresh()},z.delayedCall(.01,eM.update),y=.01,h=c=0}else eM.refresh();ty&&eR()},t.register=function(e){return X||(z=e||tI(),tj()&&window.document&&t.enable(),X=tO),X},t.defaults=function(t){if(t)for(var e in t)ep[e]=t[e];return ep},t.disable=function(t,e){tO=0,ey.forEach(function(i){return i[e?"kill":"disable"](t)}),eh(U,"wheel",ew),eh(G,"scroll",ew),clearInterval(tt),eh(G,"touchcancel",tL),eh(H,"touchstart",tL),el(eh,G,"pointerdown,touchstart,mousedown",t_),el(eh,G,"pointerup,touchend,mouseup",tB),K.kill(),tU(eh);for(var i=0;i<x.length;i+=3)ec(eh,x[i],x[i+1]),ec(eh,x[i],x[i+2])},t.enable=function(){if(U=window,q=(G=document).documentElement,H=G.body,z&&(Q=z.utils.toArray,Z=z.utils.clamp,tg=z.core.context||tL,tu=z.core.suppressOverwrites||tL,tv=U.history.scrollRestoration||"auto",eF=U.pageYOffset||0,z.core.globals("ScrollTrigger",t),H)){tO=1,(ty=document.createElement("div")).style.height="100vh",ty.style.position="absolute",e_(),function t(){return tO&&requestAnimationFrame(t)}(),Y.register(z),t.isTouch=Y.isTouch,tm=Y.isTouch&&/(iPad|iPhone|iPod|Mac)/g.test(navigator.userAgent),td=1===Y.isTouch,eu(U,"wheel",ew),$=[U,G,q,H],z.matchMedia?(t.matchMedia=function(t){var e,i=z.matchMedia();for(e in t)i.add(e,t[e]);return i},z.addEventListener("matchMediaInit",function(){return eV()}),z.addEventListener("matchMediaRevert",function(){return ek()}),z.addEventListener("matchMedia",function(){eL(0,1),eM("matchMedia")}),z.matchMedia().add("(orientation: portrait)",function(){return eT(),eT})):console.warn("Requires GSAP 3.11.0 or later"),eT(),eu(G,"scroll",ew);var e,i,n=H.hasAttribute("style"),r=H.style,s=r.borderTopStyle,o=z.core.Animation.prototype;for(o.revert||Object.defineProperty(o,"revert",{value:function(){return this.time(-.01,!0)}}),r.borderTopStyle="solid",R.m=Math.round((e=er(H)).top+R.sc())||0,D.m=Math.round(e.left+D.sc())||0,s?r.borderTopStyle=s:r.removeProperty("border-top-style"),n||(H.setAttribute("style",""),H.removeAttribute("style")),tt=setInterval(eb,250),z.delayedCall(.5,function(){return tM=0}),eu(G,"touchcancel",tL),eu(H,"touchstart",tL),el(eu,G,"pointerdown,touchstart,mousedown",t_),el(eu,G,"pointerup,touchend,mouseup",tB),tn=z.utils.checkPrefix("transform"),eW.push(tn),X=tC(),K=z.delayedCall(.2,eL).pause(),ta=[G,"visibilitychange",function(){var t=U.innerWidth,e=U.innerHeight;G.hidden?(ts=t,to=e):(ts!==t||to!==e)&&eP()},G,"DOMContentLoaded",eL,U,"load",eL,U,"resize",eP],tU(eu),ey.forEach(function(t){return t.enable(0,1)}),i=0;i<x.length;i+=3)ec(eh,x[i],x[i+1]),ec(eh,x[i],x[i+2])}},t.config=function(e){"limitCallbacks"in e&&(tT=!!e.limitCallbacks);var i=e.syncInterval;i&&clearInterval(tt)||(tt=i)&&setInterval(eb,i),"ignoreMobileResize"in e&&(td=1===t.isTouch&&e.ignoreMobileResize),"autoRefreshEvents"in e&&(tU(eh)||tU(eu,e.autoRefreshEvents||"none"),th=-1===(e.autoRefreshEvents+"").indexOf("resize"))},t.scrollerProxy=function(t,e){var i=_(t),n=x.indexOf(i),r=tN(i);~n&&x.splice(n,r?6:2),e&&(r?b.unshift(U,e,H,e,q,e):b.unshift(i,e))},t.clearMatchMedia=function(t){ey.forEach(function(e){return e._ctx&&e._ctx.query===t&&e._ctx.kill(!0,!0)})},t.isInViewport=function(t,e,i){var n=(tG(t)?_(t):t).getBoundingClientRect(),r=n[i?t2:t5]*e||0;return i?n.right-r>0&&n.left+r<U.innerWidth:n.bottom-r>0&&n.top+r<U.innerHeight},t.positionInViewport=function(t,e,i){tG(t)&&(t=_(t));var n=t.getBoundingClientRect(),r=n[i?t2:t5],s=null==e?r/2:e in ef?ef[e]*r:~e.indexOf("%")?parseFloat(e)*r/100:parseFloat(e)||0;return i?(n.left+s)/U.innerWidth:(n.top+s)/U.innerHeight},t.killAll=function(t){if(ey.slice(0).forEach(function(t){return"ScrollSmoother"!==t.vars.id&&t.kill()}),!0!==t){var e=eS.killAll||[];eS={},e.forEach(function(t){return t()})}},t}();e1.version="3.13.0",e1.saveStyles=function(t){return t?Q(t).forEach(function(t){if(t&&t.style){var e=eC.indexOf(t);e>=0&&eC.splice(e,5),eC.push(t,t.style.cssText,t.getBBox&&t.getAttribute("transform"),z.core.getCache(t),tg())}}):eC},e1.revert=function(t,e){return eV(!t,e)},e1.create=function(t,e){return new e1(t,e)},e1.refresh=function(t){return t?eP(!0):(X||e1.register())&&eL(!0)},e1.update=function(t){return++x.cache&&eI(2*(!0===t))},e1.clearScrollMemory=eO,e1.maxScroll=function(t,e){return tX(t,e?D:R)},e1.getScrollFunc=function(t,e){return L(_(t),e?D:R)},e1.getById=function(t){return ex[t]},e1.getAll=function(){return ey.filter(function(t){return"ScrollSmoother"!==t.vars.id})},e1.isScrolling=function(){return!!tV},e1.snapDirectional=ea,e1.addEventListener=function(t,e){var i=eS[t]||(eS[t]=[]);~i.indexOf(e)||i.push(e)},e1.removeEventListener=function(t,e){var i=eS[t],n=i&&i.indexOf(e);n>=0&&i.splice(n,1)},e1.batch=function(t,e){var i,n=[],r={},s=e.interval||.016,o=e.batchMax||1e9,a=function(t,e){var i=[],n=[],r=z.delayedCall(s,function(){e(i,n),i=[],n=[]}).pause();return function(t){i.length||r.restart(!0),i.push(t.trigger),n.push(t),o<=i.length&&r.progress(1)}};for(i in e)r[i]="on"===i.substr(0,2)&&tq(e[i])&&"onRefreshInit"!==i?a(i,e[i]):e[i];return tq(o)&&(o=o(),eu(e1,"refresh",function(){return o=e.batchMax()})),Q(t).forEach(function(t){var e={};for(i in r)e[i]=r[i];e.trigger=t,n.push(e1.create(e))}),n};var e2,e5=function(t,e,i,n){return e>n?t(n):e<0&&t(0),i>n?(n-e)/(i-e):i<0?e/(e-i):1},e3=function t(e,i){!0===i?e.style.removeProperty("touch-action"):e.style.touchAction=!0===i?"auto":i?"pan-"+i+(Y.isTouch?" pinch-zoom":""):"none",e===q&&t(H,i)},e8={auto:1,scroll:1},e9=function(t){var e,i=t.event,n=t.target,r=t.axis,s=(i.changedTouches?i.changedTouches[0]:i).target,o=s._gsap||z.core.getCache(s),a=tC();if(!o._isScrollT||a-o._isScrollT>2e3){for(;s&&s!==H&&(s.scrollHeight<=s.clientHeight&&s.scrollWidth<=s.clientWidth||!(e8[(e=ee(s)).overflowY]||e8[e.overflowX]));)s=s.parentNode;o._isScroll=s&&s!==n&&!tN(s)&&(e8[(e=ee(s)).overflowY]||e8[e.overflowX]),o._isScrollT=a}(o._isScroll||"x"===r)&&(i.stopPropagation(),i._gsapAllow=!0)},e4=function(t,e,i,n){return Y.create({target:t,capture:!0,debounce:!1,lockAxis:!0,type:e,onWheel:n=n&&e9,onPress:n,onDrag:n,onScroll:n,onEnable:function(){return i&&eu(G,Y.eventTypes[0],e6,!1,!0)},onDisable:function(){return eh(G,Y.eventTypes[0],e6,!0)}})},e7=/(input|label|select|textarea)/i,e6=function(t){var e=e7.test(t.target.tagName);(e||e2)&&(t._gsapAllow=!0,e2=e)},it=function(t){t$(t)||(t={}),t.preventDefault=t.isNormalizer=t.allowClicks=!0,t.type||(t.type="wheel,touch"),t.debounce=!!t.debounce,t.id=t.id||"normalizer";var e,i,n,r,s,o,a,l,u=t,h=u.normalizeScrollX,c=u.momentum,d=u.allowNestedScroll,p=u.onRelease,f=_(t.target)||q,m=z.core.globals().ScrollSmoother,g=m&&m.get(),v=tm&&(t.content&&_(t.content)||g&&!1!==t.content&&!g.smooth()&&g.content()),y=L(f,R),b=L(f,D),w=1,T=(Y.isTouch&&U.visualViewport?U.visualViewport.scale*U.visualViewport.width:U.outerWidth)/U.innerWidth,P=0,S=tq(c)?function(){return c(e)}:function(){return c||2.8},E=e4(f,t.type,!0,d),A=function(){return r=!1},M=tL,C=tL,k=function(){i=tX(f,R),C=Z(+!!tm,i),h&&(M=Z(0,tX(f,D))),n=eD},V=function(){v._gsap.y=tF(parseFloat(v._gsap.y)+y.offset)+"px",v.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+parseFloat(v._gsap.y)+", 0, 1)",y.offset=y.cacheID=0},O=function(){if(r){requestAnimationFrame(A);var t=tF(e.deltaY/2),i=C(y.v-t);if(v&&i!==y.v+y.offset){y.offset=i-y.v;var n=tF((parseFloat(v&&v._gsap.y)||0)-y.offset);v.style.transform="matrix3d(1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, "+n+", 0, 1)",v._gsap.y=n+"px",y.cacheID=x.cache,eI()}return!0}y.offset&&V(),r=!0},B=function(){k(),s.isActive()&&s.vars.scrollY>i&&(y()>i?s.progress(1)&&y(i):s.resetTo("scrollY",i))};return v&&z.set(v,{y:"+=0"}),t.ignoreCheck=function(t){return tm&&"touchmove"===t.type&&O(t)||w>1.05&&"touchstart"!==t.type||e.isGesturing||t.touches&&t.touches.length>1},t.onPress=function(){r=!1;var t=w;w=tF((U.visualViewport&&U.visualViewport.scale||1)/T),s.pause(),t!==w&&e3(f,w>1.01||!h&&"x"),o=b(),a=y(),k(),n=eD},t.onRelease=t.onGestureStart=function(t,e){if(y.offset&&V(),e){x.cache++;var n,r,o=S();h&&(r=(n=b())+-(.05*o*t.velocityX)/.227,o*=e5(b,n,r,tX(f,D)),s.vars.scrollX=M(r)),r=(n=y())+-(.05*o*t.velocityY)/.227,o*=e5(y,n,r,tX(f,R)),s.vars.scrollY=C(r),s.invalidate().duration(o).play(.01),(tm&&s.vars.scrollY>=i||n>=i-1)&&z.to({},{onUpdate:B,duration:o})}else l.restart(!0);p&&p(t)},t.onWheel=function(){s._ts&&s.pause(),tC()-P>1e3&&(n=0,P=tC())},t.onChange=function(t,e,i,r,s){if(eD!==n&&k(),e&&h&&b(M(r[2]===e?o+(t.startX-t.x):b()+e-r[1])),i){y.offset&&V();var l=s[2]===i,u=l?a+t.startY-t.y:y()+i-s[1],c=C(u);l&&u!==c&&(a+=c-u),y(c)}(i||e)&&eI()},t.onEnable=function(){e3(f,!h&&"x"),e1.addEventListener("refresh",B),eu(U,"resize",B),y.smooth&&(y.target.style.scrollBehavior="auto",y.smooth=b.smooth=!1),E.enable()},t.onDisable=function(){e3(f,!0),eh(U,"resize",B),e1.removeEventListener("refresh",B),E.kill()},t.lockAxis=!1!==t.lockAxis,(e=new Y(t)).iOS=tm,tm&&!y()&&y(1),tm&&z.ticker.add(tL),l=e._dc,s=z.to(e,{ease:"power4",paused:!0,inherit:!1,scrollX:h?"+=0.1":"+=0",scrollY:"+=0.1",modifiers:{scrollY:eZ(y,y(),function(){return s.pause()})},onUpdate:eI,onComplete:l.vars.onComplete}),e};e1.sort=function(t){if(tq(t))return ey.sort(t);var e=U.pageYOffset||0;return e1.getAll().forEach(function(t){return t._sortY=t.trigger?e+t.trigger.getBoundingClientRect().top:t.start+U.innerHeight}),ey.sort(t||function(t,e){return -1e6*(t.vars.refreshPriority||0)+(t.vars.containerAnimation?1e6:t._sortY)-((e.vars.containerAnimation?1e6:e._sortY)+-1e6*(e.vars.refreshPriority||0))})},e1.observe=function(t){return new Y(t)},e1.normalizeScroll=function(t){if(void 0===t)return tc;if(!0===t&&tc)return tc.enable();if(!1===t){tc&&tc.kill(),tc=t;return}var e=t instanceof Y?t:it(t);return tc&&tc.target===e.target&&tc.kill(),tN(e.target)&&(tc=e),e},e1.core={_getVelocityProp:F,_inputObserver:e4,_scrollers:x,_proxies:b,bridge:{ss:function(){tV||eM("scrollStart"),tV=tC()},ref:function(){return te}}},tI()&&z.registerPlugin(e1)},9364:(t,e,i)=>{i.d(e,{G:()=>n});function n(t){return"object"==typeof t&&null!==t}},9515:(t,e,i)=>{i.d(e,{Gt:()=>r,PP:()=>a,WG:()=>s,uv:()=>o});var n=i(9827);let{schedule:r,cancel:s,state:o,steps:a}=(0,i(8437).I)("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:n.l,!0)},9782:(t,e,i)=>{i.d(e,{x:()=>r});var n=i(9364);function r(t){return(0,n.G)(t)&&"ownerSVGElement"in t}},9827:(t,e,i)=>{i.d(e,{l:()=>n});let n=t=>t}}]);