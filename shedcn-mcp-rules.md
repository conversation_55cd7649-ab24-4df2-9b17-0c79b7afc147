# MCP Server Tool Instructions

This document outlines the available tools and their usage for interacting with the shadcn/ui component library through the MCP protocol.

## Available Tools

### Component Management
- `get_component`: Retrieves source code for a specific component
- `get_component_demo`: Fetches example usage code for a component
- `list_components`: Returns a list of all available components
- `get_component_metadata`: Provides dependency information and metadata

### Block Management
- `get_block`: Retrieves complete block implementations
- `list_blocks`: Returns available blocks with their categories

### Repository Navigation
- `get_directory_structure`: Returns the shadcn/ui repository structure

## Usage Examples

All commands should be sent as JSON objects with the following structure:

```json
{
  "tool": "<tool_name>",
  "arguments": {
    // tool-specific arguments
  }
}
```

### Example Commands:

1. Get a component's source:
```json
{
  "tool": "get_component",
  "arguments": {
    "componentName": "button"
  }
}
```

2. List all components:
```json
{
  "tool": "list_components",
  "arguments": {}
}
```

3. Get a dashboard block:
```json
{
  "tool": "get_block",
  "arguments": {
    "blockName": "dashboard-01"
  }
}
```
